"""
Supabase setup and configuration for the multi-agent sales support system.
"""

import asyncio
import logging
from typing import Optional, Dict, Any, List
from supabase import create_client, Client
from langchain_community.vectorstores import SupabaseVectorStore
from langchain_openai import OpenAIEmbeddings
from config.settings import get_settings
from database.schemas import SUPABASE_SCHEMA_SQL

logger = logging.getLogger(__name__)

# Global Supabase client
_supabase_client: Optional[Client] = None
_vector_store: Optional[SupabaseVectorStore] = None


class SupabaseManager:
    """Manages Supabase connections and operations."""
    
    def __init__(self):
        """Initialize the Supabase manager."""
        self.settings = get_settings()
        self.client: Optional[Client] = None
        self.vector_store: Optional[SupabaseVectorStore] = None
        
    async def initialize(self) -> None:
        """Initialize Supabase client and vector store."""
        try:
            # Create Supabase client
            self.client = create_client(
                self.settings.supabase_url,
                self.settings.supabase_service_key
            )
            
            # Test connection
            response = self.client.table("customer_interactions").select("count", count="exact").execute()
            logger.info("Supabase connection established successfully")
            
            # Initialize vector store
            await self._initialize_vector_store()
            
            global _supabase_client, _vector_store
            _supabase_client = self.client
            _vector_store = self.vector_store
            
        except Exception as e:
            logger.error(f"Failed to initialize Supabase: {e}")
            raise
    
    async def _initialize_vector_store(self) -> None:
        """Initialize the vector store for embeddings."""
        try:
            # Use OpenAI embeddings (fallback if Mistral embeddings not available)
            embeddings = OpenAIEmbeddings(
                api_key=self.settings.openai_api_key,
                model=self.settings.openai_embedding_model
            ) if self.settings.openai_api_key else None
            
            if embeddings:
                self.vector_store = SupabaseVectorStore(
                    client=self.client,
                    embedding=embeddings,
                    table_name="customer_interactions",
                    query_name="match_interactions"
                )
                logger.info("Vector store initialized successfully")
            else:
                logger.warning("No embedding API key provided, vector search will be disabled")
                
        except Exception as e:
            logger.error(f"Failed to initialize vector store: {e}")
            # Don't raise here as the system can work without vector search
    
    async def setup_database_schema(self) -> None:
        """Set up the database schema."""
        try:
            # Execute schema SQL
            # Note: Supabase Python client doesn't support raw SQL execution
            # This would typically be done through the Supabase dashboard or CLI
            logger.info("Database schema setup should be done through Supabase dashboard")
            logger.info("Please execute the SQL from schemas.py in your Supabase SQL editor")
            
        except Exception as e:
            logger.error(f"Failed to setup database schema: {e}")
            raise
    
    def get_client(self) -> Client:
        """Get the Supabase client."""
        if not self.client:
            raise RuntimeError("Supabase client not initialized")
        return self.client
    
    def get_vector_store(self) -> Optional[SupabaseVectorStore]:
        """Get the vector store."""
        return self.vector_store
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform a health check on Supabase connections."""
        health_status = {
            "supabase_connected": False,
            "vector_store_available": False,
            "tables_accessible": False,
            "error": None
        }
        
        try:
            if self.client:
                # Test basic connection
                response = self.client.table("customer_interactions").select("count", count="exact").execute()
                health_status["supabase_connected"] = True
                health_status["tables_accessible"] = True
                
                # Test vector store
                if self.vector_store:
                    health_status["vector_store_available"] = True
                    
        except Exception as e:
            health_status["error"] = str(e)
            logger.error(f"Supabase health check failed: {e}")
        
        return health_status


# Global manager instance
_supabase_manager: Optional[SupabaseManager] = None


async def initialize_supabase() -> SupabaseManager:
    """Initialize the global Supabase manager."""
    global _supabase_manager
    
    if _supabase_manager is None:
        _supabase_manager = SupabaseManager()
        await _supabase_manager.initialize()
    
    return _supabase_manager


def get_supabase_client() -> Client:
    """Get the global Supabase client."""
    global _supabase_client
    
    if _supabase_client is None:
        raise RuntimeError("Supabase not initialized. Call initialize_supabase() first.")
    
    return _supabase_client


def get_vector_store() -> Optional[SupabaseVectorStore]:
    """Get the global vector store."""
    global _vector_store
    return _vector_store


async def setup_database_schema() -> None:
    """Set up the database schema."""
    global _supabase_manager
    
    if _supabase_manager is None:
        raise RuntimeError("Supabase not initialized. Call initialize_supabase() first.")
    
    await _supabase_manager.setup_database_schema()


async def health_check() -> Dict[str, Any]:
    """Perform a health check on Supabase."""
    global _supabase_manager
    
    if _supabase_manager is None:
        return {
            "supabase_connected": False,
            "vector_store_available": False,
            "tables_accessible": False,
            "error": "Supabase not initialized"
        }
    
    return await _supabase_manager.health_check()


# Utility functions for common operations
async def store_interaction(
    customer_email: str,
    interaction_type: str,
    content: str,
    metadata: Optional[Dict[str, Any]] = None,
    embedding: Optional[List[float]] = None
) -> Dict[str, Any]:
    """Store a customer interaction."""
    client = get_supabase_client()
    
    interaction_data = {
        "customer_email": customer_email,
        "interaction_type": interaction_type,
        "content": content,
        "metadata": metadata,
        "embedding": embedding
    }
    
    response = client.table("customer_interactions").insert(interaction_data).execute()
    return response.data[0] if response.data else {}


async def get_customer_interactions(
    customer_email: str,
    limit: int = 10,
    interaction_type: Optional[str] = None
) -> List[Dict[str, Any]]:
    """Get customer interactions."""
    client = get_supabase_client()
    
    query = client.table("customer_interactions").select("*").eq("customer_email", customer_email)
    
    if interaction_type:
        query = query.eq("interaction_type", interaction_type)
    
    query = query.order("created_at", desc=True).limit(limit)
    
    response = query.execute()
    return response.data


async def store_sku_mapping(
    customer_email: str,
    customer_sku: str,
    internal_sku: str,
    description: Optional[str] = None
) -> Dict[str, Any]:
    """Store a SKU mapping."""
    client = get_supabase_client()
    
    mapping_data = {
        "customer_email": customer_email,
        "customer_sku": customer_sku,
        "internal_sku": internal_sku,
        "description": description
    }
    
    response = client.table("sku_mappings").upsert(mapping_data).execute()
    return response.data[0] if response.data else {}


async def get_sku_mapping(customer_email: str, customer_sku: str) -> Optional[Dict[str, Any]]:
    """Get SKU mapping for a customer."""
    client = get_supabase_client()
    
    response = client.table("sku_mappings").select("*").eq("customer_email", customer_email).eq("customer_sku", customer_sku).eq("active", True).execute()
    
    return response.data[0] if response.data else None


async def search_interactions(
    query_text: str,
    customer_email: Optional[str] = None,
    limit: int = 5,
    similarity_threshold: float = 0.8
) -> List[Dict[str, Any]]:
    """Search interactions using vector similarity."""
    vector_store = get_vector_store()
    
    if not vector_store:
        logger.warning("Vector store not available, falling back to text search")
        return await _text_search_interactions(query_text, customer_email, limit)
    
    try:
        # Perform vector similarity search
        filter_dict = {"customer_email": customer_email} if customer_email else None
        
        results = vector_store.similarity_search_with_score(
            query_text,
            k=limit,
            filter=filter_dict
        )
        
        # Filter by similarity threshold
        filtered_results = [
            {
                "content": doc.page_content,
                "metadata": doc.metadata,
                "similarity": score
            }
            for doc, score in results
            if score >= similarity_threshold
        ]
        
        return filtered_results
        
    except Exception as e:
        logger.error(f"Vector search failed: {e}")
        return await _text_search_interactions(query_text, customer_email, limit)


async def _text_search_interactions(
    query_text: str,
    customer_email: Optional[str] = None,
    limit: int = 5
) -> List[Dict[str, Any]]:
    """Fallback text search for interactions."""
    client = get_supabase_client()
    
    query = client.table("customer_interactions").select("*").ilike("content", f"%{query_text}%")
    
    if customer_email:
        query = query.eq("customer_email", customer_email)
    
    query = query.order("created_at", desc=True).limit(limit)
    
    response = query.execute()
    return response.data


async def store_agent_memory(
    agent_name: str,
    context_type: str,
    context_data: Dict[str, Any],
    customer_email: Optional[str] = None,
    session_id: Optional[str] = None,
    embedding: Optional[List[float]] = None,
    expires_at: Optional[str] = None
) -> Dict[str, Any]:
    """Store agent memory."""
    client = get_supabase_client()
    
    memory_data = {
        "agent_name": agent_name,
        "context_type": context_type,
        "context_data": context_data,
        "customer_email": customer_email,
        "session_id": session_id,
        "embedding": embedding,
        "expires_at": expires_at
    }
    
    response = client.table("agent_memory").insert(memory_data).execute()
    return response.data[0] if response.data else {}


async def get_agent_memory(
    agent_name: str,
    customer_email: Optional[str] = None,
    session_id: Optional[str] = None,
    limit: int = 10
) -> List[Dict[str, Any]]:
    """Get agent memory."""
    client = get_supabase_client()
    
    query = client.table("agent_memory").select("*").eq("agent_name", agent_name)
    
    if customer_email:
        query = query.eq("customer_email", customer_email)
    
    if session_id:
        query = query.eq("session_id", session_id)
    
    # Filter out expired memories
    query = query.or_("expires_at.is.null,expires_at.gt.now()")
    query = query.order("created_at", desc=True).limit(limit)
    
    response = query.execute()
    return response.data
