"""
Simplified MYOB EXO Agent using Mistral AI
Handles MYOB EXO API integration for ERP operations
"""

import asyncio
import logging
import base64
import json
from datetime import datetime
from typing import Dict, Any, List, Optional

import httpx

logger = logging.getLogger(__name__)


class MyobAgent:
    """Simplified MYOB EXO Agent using Mistral AI."""
    
    def __init__(self, mistral_api_key: str, myob_api_url: str, myob_api_key: str, supabase):
        """Initialize the MYOB agent."""
        self.mistral_api_key = mistral_api_key
        self.myob_api_url = myob_api_url.rstrip('/')
        self.myob_api_key = myob_api_key
        self.supabase = supabase
        
        # MYOB authentication (basic auth)
        self.myob_username = "api_user"  # Configure as needed
        self.myob_password = "api_password"  # Configure as needed
        
        logger.info("🏢 MYOB Agent initialized")
    
    async def search_customers(self, query: str) -> List[Dict[str, Any]]:
        """Search for customers in MYOB EXO."""
        try:
            endpoint = f"/debtor/search?q={query}"
            response = await self._make_myob_request("GET", endpoint)
            
            customers = response.get("data", [])
            logger.info(f"Found {len(customers)} customers for query: {query}")
            
            return customers
            
        except Exception as e:
            logger.error(f"❌ Error searching customers: {e}")
            return []
    
    async def get_customer_details(self, customer_id: str) -> Dict[str, Any]:
        """Get detailed customer information."""
        try:
            endpoint = f"/debtor/{customer_id}"
            customer = await self._make_myob_request("GET", endpoint)
            
            logger.info(f"Retrieved customer details for ID: {customer_id}")
            return customer
            
        except Exception as e:
            logger.error(f"❌ Error getting customer details: {e}")
            return {}
    
    async def search_products(self, query: str) -> List[Dict[str, Any]]:
        """Search for products/stock in MYOB EXO."""
        try:
            endpoint = f"/stock/search?q={query}"
            response = await self._make_myob_request("GET", endpoint)
            
            products = response.get("data", [])
            logger.info(f"Found {len(products)} products for query: {query}")
            
            return products
            
        except Exception as e:
            logger.error(f"❌ Error searching products: {e}")
            return []
    
    async def get_stock_levels(self, sku: str) -> Dict[str, Any]:
        """Get stock levels for a specific SKU."""
        try:
            endpoint = f"/stock/{sku}"
            stock_info = await self._make_myob_request("GET", endpoint)
            
            logger.info(f"Retrieved stock levels for SKU: {sku}")
            return stock_info
            
        except Exception as e:
            logger.error(f"❌ Error getting stock levels for {sku}: {e}")
            return {}
    
    async def create_sales_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a sales order in MYOB EXO."""
        try:
            # Process order data with Mistral AI first
            processed_order = await self._process_order_with_mistral(order_data)
            
            endpoint = "/salesorder"
            result = await self._make_myob_request("POST", endpoint, data=processed_order)
            
            # Store order in Supabase
            await self._store_order_record(result)
            
            logger.info(f"Created sales order: {result.get('id', 'unknown')}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Error creating sales order: {e}")
            return {"error": str(e)}
    
    async def get_sales_orders(self, customer_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get sales orders, optionally filtered by customer."""
        try:
            endpoint = "/salesorder"
            if customer_id:
                endpoint += f"?customer_id={customer_id}"
            
            response = await self._make_myob_request("GET", endpoint)
            orders = response.get("data", [])
            
            logger.info(f"Retrieved {len(orders)} sales orders")
            return orders
            
        except Exception as e:
            logger.error(f"❌ Error getting sales orders: {e}")
            return []
    
    async def map_customer_sku(self, customer_email: str, customer_sku: str, internal_sku: str) -> bool:
        """Create or update SKU mapping."""
        try:
            # Check if mapping exists
            existing = self.supabase.table("sku_mappings").select("*").eq(
                "customer_email", customer_email
            ).eq("customer_sku", customer_sku).execute()
            
            mapping_data = {
                "customer_email": customer_email,
                "customer_sku": customer_sku,
                "internal_sku": internal_sku,
                "active": True,
                "created_at": datetime.utcnow().isoformat()
            }
            
            if existing.data:
                # Update existing
                self.supabase.table("sku_mappings").update(mapping_data).eq(
                    "id", existing.data[0]["id"]
                ).execute()
            else:
                # Create new
                self.supabase.table("sku_mappings").insert(mapping_data).execute()
            
            logger.info(f"Mapped SKU: {customer_sku} -> {internal_sku} for {customer_email}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error mapping SKU: {e}")
            return False
    
    async def get_sku_mapping(self, customer_email: str, customer_sku: str) -> Optional[str]:
        """Get internal SKU for customer SKU."""
        try:
            response = self.supabase.table("sku_mappings").select("internal_sku").eq(
                "customer_email", customer_email
            ).eq("customer_sku", customer_sku).eq("active", True).execute()
            
            if response.data:
                return response.data[0]["internal_sku"]
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Error getting SKU mapping: {e}")
            return None
    
    async def _make_myob_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Make authenticated request to MYOB EXO API."""
        try:
            # Prepare authentication
            auth_string = f"{self.myob_username}:{self.myob_password}"
            encoded_auth = base64.b64encode(auth_string.encode()).decode()
            
            headers = {
                "Authorization": f"Basic {encoded_auth}",
                "x-myobapi-key": self.myob_api_key,
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
            
            url = f"{self.myob_api_url}{endpoint}"
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                if method.upper() == "GET":
                    response = await client.get(url, headers=headers)
                elif method.upper() == "POST":
                    response = await client.post(url, headers=headers, json=data)
                elif method.upper() == "PUT":
                    response = await client.put(url, headers=headers, json=data)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")
                
                if response.status_code >= 400:
                    raise Exception(f"MYOB API error: {response.status_code} - {response.text}")
                
                return response.json()
                
        except Exception as e:
            logger.error(f"❌ MYOB API request failed: {e}")
            raise
    
    async def _process_order_with_mistral(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process order data using Mistral AI for validation and enhancement."""
        try:
            prompt = f"""
            Process this sales order data for MYOB EXO API submission.
            Validate and enhance the order data, ensuring all required fields are present.
            
            Order Data:
            {json.dumps(order_data, indent=2)}
            
            Please:
            1. Validate customer information
            2. Validate product SKUs and quantities
            3. Calculate totals if missing
            4. Add any missing required fields for MYOB EXO
            5. Format according to MYOB EXO API requirements
            
            Return the processed order data in JSON format.
            """
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://api.mistral.ai/v1/chat/completions",
                    headers={
                        "Authorization": f"Bearer {self.mistral_api_key}",
                        "Content-Type": "application/json"
                    },
                    json={
                        "model": "mistral-large-latest",
                        "messages": [
                            {"role": "user", "content": prompt}
                        ],
                        "temperature": 0.1,  # Low temperature for consistency
                        "max_tokens": 2000
                    }
                )
                
                if response.status_code == 200:
                    result = response.json()
                    content = result['choices'][0]['message']['content']
                    
                    try:
                        processed_order = json.loads(content)
                        return processed_order
                    except json.JSONDecodeError:
                        logger.warning("Mistral response not valid JSON, using original order data")
                        return order_data
                else:
                    logger.warning(f"Mistral API error: {response.status_code}, using original order data")
                    return order_data
                    
        except Exception as e:
            logger.error(f"❌ Error processing order with Mistral: {e}")
            return order_data
    
    async def _store_order_record(self, order_result: Dict[str, Any]):
        """Store order record in Supabase."""
        try:
            order_data = {
                "order_id": order_result.get("id", ""),
                "customer_email": order_result.get("customer_email", ""),
                "status": order_result.get("status", "pending"),
                "total_amount": order_result.get("total", 0.0),
                "currency": order_result.get("currency", "AUD"),
                "metadata": order_result,
                "created_at": datetime.utcnow().isoformat()
            }
            
            self.supabase.table("orders").insert(order_data).execute()
            logger.debug(f"Stored order record: {order_result.get('id', 'unknown')}")
            
        except Exception as e:
            logger.error(f"❌ Failed to store order record: {e}")
    
    async def get_agent_stats(self) -> Dict[str, Any]:
        """Get MYOB agent statistics."""
        try:
            # Get recent API calls (would need to implement API logging)
            # For now, return basic stats
            
            return {
                "agent_type": "myob_exo_agent",
                "api_url": self.myob_api_url,
                "status": "active"
            }
            
        except Exception as e:
            logger.error(f"❌ Error getting MYOB agent stats: {e}")
            return {"error": str(e)}
