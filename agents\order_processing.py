"""
Order Processing Agent for the multi-agent sales support system.
Handles order management, status checking, and order placement workflows through MYOB EXO ERP.
"""

import asyncio
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
from enum import Enum

from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext
from pydantic_ai.models.mistral import MistralModel
from pydantic_ai.providers.mistral import MistralProvider
from pydantic_ai.settings import ModelSettings

from config.settings import get_settings
from database.operations import get_database_operations
from database.schemas import OrderRecord, OrderStatus, CustomerInteraction
from tools.myob_tools import get_sku_mapping_manager, get_myob_client

logger = logging.getLogger(__name__)


class OrderProcessingError(Exception):
    """Custom exception for order processing operations."""
    pass


class OrderAction(str, Enum):
    """Types of order actions."""
    CREATE = "create"
    UPDATE = "update"
    CANCEL = "cancel"
    STATUS_CHECK = "status_check"
    MODIFY = "modify"


class OrderRequest(BaseModel):
    """Model for order processing requests."""
    customer_email: str = Field(..., description="Customer's email address")
    action: OrderAction = Field(..., description="Order action to perform")
    order_data: Optional[Dict[str, Any]] = Field(default=None, description="Order data for creation/updates")
    order_id: Optional[str] = Field(default=None, description="Existing order ID for updates/status checks")
    items: List[Dict[str, Any]] = Field(default_factory=list, description="Order items")
    notes: Optional[str] = Field(default=None, description="Additional notes")
    priority: str = Field(default="normal", description="Order priority")


class OrderResponse(BaseModel):
    """Model for order processing responses."""
    success: bool = Field(..., description="Whether the operation was successful")
    order_id: Optional[str] = Field(default=None, description="Order ID")
    status: Optional[str] = Field(default=None, description="Order status")
    message: str = Field(..., description="Response message")
    order_details: Optional[Dict[str, Any]] = Field(default=None, description="Detailed order information")
    estimated_delivery: Optional[str] = Field(default=None, description="Estimated delivery date")
    total_amount: Optional[float] = Field(default=None, description="Total order amount")
    sku_mappings_applied: List[Dict[str, str]] = Field(default_factory=list, description="SKU mappings applied")
    warnings: List[str] = Field(default_factory=list, description="Any warnings or issues")


class OrderContext(BaseModel):
    """Model for order processing context."""
    customer_email: str
    customer_info: Dict[str, Any] = Field(default_factory=dict)
    order_history: List[Dict[str, Any]] = Field(default_factory=list)
    sku_mappings: List[Dict[str, Any]] = Field(default_factory=list)
    pricing_info: Dict[str, Any] = Field(default_factory=dict)


class OrderProcessingAgent:
    """Agent responsible for processing orders through MYOB EXO ERP."""
    
    def __init__(self):
        """Initialize the order processing agent."""
        self.settings = get_settings()
        self.db_ops = get_database_operations()
        self.sku_manager = None  # Will be initialized async
        self.myob_client = None  # Will be initialized async
        
        # Initialize Mistral model for intelligent order processing
        mistral_provider = MistralProvider(api_key=self.settings.mistral_api_key)
        model_settings = ModelSettings(
            temperature=0.3,  # Lower temperature for more consistent order processing
            max_tokens=self.settings.mistral_max_tokens
        )
        self.model = MistralModel(
            model_name=self.settings.mistral_model,
            provider=mistral_provider,
            settings=model_settings
        )
        
        # Initialize PydanticAI agent
        self.agent = Agent(
            model=self.model,
            result_type=OrderResponse,
            system_prompt=self._get_system_prompt()
        )
        
        # Setup agent tools
        self._setup_agent_tools()
    
    async def initialize(self) -> None:
        """Initialize the order processing agent."""
        try:
            self.erp_client = await get_erp_client()
            logger.info("Order processing agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize order processing agent: {e}")
            raise OrderProcessingError(f"Initialization failed: {e}")
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for the order processing agent."""
        return """
You are an expert order processing agent for a sales support system. Your role is to:

1. Process customer orders efficiently and accurately
2. Validate order data and apply business rules
3. Handle order status inquiries and updates
4. Apply customer-specific SKU mappings
5. Calculate pricing and delivery estimates
6. Manage order modifications and cancellations

Guidelines:
- Always validate order data before processing
- Apply customer SKU mappings when available
- Check stock availability before confirming orders
- Calculate accurate pricing including any customer-specific rates
- Provide clear status updates and delivery estimates
- Handle errors gracefully and provide helpful error messages
- Log all order activities for audit purposes

When processing orders:
- Verify customer information and credit status
- Validate all SKUs and quantities
- Apply appropriate pricing and discounts
- Check inventory availability
- Generate accurate delivery estimates
- Create proper order records in the ERP system

For order status checks:
- Provide current status and tracking information
- Include estimated delivery dates
- Highlight any issues or delays
- Suggest next steps if needed

Remember: Accuracy and customer satisfaction are paramount in order processing.
"""
    
    def _setup_agent_tools(self) -> None:
        """Set up tools for the PydanticAI agent."""
        
        @self.agent.tool
        async def validate_sku(ctx: RunContext[OrderContext], sku: str) -> str:
            """Validate a SKU and get product information."""
            try:
                customer_email = ctx.deps.customer_email
                
                # Try to map customer SKU to internal SKU
                internal_sku = await self.sku_manager.get_internal_sku(customer_email, sku)
                
                if internal_sku:
                    # Get stock information
                    stock_info = await self.erp_client.get_stock_levels(internal_sku)
                    
                    if stock_info:
                        return f"SKU {sku} (internal: {internal_sku}) - Available: {stock_info.get('quantity_available', 'Unknown')}"
                    else:
                        return f"SKU {sku} mapped to {internal_sku} but stock info unavailable"
                else:
                    return f"SKU {sku} not found in customer mappings"
                    
            except Exception as e:
                return f"Error validating SKU {sku}: {e}"
        
        @self.agent.tool
        async def get_pricing(ctx: RunContext[OrderContext], sku: str, quantity: int) -> str:
            """Get pricing information for an item."""
            try:
                customer_info = ctx.deps.customer_info
                customer_id = customer_info.get('customer_id')
                
                pricing = await self.erp_client.get_item_pricing(sku, customer_id, quantity)
                
                if pricing:
                    unit_price = pricing.get('unit_price', 0)
                    total_price = unit_price * quantity
                    return f"SKU {sku}: ${unit_price:.2f} per unit, ${total_price:.2f} total for {quantity} units"
                else:
                    return f"Pricing not available for SKU {sku}"
                    
            except Exception as e:
                return f"Error getting pricing for {sku}: {e}"
        
        @self.agent.tool
        async def check_stock_availability(ctx: RunContext[OrderContext], sku: str, quantity: int) -> str:
            """Check stock availability for an item."""
            try:
                stock_info = await self.erp_client.get_stock_levels(sku)
                
                if stock_info:
                    available = stock_info.get('quantity_available', 0)
                    
                    if available >= quantity:
                        return f"Stock available: {available} units (requested: {quantity})"
                    else:
                        return f"Insufficient stock: {available} available (requested: {quantity})"
                else:
                    return f"Stock information not available for SKU {sku}"
                    
            except Exception as e:
                return f"Error checking stock for {sku}: {e}"
    
    async def process_order_request(self, request: OrderRequest) -> OrderResponse:
        """Process an order request.
        
        Args:
            request: Order processing request
            
        Returns:
            Order processing response
        """
        try:
            if not self.erp_client:
                await self.initialize()
            
            logger.info(f"Processing order request from {request.customer_email}: {request.action}")
            
            # Get order context
            context = await self._get_order_context(request.customer_email)
            
            # Process based on action type
            if request.action == OrderAction.CREATE:
                response = await self._create_order(request, context)
            elif request.action == OrderAction.UPDATE:
                response = await self._update_order(request, context)
            elif request.action == OrderAction.CANCEL:
                response = await self._cancel_order(request, context)
            elif request.action == OrderAction.STATUS_CHECK:
                response = await self._check_order_status(request, context)
            elif request.action == OrderAction.MODIFY:
                response = await self._modify_order(request, context)
            else:
                raise OrderProcessingError(f"Unknown order action: {request.action}")
            
            # Store the interaction
            await self._store_order_interaction(request, response, context)
            
            logger.info(f"Successfully processed order request: {request.action}")
            return response
            
        except Exception as e:
            logger.error(f"Error processing order request: {e}")
            return OrderResponse(
                success=False,
                message=f"Order processing failed: {e}",
                warnings=[str(e)]
            )
    
    async def _get_order_context(self, customer_email: str) -> OrderContext:
        """Get comprehensive order context."""
        try:
            # Get customer information
            customer_info = await self.erp_client.get_customers(email=customer_email)
            customer_data = customer_info[0] if customer_info else {}
            
            # Get order history
            order_history = await self.db_ops.get_customer_orders(customer_email, limit=5)
            
            # Get SKU mappings
            sku_mappings = await self.sku_manager.get_customer_mappings(customer_email)
            
            return OrderContext(
                customer_email=customer_email,
                customer_info=customer_data,
                order_history=order_history,
                sku_mappings=sku_mappings
            )
            
        except Exception as e:
            logger.error(f"Error getting order context: {e}")
            return OrderContext(customer_email=customer_email)
    
    async def _create_order(self, request: OrderRequest, context: OrderContext) -> OrderResponse:
        """Create a new order."""
        try:
            # Validate and prepare order data
            order_data = await self._prepare_order_data(request, context)
            
            # Apply SKU mappings
            mapped_items, sku_mappings_applied = await self._apply_sku_mappings(
                request.items, context.customer_email
            )
            
            # Validate stock availability
            stock_warnings = await self._validate_stock_availability(mapped_items)
            
            # Calculate pricing
            total_amount = await self._calculate_order_total(mapped_items, context)
            
            # Create order in ERP
            erp_response = await self.erp_client.create_sales_order(
                order_data, context.customer_email
            )
            
            order_id = erp_response.get('order_id', f"ORD-{uuid.uuid4().hex[:8].upper()}")
            
            # Store order record in database
            await self._store_order_record(
                order_id=order_id,
                customer_email=context.customer_email,
                items=mapped_items,
                total_amount=total_amount,
                status=OrderStatus.PENDING
            )
            
            return OrderResponse(
                success=True,
                order_id=order_id,
                status="pending",
                message=f"Order {order_id} created successfully",
                order_details=erp_response,
                total_amount=total_amount,
                sku_mappings_applied=sku_mappings_applied,
                warnings=stock_warnings,
                estimated_delivery=self._calculate_delivery_estimate()
            )
            
        except Exception as e:
            logger.error(f"Error creating order: {e}")
            raise OrderProcessingError(f"Failed to create order: {e}")
    
    async def _update_order(self, request: OrderRequest, context: OrderContext) -> OrderResponse:
        """Update an existing order."""
        try:
            if not request.order_id:
                raise OrderProcessingError("Order ID required for update")
            
            # Get current order
            current_orders = await self.erp_client.get_sales_orders(
                order_id=request.order_id,
                customer_email=context.customer_email
            )
            
            if not current_orders:
                raise OrderProcessingError(f"Order {request.order_id} not found")
            
            current_order = current_orders[0]
            
            # Prepare update data
            update_data = request.order_data or {}
            
            # Update order in ERP
            erp_response = await self.erp_client.update_sales_order(
                request.order_id, update_data, context.customer_email
            )
            
            # Update order record in database
            await self.db_ops.update_order_status(
                request.order_id,
                update_data.get('status', current_order.get('status')),
                request.notes
            )
            
            return OrderResponse(
                success=True,
                order_id=request.order_id,
                status=update_data.get('status', current_order.get('status')),
                message=f"Order {request.order_id} updated successfully",
                order_details=erp_response
            )
            
        except Exception as e:
            logger.error(f"Error updating order: {e}")
            raise OrderProcessingError(f"Failed to update order: {e}")
    
    async def _cancel_order(self, request: OrderRequest, context: OrderContext) -> OrderResponse:
        """Cancel an order."""
        try:
            if not request.order_id:
                raise OrderProcessingError("Order ID required for cancellation")
            
            # Update order status to cancelled
            update_data = {
                "status": "cancelled",
                "cancellation_reason": request.notes or "Customer requested cancellation"
            }
            
            erp_response = await self.erp_client.update_sales_order(
                request.order_id, update_data, context.customer_email
            )
            
            # Update database record
            await self.db_ops.update_order_status(
                request.order_id,
                OrderStatus.CANCELLED,
                request.notes
            )
            
            return OrderResponse(
                success=True,
                order_id=request.order_id,
                status="cancelled",
                message=f"Order {request.order_id} cancelled successfully",
                order_details=erp_response
            )
            
        except Exception as e:
            logger.error(f"Error cancelling order: {e}")
            raise OrderProcessingError(f"Failed to cancel order: {e}")
    
    async def _check_order_status(self, request: OrderRequest, context: OrderContext) -> OrderResponse:
        """Check order status."""
        try:
            if not request.order_id:
                raise OrderProcessingError("Order ID required for status check")
            
            # Get order from ERP
            orders = await self.erp_client.get_sales_orders(
                order_id=request.order_id,
                customer_email=context.customer_email
            )
            
            if not orders:
                raise OrderProcessingError(f"Order {request.order_id} not found")
            
            order = orders[0]
            
            return OrderResponse(
                success=True,
                order_id=request.order_id,
                status=order.get('status', 'unknown'),
                message=f"Order {request.order_id} status: {order.get('status', 'unknown')}",
                order_details=order,
                total_amount=order.get('total_amount'),
                estimated_delivery=order.get('estimated_delivery')
            )
            
        except Exception as e:
            logger.error(f"Error checking order status: {e}")
            raise OrderProcessingError(f"Failed to check order status: {e}")
    
    async def _modify_order(self, request: OrderRequest, context: OrderContext) -> OrderResponse:
        """Modify an existing order."""
        # For now, treat modify as update
        return await self._update_order(request, context)
    
    async def _prepare_order_data(self, request: OrderRequest, context: OrderContext) -> Dict[str, Any]:
        """Prepare order data for ERP submission."""
        order_data = {
            "customer_email": context.customer_email,
            "customer_id": context.customer_info.get('customer_id'),
            "items": request.items,
            "notes": request.notes,
            "priority": request.priority,
            "order_date": datetime.utcnow().isoformat()
        }
        
        # Add any additional order data
        if request.order_data:
            order_data.update(request.order_data)
        
        return order_data
    
    async def _apply_sku_mappings(
        self,
        items: List[Dict[str, Any]],
        customer_email: str
    ) -> tuple[List[Dict[str, Any]], List[Dict[str, str]]]:
        """Apply SKU mappings to order items."""
        mapped_items = []
        mappings_applied = []
        
        for item in items:
            mapped_item = item.copy()
            customer_sku = item.get('sku') or item.get('item_code')
            
            if customer_sku:
                internal_sku = await self.sku_manager.get_internal_sku(customer_email, customer_sku)
                
                if internal_sku:
                    mapped_item['sku'] = internal_sku
                    mapped_item['original_sku'] = customer_sku
                    mappings_applied.append({
                        "customer_sku": customer_sku,
                        "internal_sku": internal_sku
                    })
            
            mapped_items.append(mapped_item)
        
        return mapped_items, mappings_applied
    
    async def _validate_stock_availability(self, items: List[Dict[str, Any]]) -> List[str]:
        """Validate stock availability for order items."""
        warnings = []
        
        for item in items:
            sku = item.get('sku')
            quantity = item.get('quantity', 1)
            
            if sku:
                try:
                    stock_info = await self.erp_client.get_stock_levels(sku)
                    available = stock_info.get('quantity_available', 0)
                    
                    if available < quantity:
                        warnings.append(f"Low stock for {sku}: {available} available, {quantity} requested")
                        
                except Exception as e:
                    warnings.append(f"Could not check stock for {sku}: {e}")
        
        return warnings
    
    async def _calculate_order_total(self, items: List[Dict[str, Any]], context: OrderContext) -> float:
        """Calculate total order amount."""
        total = 0.0
        
        for item in items:
            sku = item.get('sku')
            quantity = item.get('quantity', 1)
            
            if sku:
                try:
                    pricing = await self.erp_client.get_item_pricing(
                        sku,
                        context.customer_info.get('customer_id'),
                        quantity
                    )
                    
                    unit_price = pricing.get('unit_price', 0)
                    total += unit_price * quantity
                    
                except Exception as e:
                    logger.warning(f"Could not get pricing for {sku}: {e}")
        
        return total
    
    async def _store_order_record(
        self,
        order_id: str,
        customer_email: str,
        items: List[Dict[str, Any]],
        total_amount: float,
        status: OrderStatus
    ) -> None:
        """Store order record in database."""
        try:
            # For simplicity, store the first item's details
            # In a real implementation, you'd store all items
            first_item = items[0] if items else {}
            
            order_record = OrderRecord(
                order_id=order_id,
                customer_email=customer_email,
                customer_sku=first_item.get('original_sku'),
                internal_sku=first_item.get('sku'),
                quantity=first_item.get('quantity', 1),
                total_amount=total_amount,
                status=status
            )
            
            await self.db_ops.create_order_record(order_record)
            
        except Exception as e:
            logger.error(f"Error storing order record: {e}")
    
    async def _store_order_interaction(
        self,
        request: OrderRequest,
        response: OrderResponse,
        context: OrderContext
    ) -> None:
        """Store order interaction in database."""
        try:
            interaction_content = f"Order Action: {request.action}\nOrder ID: {response.order_id}\nResult: {response.message}"
            
            metadata = {
                "agent": "order_processing",
                "action": request.action,
                "order_id": response.order_id,
                "success": response.success,
                "total_amount": response.total_amount,
                "sku_mappings_applied": response.sku_mappings_applied
            }
            
            interaction = CustomerInteraction(
                customer_email=request.customer_email,
                interaction_type="order",
                content=interaction_content,
                metadata=metadata
            )
            
            await self.db_ops.create_interaction(interaction)
            
        except Exception as e:
            logger.error(f"Error storing order interaction: {e}")
    
    def _calculate_delivery_estimate(self) -> str:
        """Calculate estimated delivery date."""
        # Simple estimation - in reality this would be more sophisticated
        estimated_date = datetime.utcnow() + timedelta(days=5)
        return estimated_date.strftime("%Y-%m-%d")
    
    async def get_agent_stats(self) -> Dict[str, Any]:
        """Get order processing agent statistics."""
        try:
            # Get recent order interactions
            recent_interactions = await self.db_ops.get_interactions(
                interaction_type="order",
                limit=100
            )
            
            total_orders = len(recent_interactions)
            successful_orders = len([
                i for i in recent_interactions 
                if i.get('metadata', {}).get('success', False)
            ])
            
            return {
                "total_orders_processed": total_orders,
                "successful_orders": successful_orders,
                "success_rate": (successful_orders / total_orders) * 100 if total_orders > 0 else 0,
                "model_name": self.settings.mistral_model
            }
            
        except Exception as e:
            logger.error(f"Error getting agent stats: {e}")
            return {"error": str(e)}


# Global order processing agent instance
_order_agent: Optional[OrderProcessingAgent] = None


async def get_order_processing_agent() -> OrderProcessingAgent:
    """Get the global order processing agent instance."""
    global _order_agent
    
    if _order_agent is None:
        _order_agent = OrderProcessingAgent()
        await _order_agent.initialize()
    
    return _order_agent


async def process_order(
    customer_email: str,
    action: str,
    order_data: Optional[Dict[str, Any]] = None,
    order_id: Optional[str] = None,
    items: Optional[List[Dict[str, Any]]] = None,
    notes: Optional[str] = None,
    priority: str = "normal"
) -> OrderResponse:
    """Process an order request."""
    agent = await get_order_processing_agent()
    
    request = OrderRequest(
        customer_email=customer_email,
        action=OrderAction(action),
        order_data=order_data,
        order_id=order_id,
        items=items or [],
        notes=notes,
        priority=priority
    )
    
    return await agent.process_order_request(request)


async def get_order_stats() -> Dict[str, Any]:
    """Get order processing statistics."""
    agent = await get_order_processing_agent()
    return await agent.get_agent_stats()
