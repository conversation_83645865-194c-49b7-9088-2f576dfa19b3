---
url: "https://developer.myob.com/api/exo/endpoints/searchtemplate/"
title: "Search Template"
---

[![](https://developer.myob.com/media/cms_page_media/23/myob_logo_small.png)](http://www.myob.com/)

# Search Template

List all search templates or return search results based on a search template set up in MYOB EXO Business.

**Date Released:** May 30th 2014 **Date Updated:** May 30th 2014

| URL | Supports |
| --- | --- |
| {URI}/searchtemplate<br>{URI}/searchtemplate/{id}{parameters} | [GET](https://developer.myob.com/api/exo/endpoints/searchtemplate/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/searchtemplate/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/searchtemplate/#POST)<br>[GET](https://developer.myob.com/api/exo/endpoints/searchtemplate/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/searchtemplate/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/searchtemplate/#POST) |
| {URI} is exo.api.myob.com when connecting to the cloud or the address of the machine hosting the API when connecting on premise. |

Calling the endpoint by itself returns a list of search templates.

To use a search template to return search results, call the endpoint with the ID number of the search template you want to use, for example:

`http://exo.api.myob.com/searchtemplate/1`

Depending on the search template being called, the call may need to pass parameters to filter the search results, for example:

`http://exo.api.myob.com/searchtemplate/1?accno=21&q=airfil`

This will return search results using the search template with the ID number 1 and passing in the Debtor account number 21 and the search string “airfil” as parameters.

The elements list below details information for Search Template. To view the descriptions for the elements you can either hover any attribute to reveal details [or click here to show all details inline.](https://developer.myob.com/api/exo/endpoints/searchtemplate/#reveal)

#### Attribute Details

- name string,null
- Type: string,null
- active boolean
- Type: boolean
- type string,null
- Type: string,null
- parameters array,null
- Type: array,null
- rel string,null
- Type: string,null
- title string,null
- Type: string,null
- id integer
- Required on PUT
- href string,null
- Type: string,null

#### Example json GET response

- {
  - 0
    - {
      - name : All Active Companies
      - active : true
      - type : G
      - parameters
        - {
          - 0
            - {
              - paramname : CURRENTLAT
              - paramtype : FLOAT
            - }
          - 1
            - {
              - paramname : CURRENTLONG
              - paramtype : FLOAT
            - }
          - 2
            - {
              - paramname : RADIUS
              - paramtype : FLOAT
            - }
          - 3
            - {
              - paramname : SALESNO
              - paramtype : INTEGER
            - }
        - }
      - id : 11
      - href : {URI}/searchtemplate/11
    - }
  - 1
    - {
      - name : Available Stock
      - active : true
      - type : S
      - parameters
        - {
          - 0
            - {
              - paramname : LOCNO
              - paramtype : INTEGER
            - }
        - }
      - id : 2
      - href : {URI}/searchtemplate/2
    - }
  - 2
    - {
      - name : Customers Active last 30 days
      - active : true
      - type : G
      - parameters
        - {
          - 0
            - {
              - paramname : CURRENTLAT
              - paramtype : FLOAT
            - }
          - 1
            - {
              - paramname : CURRENTLONG
              - paramtype : FLOAT
            - }
          - 2
            - {
              - paramname : RADIUS
              - paramtype : FLOAT
            - }
        - }
      - id : 8
      - href : {URI}/searchtemplate/8
    - }
  - 3
    - {
      - name : Customers Owe Me Money
      - active : true
      - type : G
      - parameters
        - {
          - 0
            - {
              - paramname : CURRENTLAT
              - paramtype : FLOAT
            - }
          - 1
            - {
              - paramname : CURRENTLONG
              - paramtype : FLOAT
            - }
          - 2
            - {
              - paramname : RADIUS
              - paramtype : FLOAT
            - }
        - }
      - id : 6
      - href : {URI}/searchtemplate/6
    - }
  - 4
    - {
      - name : Customers with Current Orders
      - active : true
      - type : G
      - parameters
        - {
          - 0
            - {
              - paramname : CURRENTLAT
              - paramtype : FLOAT
            - }
          - 1
            - {
              - paramname : CURRENTLONG
              - paramtype : FLOAT
            - }
          - 2
            - {
              - paramname : RADIUS
              - paramtype : FLOAT
            - }
        - }
      - id : 7
      - href : {URI}/searchtemplate/7
    - }
  - 5
    - {
      - name : Last 100 Items Sold To This Account
      - active : true
      - type : S
      - parameters
        - {
          - 0
            - {
              - paramname : ACCNO
              - paramtype : INTEGER
            - }
        - }
      - id : 1
      - href : {URI}/searchtemplate/1
    - }
  - 6
    - {
      - name : My Active Companies
      - active : true
      - type : G
      - parameters
        - {
          - 0
            - {
              - paramname : CURRENTLAT
              - paramtype : FLOAT
            - }
          - 1
            - {
              - paramname : CURRENTLONG
              - paramtype : FLOAT
            - }
          - 2
            - {
              - paramname : RADIUS
              - paramtype : FLOAT
            - }
          - 3
            - {
              - paramname : SALESNO
              - paramtype : INTEGER
            - }
        - }
      - id : 10
      - href : {URI}/searchtemplate/10
    - }
  - 7
    - {
      - name : Previous Invoice to this Account
      - active : true
      - type : S
      - parameters
        - {
          - 0
            - {
              - paramname : ACCNO
              - paramtype : INTEGER
            - }
        - }
      - id : 3
      - href : {URI}/searchtemplate/3
    - }
  - 8
    - {
      - name : Prospects Active Last 30 days
      - active : true
      - type : G
      - parameters
        - {
          - 0
            - {
              - paramname : CURRENTLAT
              - paramtype : FLOAT
            - }
          - 1
            - {
              - paramname : CURRENTLONG
              - paramtype : FLOAT
            - }
          - 2
            - {
              - paramname : RADIUS
              - paramtype : FLOAT
            - }
        - }
      - id : 9
      - href : {URI}/searchtemplate/9
    - }
  - 9
    - {
      - name : Sell Down Items
      - active : true
      - type : S
      - parameters
        - {
          - 0
            - {
              - paramname : ACCNO
              - paramtype : INTEGER
            - }
        - }
      - id : 5
      - href : {URI}/searchtemplate/5
    - }
- }

{URI} is defined as: http://exo.api.myob.com/

|     |     |
| --- | --- |
|  |  |