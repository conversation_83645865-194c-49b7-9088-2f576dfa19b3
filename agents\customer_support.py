"""
Customer Support Agent for the multi-agent sales support system.
Handles customer queries using Mistral AI and PydanticAI with context-aware responses.
"""

import asyncio
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
import json

from pydantic_ai import Agent, RunContext
from pydantic_ai.models.mistral import MistralModel
from pydantic import BaseModel, Field

from config.settings import get_settings
from database.operations import get_database_operations
from database.schemas import CustomerInteraction, AgentMemory, SupportTicket, EscalationLevel
from tools.myob_tools import get_sku_mapping_manager, get_myob_client
from tools.gmail_tools import send_email

logger = logging.getLogger(__name__)


class CustomerSupportError(Exception):
    """Custom exception for customer support operations."""
    pass


class SupportQuery(BaseModel):
    """Model for customer support queries."""
    customer_email: str = Field(..., description="Customer's email address")
    query: str = Field(..., description="Customer's question or request")
    context: Optional[Dict[str, Any]] = Field(default=None, description="Additional context")
    session_id: Optional[str] = Field(default=None, description="Session identifier")
    priority: str = Field(default="medium", description="Query priority level")


class SupportResponse(BaseModel):
    """Model for customer support responses."""
    response: str = Field(..., description="Response to the customer")
    confidence: float = Field(..., description="Confidence level (0-1)")
    requires_escalation: bool = Field(default=False, description="Whether query needs escalation")
    escalation_reason: Optional[str] = Field(default=None, description="Reason for escalation")
    suggested_actions: List[str] = Field(default_factory=list, description="Suggested follow-up actions")
    referenced_orders: List[str] = Field(default_factory=list, description="Referenced order IDs")
    referenced_skus: List[str] = Field(default_factory=list, description="Referenced SKUs")


class CustomerContext(BaseModel):
    """Model for customer context information."""
    customer_email: str
    recent_interactions: List[Dict[str, Any]] = Field(default_factory=list)
    order_history: List[Dict[str, Any]] = Field(default_factory=list)
    sku_mappings: List[Dict[str, Any]] = Field(default_factory=list)
    support_tickets: List[Dict[str, Any]] = Field(default_factory=list)
    preferences: Dict[str, Any] = Field(default_factory=dict)


class CustomerSupportAgent:
    """AI-powered customer support agent using Mistral AI and PydanticAI."""
    
    def __init__(self):
        """Initialize the customer support agent."""
        self.settings = get_settings()
        self.db_ops = get_database_operations()
        self.sku_manager = None  # Will be initialized async
        
        # Initialize Mistral model
        self.model = MistralModel(
            model_name=self.settings.mistral_model,
            api_key=self.settings.mistral_api_key
        )
        
        # Initialize PydanticAI agent
        self.agent = Agent(
            model=self.model,
            output_type=SupportResponse,
            system_prompt=self._get_system_prompt(),
            deps_type=CustomerContext
        )
        
        # Add tools to the agent
        self._setup_agent_tools()

    async def initialize(self) -> None:
        """Initialize async components."""
        if self.sku_manager is None:
            self.sku_manager = await get_sku_mapping_manager()
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for the customer support agent."""
        return """
You are an expert customer support agent for a sales support system. Your role is to:

1. Provide helpful, accurate, and professional responses to customer queries
2. Use customer context (order history, previous interactions, SKU mappings) to personalize responses
3. Identify when queries require escalation to human agents
4. Extract relevant information like order IDs and SKUs from customer queries
5. Suggest appropriate follow-up actions

Guidelines:
- Always be polite, professional, and empathetic
- Use the customer's context to provide personalized responses
- If you don't have enough information, ask clarifying questions
- Escalate complex issues, complaints, or requests outside your capabilities
- Reference specific orders, SKUs, or previous interactions when relevant
- Provide clear, actionable information

When responding:
- Set confidence level based on how certain you are about your response
- Mark requires_escalation=True for complex issues, complaints, or when you lack information
- Include relevant order IDs and SKUs in your response metadata
- Suggest specific follow-up actions when appropriate

Remember: You have access to customer order history, SKU mappings, and previous interactions through the provided context.
"""
    
    def _setup_agent_tools(self) -> None:
        """Set up tools for the PydanticAI agent."""
        
        @self.agent.tool
        async def get_order_status(ctx: RunContext[CustomerContext], order_id: str) -> str:
            """Get the status of a customer order."""
            try:
                # This would integrate with the MCP server to get order status from MYOB EXO
                # For now, return a placeholder
                return f"Order {order_id} status: Processing (estimated delivery: 3-5 business days)"
            except Exception as e:
                return f"Unable to retrieve order status: {e}"
        
        @self.agent.tool
        async def lookup_sku_info(ctx: RunContext[CustomerContext], sku: str) -> str:
            """Look up information about a SKU."""
            try:
                customer_email = ctx.deps.customer_email
                # Ensure sku_manager is initialized
                if self.sku_manager is None:
                    await self.initialize()
                # Try to map customer SKU to internal SKU
                internal_sku = await self.sku_manager.get_internal_sku(customer_email, sku)  # type: ignore
                
                if internal_sku:
                    return f"SKU {sku} (internal: {internal_sku}) - Product information available"
                else:
                    return f"SKU {sku} not found in your catalog. Please verify the SKU or contact support."
            except Exception as e:
                return f"Unable to lookup SKU information: {e}"
        
        @self.agent.tool
        async def search_previous_interactions(ctx: RunContext[CustomerContext], query: str) -> str:
            """Search previous customer interactions for relevant information."""
            try:
                customer_email = ctx.deps.customer_email
                # Search interactions using semantic search
                results = await self.db_ops.search_interactions_semantic(
                    query_text=query,
                    customer_email=customer_email,
                    limit=3
                )
                
                if results:
                    summaries = []
                    for result in results:
                        summaries.append(f"Previous interaction: {result['content'][:200]}...")
                    return "Relevant previous interactions:\n" + "\n".join(summaries)
                else:
                    return "No relevant previous interactions found."
            except Exception as e:
                return f"Unable to search previous interactions: {e}"
    
    async def process_query(self, query: SupportQuery) -> SupportResponse:
        """Process a customer support query.
        
        Args:
            query: Customer support query
            
        Returns:
            Support response with recommendations
        """
        try:
            logger.info(f"Processing support query from {query.customer_email}")
            
            # Get customer context
            context = await self._get_customer_context(query.customer_email)
            
            # Prepare the prompt with context
            prompt = await self._prepare_prompt(query, context)
            
            # Run the AI agent
            result = await self.agent.run(prompt, deps=context)
            
            # Store the interaction
            await self._store_interaction(query, result.data, context)
            
            # Handle escalation if needed
            if result.data.requires_escalation:
                await self._handle_escalation(query, result.data, context)
            
            # Store agent memory
            await self._store_agent_memory(query, result.data, context)
            
            logger.info(f"Successfully processed query from {query.customer_email}")
            return result.data
            
        except Exception as e:
            logger.error(f"Error processing support query: {e}")
            # Return a fallback response
            return SupportResponse(
                response="I apologize, but I'm experiencing technical difficulties. Please contact our support team directly for assistance.",
                confidence=0.0,
                requires_escalation=True,
                escalation_reason="Technical error in AI processing"
            )
    
    async def _get_customer_context(self, customer_email: str) -> CustomerContext:
        """Get comprehensive customer context."""
        try:
            # Get recent interactions
            recent_interactions = await self.db_ops.get_interactions(
                customer_email=customer_email,
                limit=self.settings.support_context_limit
            )
            
            # Get order history
            order_history = await self.db_ops.get_customer_orders(
                customer_email=customer_email,
                limit=10
            )
            
            # Get SKU mappings
            sku_mappings = await self.sku_manager.get_customer_mappings(customer_email)
            
            # Get support tickets
            # This would be implemented with proper support ticket queries
            support_tickets = []
            
            return CustomerContext(
                customer_email=customer_email,
                recent_interactions=recent_interactions,
                order_history=order_history,
                sku_mappings=sku_mappings,
                support_tickets=support_tickets
            )
            
        except Exception as e:
            logger.error(f"Error getting customer context: {e}")
            return CustomerContext(customer_email=customer_email)
    
    async def _prepare_prompt(self, query: SupportQuery, context: CustomerContext) -> str:
        """Prepare the prompt with customer context."""
        prompt_parts = [
            f"Customer Query: {query.query}",
            f"Customer Email: {query.customer_email}",
            f"Priority: {query.priority}",
            ""
        ]
        
        # Add context information
        if context.recent_interactions:
            prompt_parts.append("Recent Interactions:")
            for interaction in context.recent_interactions[:3]:
                prompt_parts.append(f"- {interaction.get('interaction_type', 'unknown')}: {interaction.get('content', '')[:100]}...")
            prompt_parts.append("")
        
        if context.order_history:
            prompt_parts.append("Recent Orders:")
            for order in context.order_history[:3]:
                prompt_parts.append(f"- Order {order.get('order_id', 'unknown')}: {order.get('status', 'unknown')} (${order.get('total_amount', 'unknown')})")
            prompt_parts.append("")
        
        if context.sku_mappings:
            prompt_parts.append("Customer SKU Mappings:")
            for mapping in context.sku_mappings[:5]:
                prompt_parts.append(f"- {mapping.get('customer_sku', 'unknown')} -> {mapping.get('internal_sku', 'unknown')}")
            prompt_parts.append("")
        
        # Add additional context if provided
        if query.context:
            prompt_parts.append("Additional Context:")
            prompt_parts.append(json.dumps(query.context, indent=2))
            prompt_parts.append("")
        
        prompt_parts.append("Please provide a helpful response to this customer query.")
        
        return "\n".join(prompt_parts)
    
    async def _store_interaction(
        self,
        query: SupportQuery,
        response: SupportResponse,
        context: CustomerContext
    ) -> None:
        """Store the support interaction."""
        try:
            interaction_content = f"Query: {query.query}\n\nResponse: {response.response}"
            
            metadata = {
                "agent": "customer_support",
                "confidence": response.confidence,
                "requires_escalation": response.requires_escalation,
                "escalation_reason": response.escalation_reason,
                "suggested_actions": response.suggested_actions,
                "referenced_orders": response.referenced_orders,
                "referenced_skus": response.referenced_skus,
                "session_id": query.session_id,
                "priority": query.priority
            }
            
            interaction = CustomerInteraction(
                customer_email=query.customer_email,
                interaction_type="support",
                content=interaction_content,
                metadata=metadata
            )
            
            await self.db_ops.create_interaction(interaction)
            
        except Exception as e:
            logger.error(f"Error storing support interaction: {e}")
    
    async def _handle_escalation(
        self,
        query: SupportQuery,
        response: SupportResponse,
        context: CustomerContext
    ) -> None:
        """Handle query escalation."""
        try:
            if not self.settings.enable_escalation:
                logger.info("Escalation disabled in settings")
                return
            
            # Create support ticket
            ticket_id = f"TICKET-{uuid.uuid4().hex[:8].upper()}"
            
            # Determine escalation level
            escalation_level = EscalationLevel.MEDIUM
            if query.priority == "high" or "urgent" in query.query.lower():
                escalation_level = EscalationLevel.HIGH
            elif "complaint" in query.query.lower() or "angry" in query.query.lower():
                escalation_level = EscalationLevel.HIGH
            
            ticket = SupportTicket(
                ticket_id=ticket_id,
                customer_email=query.customer_email,
                subject=f"Escalated Query: {query.query[:50]}...",
                description=f"Original Query: {query.query}\n\nAI Response: {response.response}\n\nEscalation Reason: {response.escalation_reason}",
                category="ai_escalation",
                priority=query.priority,
                escalation_level=escalation_level
            )
            
            await self.db_ops.create_support_ticket(ticket)
            
            logger.info(f"Created escalation ticket {ticket_id} for {query.customer_email}")
            
            # Optionally send notification email to support team
            # This would be configured based on business requirements
            
        except Exception as e:
            logger.error(f"Error handling escalation: {e}")
    
    async def _store_agent_memory(
        self,
        query: SupportQuery,
        response: SupportResponse,
        context: CustomerContext
    ) -> None:
        """Store agent memory for future context."""
        try:
            memory_data = {
                "query": query.query,
                "response": response.response,
                "confidence": response.confidence,
                "timestamp": datetime.utcnow().isoformat(),
                "context_summary": {
                    "recent_interactions_count": len(context.recent_interactions),
                    "order_history_count": len(context.order_history),
                    "sku_mappings_count": len(context.sku_mappings)
                }
            }
            
            # Set expiration (e.g., 30 days)
            expires_at = datetime.utcnow() + timedelta(days=30)
            
            memory = AgentMemory(
                agent_name="customer_support",
                context_type="support_interaction",
                context_data=memory_data,
                customer_email=query.customer_email,
                session_id=query.session_id,
                expires_at=expires_at
            )
            
            await self.db_ops.store_agent_memory(memory)
            
        except Exception as e:
            logger.error(f"Error storing agent memory: {e}")
    
    async def get_agent_stats(self) -> Dict[str, Any]:
        """Get customer support agent statistics."""
        try:
            # Get recent interactions
            recent_interactions = await self.db_ops.get_interactions(
                interaction_type="support",
                limit=100
            )
            
            total_queries = len(recent_interactions)
            escalated_queries = len([
                i for i in recent_interactions 
                if i.get('metadata', {}).get('requires_escalation', False)
            ])
            
            avg_confidence = sum([
                i.get('metadata', {}).get('confidence', 0) 
                for i in recent_interactions
            ]) / total_queries if total_queries > 0 else 0
            
            return {
                "total_queries_processed": total_queries,
                "escalated_queries": escalated_queries,
                "escalation_rate": (escalated_queries / total_queries) * 100 if total_queries > 0 else 0,
                "average_confidence": avg_confidence,
                "model_name": self.settings.mistral_model
            }
            
        except Exception as e:
            logger.error(f"Error getting agent stats: {e}")
            return {"error": str(e)}


# Global customer support agent instance
_support_agent: Optional[CustomerSupportAgent] = None


async def get_customer_support_agent() -> CustomerSupportAgent:
    """Get the global customer support agent instance."""
    global _support_agent
    
    if _support_agent is None:
        _support_agent = CustomerSupportAgent()
    
    return _support_agent


async def process_support_query(
    customer_email: str,
    query: str,
    context: Optional[Dict[str, Any]] = None,
    session_id: Optional[str] = None,
    priority: str = "medium"
) -> SupportResponse:
    """Process a customer support query."""
    agent = await get_customer_support_agent()
    
    support_query = SupportQuery(
        customer_email=customer_email,
        query=query,
        context=context,
        session_id=session_id,
        priority=priority
    )
    
    return await agent.process_query(support_query)


async def get_support_stats() -> Dict[str, Any]:
    """Get customer support statistics."""
    agent = await get_customer_support_agent()
    return await agent.get_agent_stats()
