#!/usr/bin/env python3
"""
Demo script to test the temp file creation functionality without requiring Gmail authentication.
This directly tests the _create_temp_file method.
"""

import asyncio
import os
import json
from datetime import datetime
from tools.gmail_tools import GmailManager

async def demo_temp_file_creation():
    """Demonstrate temp file creation with mock email data."""
    print("🚀 Demo: Gmail Temp File Creation")
    print("=" * 50)
    
    # Create a GmailManager instance (without initializing Gmail API)
    gmail_manager = GmailManager()
    
    # Create mock email data
    mock_email_data = {
        'id': '1234567890abcdef',
        'thread_id': 'thread_123',
        'label_ids': ['INBOX', 'UNREAD'],
        'snippet': 'This is a test email for demonstrating temp file creation...',
        'headers': {
            'From': '<EMAIL>',
            'To': '<EMAIL>',
            'Subject': 'Test Email for Temp File Demo',
            'Date': 'Mon, 29 Jul 2025 13:00:00 +0000'
        },
        'body': '''Hello Team,

This is a test email to demonstrate the temp file creation functionality.

The system should create a JSON file in the temp/ directory with this email's details.

Key features being tested:
- Email metadata extraction
- Body content preview
- Attachment information
- Processing timestamps

Best regards,
Test System''',
        'attachments': [
            {'filename': 'invoice.pdf', 'size': 245760},
            {'filename': 'receipt.jpg', 'size': 102400}
        ],
        'size_estimate': 15420,
        'internal_date': '1722254400000',
        'from': '<EMAIL>',
        'to': '<EMAIL>',
        'subject': 'Test Email for Temp File Demo',
        'date': 'Mon, 29 Jul 2025 13:00:00 +0000'
    }
    
    print("📧 Mock email data created:")
    print(f"   From: {mock_email_data['from']}")
    print(f"   Subject: {mock_email_data['subject']}")
    print(f"   Attachments: {len(mock_email_data['attachments'])}")
    print(f"   Body length: {len(mock_email_data['body'])} characters")
    
    print("\n🔄 Creating temp file...")
    
    try:
        # Call the temp file creation method
        await gmail_manager._create_temp_file(mock_email_data)
        print("✅ Temp file creation completed!")
        
    except Exception as e:
        print(f"❌ Error creating temp file: {e}")
        return False
    
    print("\n📁 Checking temp directory...")
    
    # Check what files were created
    temp_dir = "temp"
    if os.path.exists(temp_dir):
        files = [f for f in os.listdir(temp_dir) if f.endswith('.json')]
        
        if files:
            print(f"✅ Found {len(files)} JSON file(s) in temp directory:")
            
            # Show the most recent file
            latest_file = max(files, key=lambda f: os.path.getmtime(os.path.join(temp_dir, f)))
            filepath = os.path.join(temp_dir, latest_file)
            
            print(f"📄 Latest file: {latest_file}")
            
            # Read and display the content
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    temp_data = json.load(f)
                
                print("\n📋 Temp file contents:")
                print(f"   Timestamp: {temp_data.get('timestamp')}")
                print(f"   Message ID: {temp_data.get('message_id')}")
                print(f"   From: {temp_data.get('from')}")
                print(f"   Subject: {temp_data.get('subject')}")
                print(f"   Body preview: {temp_data.get('body_preview')[:100]}...")
                print(f"   Attachments: {temp_data.get('attachments_count')} files")
                print(f"   Processing status: {temp_data.get('processing_status')}")
                
                if temp_data.get('attachments'):
                    print(f"   Attachment files: {', '.join(temp_data.get('attachments', []))}")
                
                return True
                
            except Exception as e:
                print(f"❌ Error reading temp file: {e}")
                return False
        else:
            print("❌ No JSON files found in temp directory")
            return False
    else:
        print("❌ Temp directory does not exist")
        return False

async def demo_multiple_emails():
    """Demo creating temp files for multiple emails."""
    print("\n" + "=" * 50)
    print("🔄 Demo: Multiple Email Processing")
    print("=" * 50)
    
    gmail_manager = GmailManager()
    
    # Create multiple mock emails
    mock_emails = [
        {
            'id': 'email001',
            'from': '<EMAIL>',
            'subject': 'Order Inquiry - Product ABC123',
            'body': 'Hello, I would like to inquire about product ABC123. Is it in stock?',
            'attachments': [],
            'size_estimate': 1024
        },
        {
            'id': 'email002', 
            'from': '<EMAIL>',
            'subject': 'Invoice #INV-2025-001',
            'body': 'Please find attached invoice for recent order.',
            'attachments': [{'filename': 'invoice_001.pdf', 'size': 156789}],
            'size_estimate': 2048
        },
        {
            'id': 'email003',
            'from': '<EMAIL>',
            'subject': 'Technical Support Request',
            'body': 'We are experiencing issues with our recent order. Please assist.',
            'attachments': [
                {'filename': 'error_log.txt', 'size': 4096},
                {'filename': 'screenshot.png', 'size': 245760}
            ],
            'size_estimate': 3072
        }
    ]
    
    print(f"📧 Processing {len(mock_emails)} mock emails...")
    
    for i, email_data in enumerate(mock_emails, 1):
        # Add required fields
        email_data.update({
            'thread_id': f'thread_{email_data["id"]}',
            'label_ids': ['INBOX'],
            'snippet': email_data['body'][:50] + '...',
            'headers': {'From': email_data['from'], 'Subject': email_data['subject']},
            'internal_date': str(int(datetime.now().timestamp() * 1000)),
            'to': '<EMAIL>',
            'date': datetime.now().strftime('%a, %d %b %Y %H:%M:%S +0000')
        })
        
        print(f"\n📨 Processing email {i}/{len(mock_emails)}: {email_data['subject']}")
        
        try:
            await gmail_manager._create_temp_file(email_data)
            print(f"   ✅ Temp file created for email {email_data['id']}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    # Show final count
    temp_dir = "temp"
    if os.path.exists(temp_dir):
        json_files = [f for f in os.listdir(temp_dir) if f.endswith('.json')]
        print(f"\n📊 Total JSON files in temp directory: {len(json_files)}")

async def main():
    """Main demo function."""
    print("🎯 Gmail Temp File Creation Demo")
    print("This demo shows how email processing creates temp files for testing.")
    print()
    
    # Demo 1: Single email
    success1 = await demo_temp_file_creation()
    
    if success1:
        # Demo 2: Multiple emails
        await demo_multiple_emails()
    
    print("\n" + "=" * 50)
    print("🎉 Demo completed!")
    print("\n💡 Key points:")
    print("- Temp files are created in JSON format")
    print("- Files contain email metadata and body preview")
    print("- Filenames include timestamp and message ID")
    print("- Files are useful for testing and debugging email processing")
    print("\n📁 Check the 'temp/' directory to see the created files.")

if __name__ == "__main__":
    asyncio.run(main())
