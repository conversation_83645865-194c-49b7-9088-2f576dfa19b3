#!/usr/bin/env python3
"""
Setup Database Tables for HITL System
Creates necessary tables for learning feedback and analysis storage
"""

import os
import asyncio
from datetime import datetime, timezone

from supabase import create_client
from dotenv import load_dotenv

# Load environment
load_dotenv()

def setup_database_tables():
    """Create necessary database tables for HITL system."""
    
    supabase = create_client(
        os.getenv("SUPABASE_URL"),
        os.getenv("SUPABASE_SERVICE_KEY")
    )
    
    print("🔧 Setting up database tables for HITL system...")
    
    # 1. Create learning_feedback table
    try:
        learning_feedback_sql = """
        CREATE TABLE IF NOT EXISTS learning_feedback (
            id SERIAL PRIMARY KEY,
            email_id TEXT,
            customer_email TEXT,
            notes TEXT NOT NULL,
            category TEXT DEFAULT 'other',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            reviewer TEXT DEFAULT 'human_reviewer'
        );
        """
        
        supabase.rpc('execute_sql', {'sql': learning_feedback_sql}).execute()
        print("✅ Created learning_feedback table")
        
    except Exception as e:
        print(f"⚠️ learning_feedback table may already exist: {e}")
    
    # 2. Create analysis_feedback table
    try:
        analysis_feedback_sql = """
        CREATE TABLE IF NOT EXISTS analysis_feedback (
            id SERIAL PRIMARY KEY,
            email_id TEXT,
            original_analysis JSONB,
            rejection_reason TEXT,
            correct_intent TEXT,
            correct_urgency TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """
        
        supabase.rpc('execute_sql', {'sql': analysis_feedback_sql}).execute()
        print("✅ Created analysis_feedback table")
        
    except Exception as e:
        print(f"⚠️ analysis_feedback table may already exist: {e}")
    
    # 3. Update customer_interactions table to include review fields
    try:
        update_interactions_sql = """
        ALTER TABLE customer_interactions 
        ADD COLUMN IF NOT EXISTS review_status TEXT DEFAULT 'pending',
        ADD COLUMN IF NOT EXISTS review_notes TEXT,
        ADD COLUMN IF NOT EXISTS reviewed_at TIMESTAMP WITH TIME ZONE,
        ADD COLUMN IF NOT EXISTS reviewed_by TEXT;
        """
        
        supabase.rpc('execute_sql', {'sql': update_interactions_sql}).execute()
        print("✅ Updated customer_interactions table with review fields")
        
    except Exception as e:
        print(f"⚠️ customer_interactions table may already have review fields: {e}")
    
    # 4. Create indexes for better performance
    try:
        indexes_sql = """
        CREATE INDEX IF NOT EXISTS idx_customer_interactions_review_status 
        ON customer_interactions(review_status);
        
        CREATE INDEX IF NOT EXISTS idx_customer_interactions_created_at 
        ON customer_interactions(created_at);
        
        CREATE INDEX IF NOT EXISTS idx_learning_feedback_email_id 
        ON learning_feedback(email_id);
        
        CREATE INDEX IF NOT EXISTS idx_analysis_feedback_email_id 
        ON analysis_feedback(email_id);
        """
        
        supabase.rpc('execute_sql', {'sql': indexes_sql}).execute()
        print("✅ Created database indexes")
        
    except Exception as e:
        print(f"⚠️ Indexes may already exist: {e}")
    
    # 5. Insert sample data for testing
    try:
        # Check if we have any customer interactions
        result = supabase.table("customer_interactions").select("count").execute()
        
        if not result.data or len(result.data) == 0:
            print("📝 Inserting sample data for testing...")
            
            sample_data = {
                "customer_email": "<EMAIL>",
                "interaction_type": "email",
                "content": "This is a test email for the HITL system",
                "metadata": {
                    "subject": "Test Email",
                    "analysis": {
                        "intent": "inquiry",
                        "urgency": "medium",
                        "confidence": 0.85,
                        "summary": "Test email for system validation",
                        "suggested_action": "respond",
                        "flags": ["test"]
                    },
                    "review_status": "pending"
                },
                "created_at": datetime.now(timezone.utc).isoformat()
            }
            
            supabase.table("customer_interactions").insert(sample_data).execute()
            print("✅ Inserted sample test data")
        else:
            print("📊 Customer interactions table already has data")
            
    except Exception as e:
        print(f"⚠️ Could not insert sample data: {e}")
    
    print("\n🎉 Database setup complete!")
    print("\nAvailable tables:")
    print("- customer_interactions (with review fields)")
    print("- learning_feedback")
    print("- analysis_feedback")
    print("\nYou can now run the human review dashboard:")
    print("python human_review_dashboard.py")

def test_database_connection():
    """Test database connection and show current data."""
    
    supabase = create_client(
        os.getenv("SUPABASE_URL"),
        os.getenv("SUPABASE_SERVICE_KEY")
    )
    
    print("🔍 Testing database connection...")
    
    try:
        # Test customer_interactions
        result = supabase.table("customer_interactions").select("*").limit(5).execute()
        print(f"✅ customer_interactions: {len(result.data)} records found")
        
        # Show sample record structure
        if result.data:
            sample = result.data[0]
            print(f"   Sample record keys: {list(sample.keys())}")
            metadata = sample.get('metadata', {})
            if metadata:
                print(f"   Metadata keys: {list(metadata.keys())}")
                analysis = metadata.get('analysis', {})
                if analysis:
                    print(f"   Analysis keys: {list(analysis.keys())}")
        
        # Test learning_feedback
        try:
            result = supabase.table("learning_feedback").select("*").limit(1).execute()
            print(f"✅ learning_feedback: {len(result.data)} records found")
        except:
            print("⚠️ learning_feedback table not found")
        
        # Test analysis_feedback
        try:
            result = supabase.table("analysis_feedback").select("*").limit(1).execute()
            print(f"✅ analysis_feedback: {len(result.data)} records found")
        except:
            print("⚠️ analysis_feedback table not found")
            
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False
    
    print("✅ Database connection successful!")
    return True

def show_recent_emails():
    """Show recent emails for verification."""
    
    supabase = create_client(
        os.getenv("SUPABASE_URL"),
        os.getenv("SUPABASE_SERVICE_KEY")
    )
    
    print("\n📧 Recent emails in database:")
    
    try:
        result = supabase.table("customer_interactions").select(
            "customer_email, created_at, metadata"
        ).eq("interaction_type", "email").order("created_at", desc=True).limit(10).execute()
        
        for i, email in enumerate(result.data):
            metadata = email.get('metadata', {})
            analysis = metadata.get('analysis', {})
            
            print(f"{i+1}. From: {email.get('customer_email', 'unknown')}")
            print(f"   Subject: {metadata.get('subject', 'No subject')}")
            print(f"   Intent: {analysis.get('intent', 'unknown')}")
            print(f"   Urgency: {analysis.get('urgency', 'unknown')}")
            print(f"   Review Status: {metadata.get('review_status', 'unknown')}")
            print(f"   Time: {email.get('created_at', 'unknown')}")
            print()
            
    except Exception as e:
        print(f"❌ Could not fetch recent emails: {e}")

if __name__ == "__main__":
    print("🚀 HITL Database Setup Tool")
    print("=" * 50)
    
    # Test connection first
    if test_database_connection():
        print("\n" + "=" * 50)
        
        # Setup tables
        setup_database_tables()
        
        print("\n" + "=" * 50)
        
        # Show recent emails
        show_recent_emails()
        
        print("\n" + "=" * 50)
        print("🎯 Next steps:")
        print("1. Run: python human_review_dashboard.py")
        print("2. Test the review workflow")
        print("3. Add learning notes and feedback")
        print("4. Monitor system performance")
    else:
        print("❌ Please check your Supabase configuration and try again.")
