"""
Unit tests for the multi-agent system.
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime

from agents.email_monitor import EmailMonitoringAgent
from agents.customer_support import CustomerSupportAgent, SupportQuery
from agents.order_processing import OrderProcessingAgent, OrderRequest, OrderAction
from agents.purchasing import PurchasingAgent, PurchaseOrderRequest
from agents.orchestrator import AgentOrchestrator, WorkflowRequest, WorkflowType


@pytest.mark.unit
class TestEmailMonitoringAgent:
    """Test email monitoring agent."""
    
    @pytest.fixture
    async def email_agent(self, mock_gmail_service, mock_database_operations, mock_settings):
        """Create email monitoring agent with mocked dependencies."""
        with patch('agents.email_monitor.get_gmail_manager') as mock_get_gmail:
            mock_gmail_manager = AsyncMock()
            mock_gmail_manager.get_messages.return_value = [{"id": "test_msg_1"}]
            mock_gmail_manager.get_message_details.return_value = {
                "id": "test_msg_1",
                "from": "<EMAIL>",
                "subject": "Test Subject",
                "body": "Test body",
                "attachments": []
            }
            mock_get_gmail.return_value = mock_gmail_manager
            
            agent = EmailMonitoringAgent()
            await agent.initialize()
            return agent
    
    async def test_initialize(self, email_agent):
        """Test email agent initialization."""
        # Assert
        assert email_agent.gmail_manager is not None
        assert email_agent.running is False
    
    async def test_extract_customer_query(self, email_agent):
        """Test extracting customer query from email."""
        # Arrange
        subject = "Order status inquiry"
        body = "Hi, I would like to check the status of my order ORD001. Thanks!"
        
        # Act
        query = await email_agent._extract_customer_query(subject, body)
        
        # Assert
        assert "Order status inquiry" in query
        assert len(query) <= 500
    
    async def test_process_email(self, email_agent, mock_database_operations):
        """Test processing a single email."""
        # Arrange
        message_id = "test_msg_1"
        
        # Act
        await email_agent._process_email(message_id)
        
        # Assert
        mock_database_operations.log_email_processing.assert_called()
        mock_database_operations.create_interaction.assert_called()
    
    async def test_get_processing_stats(self, email_agent, mock_database_operations):
        """Test getting email processing statistics."""
        # Arrange
        mock_database_operations.get_unprocessed_emails.return_value = [
            {"processed": True}, {"processed": False}, {"processing_status": "error"}
        ]
        
        # Act
        stats = await email_agent.get_processing_stats()
        
        # Assert
        assert "total_emails_processed" in stats
        assert "total_errors" in stats
        assert "success_rate" in stats


@pytest.mark.unit
class TestCustomerSupportAgent:
    """Test customer support agent."""
    
    @pytest.fixture
    async def support_agent(self, mock_mistral_model, mock_database_operations, mock_settings):
        """Create customer support agent with mocked dependencies."""
        with patch('agents.customer_support.MistralModel', return_value=mock_mistral_model):
            with patch('agents.customer_support.Agent') as mock_agent_class:
                mock_agent = AsyncMock()
                mock_agent.run.return_value.data.response = "Test support response"
                mock_agent.run.return_value.data.confidence = 0.8
                mock_agent.run.return_value.data.requires_escalation = False
                mock_agent_class.return_value = mock_agent
                
                agent = CustomerSupportAgent()
                return agent
    
    async def test_process_query(self, support_agent, mock_database_operations):
        """Test processing a support query."""
        # Arrange
        query = SupportQuery(
            customer_email="<EMAIL>",
            query="What is the status of my order?",
            priority="medium"
        )
        
        # Act
        response = await support_agent.process_query(query)
        
        # Assert
        assert response.response == "Test support response"
        assert response.confidence == 0.8
        assert response.requires_escalation is False
    
    async def test_get_customer_context(self, support_agent, mock_database_operations):
        """Test getting customer context."""
        # Arrange
        mock_database_operations.get_interactions.return_value = [
            {"interaction_type": "email", "content": "Previous interaction"}
        ]
        mock_database_operations.get_customer_orders.return_value = [
            {"order_id": "ORD001", "status": "shipped"}
        ]
        
        # Act
        context = await support_agent._get_customer_context("<EMAIL>")
        
        # Assert
        assert context.customer_email == "<EMAIL>"
        assert len(context.recent_interactions) == 1
        assert len(context.order_history) == 1
    
    async def test_escalation_handling(self, support_agent, mock_database_operations):
        """Test escalation handling."""
        # Arrange
        query = SupportQuery(
            customer_email="<EMAIL>",
            query="I'm very angry about my order!",
            priority="high"
        )
        
        # Mock escalation response
        support_agent.agent.run.return_value.data.requires_escalation = True
        support_agent.agent.run.return_value.data.escalation_reason = "Customer complaint"
        
        # Act
        response = await support_agent.process_query(query)
        
        # Assert
        assert response.requires_escalation is True
        mock_database_operations.create_support_ticket.assert_called()


@pytest.mark.unit
class TestOrderProcessingAgent:
    """Test order processing agent."""
    
    @pytest.fixture
    async def order_agent(self, mock_mistral_model, mock_database_operations, mock_myob_api, mock_settings):
        """Create order processing agent with mocked dependencies."""
        with patch('agents.order_processing.MistralModel', return_value=mock_mistral_model):
            with patch('agents.order_processing.get_erp_client') as mock_get_erp:
                mock_erp_client = AsyncMock()
                mock_erp_client.create_sales_order.return_value = {"order_id": "ORD001"}
                mock_erp_client.get_sales_orders.return_value = [{"order_id": "ORD001", "status": "pending"}]
                mock_get_erp.return_value = mock_erp_client
                
                agent = OrderProcessingAgent()
                await agent.initialize()
                return agent
    
    async def test_create_order(self, order_agent, mock_database_operations):
        """Test creating a new order."""
        # Arrange
        request = OrderRequest(
            customer_email="<EMAIL>",
            action=OrderAction.CREATE,
            items=[{"sku": "PROD001", "quantity": 2}]
        )
        
        # Act
        response = await order_agent.process_order_request(request)
        
        # Assert
        assert response.success is True
        assert response.order_id == "ORD001"
        assert response.status == "pending"
    
    async def test_check_order_status(self, order_agent):
        """Test checking order status."""
        # Arrange
        request = OrderRequest(
            customer_email="<EMAIL>",
            action=OrderAction.STATUS_CHECK,
            order_id="ORD001"
        )
        
        # Act
        response = await order_agent.process_order_request(request)
        
        # Assert
        assert response.success is True
        assert response.order_id == "ORD001"
        assert response.status == "pending"
    
    async def test_apply_sku_mappings(self, order_agent):
        """Test applying SKU mappings to order items."""
        # Arrange
        items = [{"sku": "CUST-001", "quantity": 2}]
        
        with patch.object(order_agent.sku_manager, 'get_internal_sku', return_value="PROD001"):
            # Act
            mapped_items, mappings = await order_agent._apply_sku_mappings(items, "<EMAIL>")
            
            # Assert
            assert mapped_items[0]["sku"] == "PROD001"
            assert mapped_items[0]["original_sku"] == "CUST-001"
            assert len(mappings) == 1


@pytest.mark.unit
class TestPurchasingAgent:
    """Test purchasing agent."""
    
    @pytest.fixture
    async def purchasing_agent(self, mock_mistral_model, mock_database_operations, mock_myob_api, mock_settings):
        """Create purchasing agent with mocked dependencies."""
        with patch('agents.purchasing.MistralModel', return_value=mock_mistral_model):
            with patch('agents.purchasing.get_erp_client') as mock_get_erp:
                mock_erp_client = AsyncMock()
                mock_erp_client.get_stock_items.return_value = [
                    {"sku": "PROD001", "quantity_available": 5}
                ]
                mock_erp_client.get_stock_levels.return_value = {"quantity_available": 5}
                mock_erp_client.create_purchase_order.return_value = {"order_id": "PO001"}
                mock_get_erp.return_value = mock_erp_client
                
                agent = PurchasingAgent()
                await agent.initialize()
                return agent
    
    async def test_determine_alert_type(self, purchasing_agent):
        """Test determining alert type based on stock levels."""
        # Arrange
        item = {"reorder_point": 10}
        
        # Act - Out of stock
        alert_type = purchasing_agent._determine_alert_type(0, item)
        assert alert_type.value == "out_of_stock"
        
        # Act - Critical stock
        alert_type = purchasing_agent._determine_alert_type(3, item)
        assert alert_type.value == "critical_stock"
        
        # Act - Low stock
        alert_type = purchasing_agent._determine_alert_type(8, item)
        assert alert_type.value == "low_stock"
        
        # Act - Normal stock
        alert_type = purchasing_agent._determine_alert_type(15, item)
        assert alert_type is None
    
    async def test_create_purchase_order(self, purchasing_agent, mock_database_operations):
        """Test creating a purchase order."""
        # Arrange
        request = PurchaseOrderRequest(
            items=[{"sku": "PROD001", "quantity": 100, "unit_cost": 10.00}],
            supplier="Test Supplier",
            priority="high"
        )
        
        # Act
        response = await purchasing_agent.create_purchase_order(request)
        
        # Assert
        assert response.success is True
        assert response.purchase_order_id == "PO001"
        assert response.total_amount == 1000.00
    
    async def test_monitoring_cycle(self, purchasing_agent, mock_database_operations):
        """Test inventory monitoring cycle."""
        # Arrange
        purchasing_agent.settings.enable_auto_purchasing = False
        
        # Act
        await purchasing_agent._monitoring_cycle()
        
        # Assert
        # Should check stock items and create alerts if needed
        purchasing_agent.erp_client.get_stock_items.assert_called()


@pytest.mark.unit
class TestAgentOrchestrator:
    """Test agent orchestrator."""
    
    @pytest.fixture
    async def orchestrator(self, mock_langchain_agent, mock_database_operations, mock_settings):
        """Create orchestrator with mocked dependencies."""
        with patch('agents.orchestrator.ChatOpenAI') as mock_llm:
            with patch('agents.orchestrator.get_email_monitoring_agent') as mock_get_email:
                with patch('agents.orchestrator.get_customer_support_agent') as mock_get_support:
                    with patch('agents.orchestrator.get_order_processing_agent') as mock_get_order:
                        with patch('agents.orchestrator.get_purchasing_agent') as mock_get_purchasing:
                            
                            mock_get_email.return_value = AsyncMock()
                            mock_get_support.return_value = AsyncMock()
                            mock_get_order.return_value = AsyncMock()
                            mock_get_purchasing.return_value = AsyncMock()
                            
                            orchestrator = AgentOrchestrator()
                            await orchestrator.initialize()
                            return orchestrator
    
    async def test_execute_email_to_support_workflow(self, orchestrator, mock_database_operations):
        """Test email-to-support workflow execution."""
        # Arrange
        request = WorkflowRequest(
            workflow_type=WorkflowType.EMAIL_TO_SUPPORT,
            customer_email="<EMAIL>",
            input_data={
                "subject": "Order inquiry",
                "body": "I need help with my order",
                "extracted_query": "Order inquiry"
            }
        )
        
        with patch('agents.orchestrator.process_support_query') as mock_support:
            mock_support.return_value.requires_escalation = False
            mock_support.return_value.model_dump.return_value = {"response": "Test response"}
            
            with patch('agents.orchestrator.get_customer_insights') as mock_insights:
                mock_insights.return_value = {"insights": "test"}
                
                # Act
                response = await orchestrator.execute_workflow(request)
                
                # Assert
                assert response.status.value == "completed"
                assert response.result["workflow_type"] == "email_to_support"
    
    async def test_execute_order_processing_workflow(self, orchestrator, mock_database_operations):
        """Test order processing workflow execution."""
        # Arrange
        request = WorkflowRequest(
            workflow_type=WorkflowType.ORDER_PROCESSING,
            customer_email="<EMAIL>",
            input_data={
                "action": "create",
                "items": [{"sku": "PROD001", "quantity": 2}]
            }
        )
        
        with patch('agents.orchestrator.process_order') as mock_order:
            mock_order.return_value.success = True
            mock_order.return_value.model_dump.return_value = {"order_id": "ORD001"}
            
            with patch('agents.orchestrator.get_customer_insights') as mock_insights:
                mock_insights.return_value = {"insights": "test"}
                
                # Act
                response = await orchestrator.execute_workflow(request)
                
                # Assert
                assert response.status.value == "completed"
                assert response.result["workflow_type"] == "order_processing"
    
    async def test_workflow_error_handling(self, orchestrator):
        """Test workflow error handling."""
        # Arrange
        request = WorkflowRequest(
            workflow_type=WorkflowType.EMAIL_TO_SUPPORT,
            customer_email="<EMAIL>",
            input_data={}
        )
        
        with patch('agents.orchestrator.process_support_query', side_effect=Exception("Test error")):
            # Act
            response = await orchestrator.execute_workflow(request)
            
            # Assert
            assert response.status.value == "failed"
            assert "Test error" in response.error_message
    
    async def test_get_workflow_status(self, orchestrator):
        """Test getting workflow status."""
        # Arrange
        workflow_id = "test_workflow_1"
        orchestrator.active_workflows[workflow_id] = {
            "status": "running",
            "steps_completed": ["step1", "step2"]
        }
        
        # Act
        status = await orchestrator.get_workflow_status(workflow_id)
        
        # Assert
        assert status is not None
        assert status["status"] == "running"
        assert len(status["steps_completed"]) == 2
    
    async def test_cancel_workflow(self, orchestrator):
        """Test cancelling a workflow."""
        # Arrange
        workflow_id = "test_workflow_1"
        orchestrator.active_workflows[workflow_id] = {"status": "running"}
        
        # Act
        result = await orchestrator.cancel_workflow(workflow_id)
        
        # Assert
        assert result is True
        assert orchestrator.active_workflows[workflow_id]["status"] == "cancelled"
