# Configuration Guide

This guide covers all configuration options for the Multi-Agent Sales Support System.

## 📋 Environment Variables

### Core Configuration

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `ENVIRONMENT` | string | `development` | Environment mode (development/testing/production) |
| `DEBUG` | boolean | `true` | Enable debug mode |
| `LOG_LEVEL` | string | `INFO` | Logging level (DEBUG/INFO/WARNING/ERROR) |
| `APP_VERSION` | string | `1.0.0` | Application version |

### API Keys and Authentication

| Variable | Type | Required | Description |
|----------|------|----------|-------------|
| `MISTRAL_API_KEY` | string | Yes | Mistral AI API key for agent reasoning |
| `OPENAI_API_KEY` | string | Yes | OpenAI API key for LangChain orchestration |
| `GOOGLE_CLIENT_ID` | string | No | Google OAuth client ID for Gmail API |
| `GOOGLE_CLIENT_SECRET` | string | No | Google OAuth client secret |
| `JWT_SECRET_KEY` | string | Yes | Secret key for JWT token generation |

### Database Configuration

| Variable | Type | Required | Description |
|----------|------|----------|-------------|
| `SUPABASE_URL` | string | Yes | Supabase project URL |
| `SUPABASE_SERVICE_KEY` | string | Yes | Supabase service role key |
| `SUPABASE_ANON_KEY` | string | No | Supabase anonymous key |
| `DATABASE_POOL_SIZE` | integer | `10` | Database connection pool size |
| `DATABASE_TIMEOUT` | integer | `30` | Database connection timeout (seconds) |

### ERP Integration

| Variable | Type | Required | Description |
|----------|------|----------|-------------|
| `MYOB_EXO_API_URL` | string | Yes | MYOB EXO API base URL |
| `MYOB_EXO_API_KEY` | string | Yes | MYOB EXO API authentication key |
| `MYOB_EXO_TIMEOUT` | integer | `30` | API request timeout (seconds) |
| `MYOB_EXO_RETRY_ATTEMPTS` | integer | `3` | Number of retry attempts for failed requests |

### Knowledge Graph

| Variable | Type | Required | Description |
|----------|------|----------|-------------|
| `FALKORDB_HOST` | string | No | FalkorDB host address |
| `FALKORDB_PORT` | integer | `6379` | FalkorDB port |
| `FALKORDB_PASSWORD` | string | No | FalkorDB authentication password |
| `FALKORDB_DATABASE` | string | `sales_support` | FalkorDB database name |
| `FALKORDB_SSL` | boolean | `false` | Enable SSL for FalkorDB connection |

### MCP Server Configuration

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `MCP_SERVER_HOST` | string | `0.0.0.0` | MCP server bind address |
| `MCP_SERVER_PORT` | integer | `8000` | MCP server port |
| `MCP_SERVER_WORKERS` | integer | `4` | Number of worker processes |
| `MCP_SERVER_TIMEOUT` | integer | `60` | Request timeout (seconds) |

### Feature Toggles

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `ENABLE_EMAIL_MONITORING` | boolean | `true` | Enable email monitoring agent |
| `ENABLE_INVENTORY_MONITORING` | boolean | `true` | Enable inventory monitoring |
| `ENABLE_KNOWLEDGE_GRAPH` | boolean | `true` | Enable knowledge graph features |
| `ENABLE_AUTO_PURCHASING` | boolean | `false` | Enable automatic purchase order creation |
| `ENABLE_MCP_SERVER` | boolean | `true` | Enable MCP server |

### Agent Configuration

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `EMAIL_CHECK_INTERVAL` | integer | `300` | Email check interval (seconds) |
| `INVENTORY_CHECK_INTERVAL` | integer | `600` | Inventory check interval (seconds) |
| `HEALTH_CHECK_INTERVAL` | integer | `60` | Health check interval (seconds) |
| `SUPPORT_CONTEXT_LIMIT` | integer | `5` | Number of previous interactions to include in context |
| `ORDER_PROCESSING_TIMEOUT` | integer | `30` | Order processing timeout (seconds) |
| `INVENTORY_LOW_THRESHOLD` | integer | `10` | Default low inventory threshold |
| `ORDER_AUTO_APPROVAL_LIMIT` | float | `1000.0` | Auto-approval limit for orders |

### AI Model Configuration

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `MISTRAL_MODEL` | string | `mistral-large-latest` | Mistral model to use |
| `MISTRAL_MAX_TOKENS` | integer | `4000` | Maximum tokens for Mistral responses |
| `MISTRAL_TEMPERATURE` | float | `0.3` | Temperature for Mistral model |
| `OPENAI_MODEL` | string | `gpt-4` | OpenAI model for LangChain |
| `EMBEDDING_MODEL` | string | `text-embedding-ada-002` | Model for generating embeddings |

## 🔧 Configuration Files

### Environment File (.env)

```bash
# Core Configuration
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO
APP_VERSION=1.0.0

# API Keys
MISTRAL_API_KEY=your_mistral_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
JWT_SECRET_KEY=your_jwt_secret_key

# Database
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your_supabase_service_key
DATABASE_POOL_SIZE=10
DATABASE_TIMEOUT=30

# ERP Integration
MYOB_EXO_API_URL=https://your-myob-api.com
MYOB_EXO_API_KEY=your_myob_api_key
MYOB_EXO_TIMEOUT=30
MYOB_EXO_RETRY_ATTEMPTS=3

# Knowledge Graph
FALKORDB_HOST=localhost
FALKORDB_PORT=6379
FALKORDB_DATABASE=sales_support
FALKORDB_SSL=false

# MCP Server
MCP_SERVER_HOST=0.0.0.0
MCP_SERVER_PORT=8000
MCP_SERVER_WORKERS=4

# Feature Toggles
ENABLE_EMAIL_MONITORING=true
ENABLE_INVENTORY_MONITORING=true
ENABLE_KNOWLEDGE_GRAPH=true
ENABLE_AUTO_PURCHASING=false
ENABLE_MCP_SERVER=true

# Agent Configuration
EMAIL_CHECK_INTERVAL=300
INVENTORY_CHECK_INTERVAL=600
SUPPORT_CONTEXT_LIMIT=5
ORDER_PROCESSING_TIMEOUT=30
INVENTORY_LOW_THRESHOLD=10
ORDER_AUTO_APPROVAL_LIMIT=1000.0

# AI Models
MISTRAL_MODEL=mistral-large-latest
MISTRAL_MAX_TOKENS=4000
MISTRAL_TEMPERATURE=0.3
OPENAI_MODEL=gpt-4
```

### Application Settings (config/settings.py)

```python
from pydantic import BaseSettings, Field
from typing import Optional

class Settings(BaseSettings):
    # Core Configuration
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=True, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    
    # API Keys
    mistral_api_key: str = Field(..., env="MISTRAL_API_KEY")
    openai_api_key: str = Field(..., env="OPENAI_API_KEY")
    google_client_id: Optional[str] = Field(default=None, env="GOOGLE_CLIENT_ID")
    google_client_secret: Optional[str] = Field(default=None, env="GOOGLE_CLIENT_SECRET")
    jwt_secret_key: str = Field(..., env="JWT_SECRET_KEY")
    
    # Database
    supabase_url: str = Field(..., env="SUPABASE_URL")
    supabase_service_key: str = Field(..., env="SUPABASE_SERVICE_KEY")
    database_pool_size: int = Field(default=10, env="DATABASE_POOL_SIZE")
    database_timeout: int = Field(default=30, env="DATABASE_TIMEOUT")
    
    # ERP Integration
    myob_exo_api_url: str = Field(..., env="MYOB_EXO_API_URL")
    myob_exo_api_key: str = Field(..., env="MYOB_EXO_API_KEY")
    myob_exo_timeout: int = Field(default=30, env="MYOB_EXO_TIMEOUT")
    myob_exo_retry_attempts: int = Field(default=3, env="MYOB_EXO_RETRY_ATTEMPTS")
    
    # Knowledge Graph
    falkordb_host: str = Field(default="localhost", env="FALKORDB_HOST")
    falkordb_port: int = Field(default=6379, env="FALKORDB_PORT")
    falkordb_password: Optional[str] = Field(default=None, env="FALKORDB_PASSWORD")
    falkordb_database: str = Field(default="sales_support", env="FALKORDB_DATABASE")
    falkordb_ssl: bool = Field(default=False, env="FALKORDB_SSL")
    
    # Feature Toggles
    enable_email_monitoring: bool = Field(default=True, env="ENABLE_EMAIL_MONITORING")
    enable_inventory_monitoring: bool = Field(default=True, env="ENABLE_INVENTORY_MONITORING")
    enable_knowledge_graph: bool = Field(default=True, env="ENABLE_KNOWLEDGE_GRAPH")
    enable_auto_purchasing: bool = Field(default=False, env="ENABLE_AUTO_PURCHASING")
    enable_mcp_server: bool = Field(default=True, env="ENABLE_MCP_SERVER")
    
    # Agent Configuration
    email_check_interval: int = Field(default=300, env="EMAIL_CHECK_INTERVAL")
    inventory_check_interval: int = Field(default=600, env="INVENTORY_CHECK_INTERVAL")
    support_context_limit: int = Field(default=5, env="SUPPORT_CONTEXT_LIMIT")
    order_processing_timeout: int = Field(default=30, env="ORDER_PROCESSING_TIMEOUT")
    inventory_low_threshold: int = Field(default=10, env="INVENTORY_LOW_THRESHOLD")
    order_auto_approval_limit: float = Field(default=1000.0, env="ORDER_AUTO_APPROVAL_LIMIT")
    
    # AI Models
    mistral_model: str = Field(default="mistral-large-latest", env="MISTRAL_MODEL")
    mistral_max_tokens: int = Field(default=4000, env="MISTRAL_MAX_TOKENS")
    mistral_temperature: float = Field(default=0.3, env="MISTRAL_TEMPERATURE")
    openai_model: str = Field(default="gpt-4", env="OPENAI_MODEL")
    
    class Config:
        env_file = ".env"
        case_sensitive = False
```

## 🔐 Security Configuration

### API Key Management

```python
# Use environment variables or secrets management
import os
from cryptography.fernet import Fernet

class SecureConfig:
    def __init__(self):
        self.encryption_key = os.environ.get('ENCRYPTION_KEY')
        self.cipher = Fernet(self.encryption_key) if self.encryption_key else None
    
    def encrypt_api_key(self, api_key: str) -> str:
        if self.cipher:
            return self.cipher.encrypt(api_key.encode()).decode()
        return api_key
    
    def decrypt_api_key(self, encrypted_key: str) -> str:
        if self.cipher:
            return self.cipher.decrypt(encrypted_key.encode()).decode()
        return encrypted_key
```

### Database Security

```python
# Database connection with SSL
SUPABASE_CONFIG = {
    "url": os.environ["SUPABASE_URL"],
    "key": os.environ["SUPABASE_SERVICE_KEY"],
    "options": {
        "schema": "public",
        "auto_refresh_token": True,
        "persist_session": True,
        "detect_session_in_url": False,
        "headers": {
            "x-application-name": "sales-support-system"
        }
    }
}
```

## 🎛️ Environment-Specific Configuration

### Development Environment

```bash
# .env.development
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG

# Use test/development API keys
MISTRAL_API_KEY=test_mistral_key
OPENAI_API_KEY=test_openai_key

# Local database
SUPABASE_URL=http://localhost:54321
SUPABASE_SERVICE_KEY=local_service_key

# Disable certain features in development
ENABLE_EMAIL_MONITORING=false
ENABLE_AUTO_PURCHASING=false

# Faster intervals for testing
EMAIL_CHECK_INTERVAL=60
INVENTORY_CHECK_INTERVAL=120
```

### Testing Environment

```bash
# .env.testing
ENVIRONMENT=testing
DEBUG=true
LOG_LEVEL=DEBUG

# Mock API keys
MISTRAL_API_KEY=mock_mistral_key
OPENAI_API_KEY=mock_openai_key

# Test database
SUPABASE_URL=http://localhost:54321
SUPABASE_SERVICE_KEY=test_service_key

# Disable external integrations
ENABLE_EMAIL_MONITORING=false
ENABLE_KNOWLEDGE_GRAPH=false
ENABLE_MCP_SERVER=false
```

### Production Environment

```bash
# .env.production
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# Production API keys (use secrets management)
MISTRAL_API_KEY=${MISTRAL_API_KEY}
OPENAI_API_KEY=${OPENAI_API_KEY}

# Production database
SUPABASE_URL=${SUPABASE_URL}
SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}

# Enable all features
ENABLE_EMAIL_MONITORING=true
ENABLE_INVENTORY_MONITORING=true
ENABLE_KNOWLEDGE_GRAPH=true
ENABLE_AUTO_PURCHASING=true
ENABLE_MCP_SERVER=true

# Production intervals
EMAIL_CHECK_INTERVAL=300
INVENTORY_CHECK_INTERVAL=600
```

## 📊 Monitoring Configuration

### Logging Configuration

```python
# config/logging.py
import logging.config

LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "standard": {
            "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
        },
        "detailed": {
            "format": "%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s"
        },
        "json": {
            "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
            "format": "%(asctime)s %(name)s %(levelname)s %(message)s"
        }
    },
    "handlers": {
        "console": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "standard"
        },
        "file": {
            "level": "INFO",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": "logs/application.log",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
            "formatter": "detailed"
        }
    },
    "loggers": {
        "": {
            "handlers": ["console", "file"],
            "level": "INFO",
            "propagate": False
        },
        "agents": {
            "handlers": ["console", "file"],
            "level": "DEBUG",
            "propagate": False
        }
    }
}
```

### Health Check Configuration

```python
# Health check endpoints configuration
HEALTH_CHECK_CONFIG = {
    "database": {
        "enabled": True,
        "timeout": 5,
        "critical": True
    },
    "erp_api": {
        "enabled": True,
        "timeout": 10,
        "critical": True
    },
    "knowledge_graph": {
        "enabled": True,
        "timeout": 5,
        "critical": False
    },
    "agents": {
        "enabled": True,
        "timeout": 3,
        "critical": True
    }
}
```

## 🔄 Configuration Validation

### Validation Script

```python
# scripts/validate_config.py
import os
from typing import List, Dict, Any

def validate_configuration() -> Dict[str, Any]:
    """Validate system configuration."""
    errors = []
    warnings = []
    
    # Required environment variables
    required_vars = [
        "MISTRAL_API_KEY",
        "SUPABASE_URL",
        "SUPABASE_SERVICE_KEY",
        "MYOB_EXO_API_URL",
        "MYOB_EXO_API_KEY"
    ]
    
    for var in required_vars:
        if not os.environ.get(var):
            errors.append(f"Missing required environment variable: {var}")
    
    # Optional but recommended variables
    recommended_vars = [
        "OPENAI_API_KEY",
        "JWT_SECRET_KEY"
    ]
    
    for var in recommended_vars:
        if not os.environ.get(var):
            warnings.append(f"Missing recommended environment variable: {var}")
    
    # Validate URLs
    supabase_url = os.environ.get("SUPABASE_URL", "")
    if supabase_url and not supabase_url.startswith("https://"):
        warnings.append("SUPABASE_URL should use HTTPS in production")
    
    return {
        "valid": len(errors) == 0,
        "errors": errors,
        "warnings": warnings
    }

if __name__ == "__main__":
    result = validate_configuration()
    if result["valid"]:
        print("✅ Configuration is valid")
    else:
        print("❌ Configuration errors found:")
        for error in result["errors"]:
            print(f"  - {error}")
    
    if result["warnings"]:
        print("⚠️  Configuration warnings:")
        for warning in result["warnings"]:
            print(f"  - {warning}")
```

This configuration guide provides comprehensive documentation for all configuration options in the Multi-Agent Sales Support System, including environment-specific settings, security considerations, and validation procedures.
