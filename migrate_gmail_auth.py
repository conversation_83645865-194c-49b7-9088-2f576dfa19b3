#!/usr/bin/env python3
"""
Migration script to help transition from old Gmail authentication to new credentials.json method.
"""

import os
import shutil
from pathlib import Path


def main():
    """Run Gmail authentication migration."""
    print("🔄 Gmail Authentication Migration Script")
    print("=" * 50)
    
    print("This script will help you migrate from the old Gmail authentication method")
    print("to the new credentials.json-based authentication.\n")
    
    # Check current state
    print("📋 Checking current authentication setup...")
    
    # Check for old credential files
    old_files = [
        "credentials/gmail_credentials.json",
        "credentials/gmail_token.pickle"
    ]
    
    old_files_found = []
    for file_path in old_files:
        if os.path.exists(file_path):
            old_files_found.append(file_path)
            print(f"🔍 Found old file: {file_path}")
    
    # Check for new credential files
    new_files = [
        "credentials.json",
        "token.json", 
        "token.pickle"
    ]
    
    new_files_found = []
    for file_path in new_files:
        if os.path.exists(file_path):
            new_files_found.append(file_path)
            print(f"✅ Found new file: {file_path}")
    
    # Check environment variables
    print("\n📋 Checking environment variables...")
    
    old_env_vars = [
        "GOOGLE_CLIENT_ID",
        "GOOGLE_CLIENT_SECRET", 
        "GOOGLE_REDIRECT_URI"
    ]
    
    old_env_found = []
    for var_name in old_env_vars:
        if os.getenv(var_name):
            old_env_found.append(var_name)
            print(f"🔍 Found old env var: {var_name}")
    
    # Determine migration status
    print("\n🔍 Migration Status Analysis:")
    
    if not old_files_found and not old_env_found and "credentials.json" in new_files_found:
        print("✅ Already migrated! You're using the new authentication method.")
        print("   No action needed.")
        return
    
    if old_files_found or old_env_found:
        print("⚠️  Old authentication method detected.")
        print("   Migration recommended.")
    
    if "credentials.json" not in new_files_found:
        print("❌ credentials.json not found.")
        print("   You need to download this from Google Cloud Console.")
    
    # Migration steps
    print("\n🚀 Migration Steps:")
    print("=" * 30)
    
    print("1. 📥 Download credentials.json:")
    print("   - Go to Google Cloud Console")
    print("   - Navigate to APIs & Services → Credentials")
    print("   - Create OAuth 2.0 Client ID (Desktop application)")
    print("   - Download the JSON file")
    print("   - Rename to 'credentials.json'")
    print("   - Place in project root directory")
    
    if "credentials.json" in new_files_found:
        print("   ✅ credentials.json already exists")
    else:
        print("   ❌ credentials.json missing - download required")
    
    print("\n2. 🧹 Clean up old files:")
    if old_files_found:
        print("   The following old files can be safely deleted:")
        for file_path in old_files_found:
            print(f"   - {file_path}")
        
        response = input("\n   Delete old files now? (y/N): ").strip().lower()
        if response == 'y':
            for file_path in old_files_found:
                try:
                    os.remove(file_path)
                    print(f"   ✅ Deleted: {file_path}")
                except Exception as e:
                    print(f"   ❌ Failed to delete {file_path}: {e}")
        else:
            print("   Skipped deletion. You can delete these files manually later.")
    else:
        print("   ✅ No old files to clean up")
    
    print("\n3. 🔧 Update environment variables:")
    if old_env_found:
        print("   Remove these from your .env file:")
        for var_name in old_env_found:
            print(f"   - {var_name}")
        print("   These are no longer needed with credentials.json authentication.")
    else:
        print("   ✅ No old environment variables found")
    
    print("\n4. 🧪 Test new authentication:")
    print("   Run the test script to verify everything works:")
    print("   python test_gmail_auth.py")
    
    # Check if we can run the test
    if "credentials.json" in new_files_found:
        response = input("\n   Run authentication test now? (y/N): ").strip().lower()
        if response == 'y':
            print("\n🧪 Running authentication test...")
            try:
                import subprocess
                result = subprocess.run(
                    ["python", "test_gmail_auth.py"],
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    print("✅ Authentication test passed!")
                    print(result.stdout)
                else:
                    print("❌ Authentication test failed:")
                    print(result.stderr)
                    
            except Exception as e:
                print(f"❌ Failed to run test: {e}")
                print("   Please run 'python test_gmail_auth.py' manually")
    
    print("\n📚 Additional Resources:")
    print("   - docs/gmail_authentication_update.md - Detailed documentation")
    print("   - test_gmail_auth.py - Authentication test script")
    print("   - Google Cloud Console: https://console.cloud.google.com/")
    
    print("\n✨ Migration Summary:")
    print("=" * 30)
    
    if "credentials.json" in new_files_found:
        print("✅ credentials.json: Ready")
    else:
        print("❌ credentials.json: Download required")
    
    if not old_files_found:
        print("✅ Old files: Already cleaned up")
    else:
        print("⚠️  Old files: Manual cleanup recommended")
    
    if not old_env_found:
        print("✅ Environment: Already updated")
    else:
        print("⚠️  Environment: Manual cleanup recommended")
    
    print("\n🎉 Migration complete!")
    print("Your Gmail authentication is now using the official Google method.")


if __name__ == "__main__":
    main()
