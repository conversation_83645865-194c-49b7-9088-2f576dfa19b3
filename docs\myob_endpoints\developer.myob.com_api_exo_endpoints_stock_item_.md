---
url: "https://developer.myob.com/api/exo/endpoints/stock_item/"
title: "Stock Item"
---

[![](https://developer.myob.com/media/cms_page_media/23/myob_logo_small.png)](http://www.myob.com/)

# Stock Item

Return details on stock items.

**Date Released:** Oct 25th 2013 **Date Updated:** May 30th 2014

| URL | Supports |
| --- | --- |
| {URI}/stockitem/{id}<br>{URI}/stockitem/search?q={query}<br>{URI}/stockitem/{id}/image?height={h}&width={w} | [GET](https://developer.myob.com/api/exo/endpoints/stock_item/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/stock_item/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/stock_item/#POST)<br>[GET](https://developer.myob.com/api/exo/endpoints/stock_item/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/stock_item/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/stock_item/#POST)<br>[GET](https://developer.myob.com/api/exo/endpoints/stock_item/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/stock_item/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/stock_item/#POST) |
| {URI} is exo.api.myob.com when connecting to the cloud or the address of the machine hosting the API when connecting on premise. |

`search` returns stock items that match the specified search string.

`image` returns the stock item's image file with dimensions specified (in pixels) by the `height` and `width` parameters. The image’s aspect ratio is preserved if just one of the dimensions is specified. If no dimensions are supplied, the width defaults to 100 pixels (with the height calculated to preserve the aspect ratio). You can return the actual dimensions of the image with `width=auto` and/or `height=auto`.

**Note:** Actual fields returned may differ slightly depending on local settings and configuration.

The elements list below details information for Stock Item. To view the descriptions for the elements you can either hover any attribute to reveal details [or click here to show all details inline.](https://developer.myob.com/api/exo/endpoints/stock_item/#reveal)

#### Attribute Details

EXO API Schema Doc Helper

Host Address API Request
Developer Key Authorisation
EXO Token
- primarygroupid integer
- Required on PUT
- primarygroup object,null
- Type: object,null
  - active boolean
  - Type: boolean
  - name string,null
  - Type: string,null
  - rel string,null
  - Type: string,null
  - title string,null
  - Type: string,null
  - id integer
  - Required on PUT
  - href string,null
  - Type: string,null
- secondarygroupid integer
- Required on PUT
- secondarygroup object,null
- Type: object,null
  - name string,null
  - Type: string,null
  - rel string,null
  - Type: string,null
  - title string,null
  - Type: string,null
  - id integer
  - Required on PUT
  - href string,null
  - Type: string,null
- stockpricegroupid integer
- Required on PUT
- stockpricegroup object,null
- Type: object,null
  - name string,null
  - Type: string,null
  - rel string,null
  - Type: string,null
  - title string,null
  - Type: string,null
  - id integer
  - Required on PUT
  - href string,null
  - Type: string,null
- stockclassificationid integer
- Required on PUT
- stockclassification object,null
- Type: object,null
  - name string,null
  - Type: string,null
  - rel string,null
  - Type: string,null
  - title string,null
  - Type: string,null
  - id integer
  - Required on PUT
  - href string,null
  - Type: string,null
- suppliercreditoraccountid integer
- Required on PUT
- suppliercreditoraccountname string,null
- Type: string,null
- unitofmeasureid string,null
- Required on PUT
- unitofmeasure object,null
- Type: object,null
  - description string,null
  - Type: string,null
  - rel string,null
  - Type: string,null
  - title string,null
  - Type: string,null
  - id string
  - Required on PUT
  - href string,null
  - Type: string,null
- salestaxrateid integer
- Required on PUT
- salestaxrate object,null
- Type: object,null
  - name string,null
  - Type: string,null
  - shortname string,null
  - Type: string,null
  - rate number
  - Type: number
  - rel string,null
  - Type: string,null
  - title string,null
  - Type: string,null
  - id integer
  - Required on PUT
  - href string,null
  - Type: string,null
- serialnumberstracked boolean
- Type: boolean
- serialnumberstrackingtypeid integer
- Required on PUT
- serialnumberstrackingtypename string,null
- Type: string,null
- batchnumberstracked boolean
- Type: boolean
- discountable boolean
- Type: boolean
- canexpire boolean
- Type: boolean
- expireindays integer,null
- Type: integer,null
- restricted boolean
- Type: boolean
- totalinstock number
- Type: number
- barcode1 string,null
- Type: string,null
- barcode2 string,null
- Type: string,null
- barcode3 string,null
- Type: string,null
- showonweb boolean
- Type: boolean
- webdescription string,null
- Type: string,null
- webimagefilename string,null
- Type: string,null
- latestcost number
- Type: number
- suppliercost number
- Type: number
- standardcost number
- Type: number
- averagecost number
- Type: number
- stocktype string,null
- Type: string,null
- description string,null
- Type: string,null
- saleprices array,null
- Type: array,null
- active boolean
- Type: boolean
- notes string,null
- Type: string,null
- extrafields array,null
- Type: array,null
- rel string,null
- Type: string,null
- title string,null
- Type: string,null
- id string
- Required on PUT
- href string,null
- Type: string,null

#### Example json GET response

- {
  - stocklevels
    - {
      - 0
        - {
          - locationid : 1
          - physical : 16
          - free : -34
          - committed : 50
          - incoming : 0
          - notforsale : 0
          - onbackorder : 28
        - }
      - 1
        - {
          - locationid : 2
          - physical : 6
          - free : 16
          - committed : 0
          - incoming : 10
          - notforsale : 0
          - onbackorder : 0
        - }
      - 2
        - {
          - locationid : 3
          - physical : 1
          - free : 1
          - committed : 0
          - incoming : 0
          - notforsale : 0
          - onbackorder : 0
        - }
      - 3
        - {
          - locationid : 4
          - physical : 5
          - free : 5
          - committed : 10
          - incoming : 10
          - notforsale : 0
          - onbackorder : 0
        - }
      - 4
        - {
          - locationid : 5
          - physical : 5
          - free : 0
          - committed : 5
          - incoming : 0
          - notforsale : 5
          - onbackorder : 0
        - }
    - }
  - primarygroupid : 1
  - secondarygroupid : 0
  - stockpricegroupid : 0
  - stockclassificationid : 0
  - suppliercreditoraccountid : 8
  - suppliercreditoraccountname : Aussie Car Parts
  - unitofmeasureid : Each
  - salestaxrateid : -1
  - serialnumberstracked : false
  - serialnumberstrackingtypeid : 0
  - serialnumberstrackingtypename : NotSerialised
  - batchnumberstracked : false
  - discountable : true
  - canexpire : false
  - expireindays
  - restricted : false
  - totalinstock : 28
  - barcode1 : ***************
  - barcode2 : ****************
  - barcode3 : ***************
  - showonweb : true
  - webdescription : OVALCHROME AIR FILTER
  - webimagefilename : OVALAIR.JPG
  - latestcost : 49.99
  - suppliercost : 39.99
  - standardcost : 49.99
  - averagecost : 49.99
  - stocktype : PhysicalItem
  - description : Ovalchrome Air Filter
  - saleprices
    - {
      - 0
        - {
          - price : 62.04
          - id : 1
          - name : Internet
          - currencyid : 0
        - }
      - 1
        - {
          - price : 68.24
          - id : 2
          - name : Retail
          - currencyid : 0
        - }
      - 2
        - {
          - price : 61.416
          - id : 3
          - name : Trade
          - currencyid : 0
        - }
      - 3
        - {
          - price : 21.8368
          - id : 4
          - name : UK
          - currencyid : 0
        - }
      - 4
        - {
          - price : 30.708
          - id : 5
          - name : US
          - currencyid : 0
        - }
      - 5
        - {
          - price : 64.828
          - id : 6
          - name : Fiji
          - currencyid : 0
        - }
      - 6
        - {
          - price : 54.592
          - id : 7
          - name : New Zealand
          - currencyid : 1
        - }
      - 7
        - {
          - price : 53.5343
          - id : 8
          - name : Singapore
          - currencyid : 0
        - }
      - 8
        - {
          - price : 0
          - id : 9
          - name : ComputedSellPrice
          - currencyid : 0
        - }
      - 9
        - {
          - price : 0
          - id : 10
          - name :
          - currencyid : 0
        - }
    - }
  - active : true
  - notes : Aapproved and tested under strict guidance by the national Body of Automotive Association of New Zeland. Conforms to specifications by overseas Manufacturers of similar products
  - extrafields
    - {
      - 0
        - {
          - key : X\_SIZEID
          - value : 4
        - }
      - 1
        - {
          - key : X\_COLOURID
          - value : 2
        - }
      - 2
        - {
          - key : X\_DATE
          - value : 2015-05-28
        - }
      - 3
        - {
          - key : X\_DROPDOWN
          - value : 3\. LBLUE
        - }
      - 4
        - {
          - key : X\_INTEGER
          - value : 4094
        - }
      - 5
        - {
          - key : X\_TEXT
          - value : 435443545
        - }
      - 6
        - {
          - key : X\_CHECKBOX
          - value : Y
        - }
    - }
  - id : AIRFIL01
  - href : {URI}https://exo-stage.api.myob.com/stockitem/AIRFIL01
- }

{URI} is defined as: http://exo.api.myob.com/

|     |     |
| --- | --- |
|  |  |