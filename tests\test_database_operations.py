"""
Unit tests for database operations.
"""

import pytest
from datetime import datetime
from unittest.mock import AsyncMock, patch

from database.operations import DatabaseOperations
from database.schemas import (
    CustomerInteraction, SKUMapping, OrderRecord, OrderStatus,
    EmailProcessingLog, InventoryAlert, AgentMemory
)


@pytest.mark.unit
class TestDatabaseOperations:
    """Test database operations."""
    
    @pytest.fixture
    async def db_ops(self, mock_supabase_client):
        """Create database operations instance with mocked client."""
        with patch('database.operations.get_supabase_client', return_value=mock_supabase_client):
            db_ops = DatabaseOperations()
            return db_ops
    
    async def test_create_interaction(self, db_ops, mock_supabase_client, sample_interaction_data):
        """Test creating a customer interaction."""
        # Arrange
        interaction = CustomerInteraction(**sample_interaction_data)
        mock_supabase_client.table.return_value.insert.return_value.execute.return_value.data = [{"id": 1}]
        
        # Act
        result = await db_ops.create_interaction(interaction)
        
        # Assert
        assert result["id"] == 1
        mock_supabase_client.table.assert_called_with("customer_interactions")
    
    async def test_get_interactions(self, db_ops, mock_supabase_client):
        """Test getting customer interactions."""
        # Arrange
        expected_data = [
            {"id": 1, "customer_email": "<EMAIL>", "content": "Test interaction"}
        ]
        mock_supabase_client.table.return_value.select.return_value.eq.return_value.order.return_value.limit.return_value.execute.return_value.data = expected_data
        
        # Act
        result = await db_ops.get_interactions(customer_email="<EMAIL>", limit=10)
        
        # Assert
        assert result == expected_data
        mock_supabase_client.table.assert_called_with("customer_interactions")
    
    async def test_create_sku_mapping(self, db_ops, mock_supabase_client, sample_sku_mapping):
        """Test creating a SKU mapping."""
        # Arrange
        mapping = SKUMapping(**sample_sku_mapping)
        mock_supabase_client.table.return_value.upsert.return_value.execute.return_value.data = [{"id": 1}]
        
        # Act
        result = await db_ops.create_sku_mapping(mapping)
        
        # Assert
        assert result["id"] == 1
        mock_supabase_client.table.assert_called_with("sku_mappings")
    
    async def test_get_sku_mapping(self, db_ops, mock_supabase_client):
        """Test getting a SKU mapping."""
        # Arrange
        expected_mapping = {
            "customer_email": "<EMAIL>",
            "customer_sku": "CUST-001",
            "internal_sku": "PROD001"
        }
        mock_supabase_client.table.return_value.select.return_value.eq.return_value.eq.return_value.eq.return_value.execute.return_value.data = [expected_mapping]
        
        # Act
        result = await db_ops.get_sku_mapping("<EMAIL>", "CUST-001")
        
        # Assert
        assert result == expected_mapping
    
    async def test_create_order_record(self, db_ops, mock_supabase_client, sample_order_data):
        """Test creating an order record."""
        # Arrange
        order = OrderRecord(
            order_id=sample_order_data["order_id"],
            customer_email=sample_order_data["customer_email"],
            quantity=sample_order_data["items"][0]["quantity"],
            total_amount=sample_order_data["total_amount"],
            status=OrderStatus.PENDING
        )
        mock_supabase_client.table.return_value.insert.return_value.execute.return_value.data = [{"id": 1}]
        
        # Act
        result = await db_ops.create_order_record(order)
        
        # Assert
        assert result["id"] == 1
        mock_supabase_client.table.assert_called_with("order_records")
    
    async def test_update_order_status(self, db_ops, mock_supabase_client):
        """Test updating order status."""
        # Arrange
        mock_supabase_client.table.return_value.update.return_value.eq.return_value.execute.return_value.data = [{"id": 1}]
        
        # Act
        result = await db_ops.update_order_status("ORD001", "shipped", "Order shipped")
        
        # Assert
        assert result is True
        mock_supabase_client.table.assert_called_with("order_records")
    
    async def test_log_email_processing(self, db_ops, mock_supabase_client):
        """Test logging email processing."""
        # Arrange
        log_entry = EmailProcessingLog(
            message_id="test_msg_1",
            customer_email="<EMAIL>",
            subject="Test Subject",
            processed=True,
            processing_status="completed"
        )
        mock_supabase_client.table.return_value.insert.return_value.execute.return_value.data = [{"id": 1}]
        
        # Act
        result = await db_ops.log_email_processing(log_entry)
        
        # Assert
        assert result["id"] == 1
        mock_supabase_client.table.assert_called_with("email_processing_log")
    
    async def test_create_inventory_alert(self, db_ops, mock_supabase_client):
        """Test creating an inventory alert."""
        # Arrange
        alert = InventoryAlert(
            internal_sku="PROD001",
            current_quantity=5,
            threshold_quantity=10,
            alert_type="low_stock"
        )
        mock_supabase_client.table.return_value.insert.return_value.execute.return_value.data = [{"id": 1}]
        
        # Act
        result = await db_ops.create_inventory_alert(alert)
        
        # Assert
        assert result["id"] == 1
        mock_supabase_client.table.assert_called_with("inventory_alerts")
    
    async def test_store_agent_memory(self, db_ops, mock_supabase_client):
        """Test storing agent memory."""
        # Arrange
        memory = AgentMemory(
            agent_name="test_agent",
            context_type="test_context",
            context_data={"key": "value"},
            customer_email="<EMAIL>"
        )
        mock_supabase_client.table.return_value.insert.return_value.execute.return_value.data = [{"id": 1}]
        
        # Act
        result = await db_ops.store_agent_memory(memory)
        
        # Assert
        assert result["id"] == 1
        mock_supabase_client.table.assert_called_with("agent_memory")
    
    async def test_get_agent_memory(self, db_ops, mock_supabase_client):
        """Test getting agent memory."""
        # Arrange
        expected_data = [
            {"id": 1, "agent_name": "test_agent", "context_data": {"key": "value"}}
        ]
        mock_supabase_client.table.return_value.select.return_value.eq.return_value.or_.return_value.order.return_value.limit.return_value.execute.return_value.data = expected_data
        
        # Act
        result = await db_ops.get_agent_memory("test_agent", limit=10)
        
        # Assert
        assert result == expected_data
    
    async def test_search_interactions_semantic_no_vector_store(self, db_ops, mock_supabase_client):
        """Test semantic search when vector store is not available."""
        # Arrange
        db_ops.vector_store = None
        
        # Act
        result = await db_ops.search_interactions_semantic("test query")
        
        # Assert
        assert result == []
    
    async def test_cleanup_expired_memories(self, db_ops, mock_supabase_client):
        """Test cleaning up expired memories."""
        # Arrange
        mock_supabase_client.table.return_value.delete.return_value.lt.return_value.execute.return_value.data = [{"id": 1}, {"id": 2}]
        
        # Act
        result = await db_ops.cleanup_expired_memories()
        
        # Assert
        assert result == 2
        mock_supabase_client.table.assert_called_with("agent_memory")
    
    async def test_cleanup_old_interactions(self, db_ops, mock_supabase_client):
        """Test cleaning up old interactions."""
        # Arrange
        mock_supabase_client.table.return_value.delete.return_value.lt.return_value.execute.return_value.data = [{"id": 1}]
        
        # Act
        result = await db_ops.cleanup_old_interactions(days=365)
        
        # Assert
        assert result == 1
        mock_supabase_client.table.assert_called_with("customer_interactions")


@pytest.mark.unit
class TestDatabaseSchemas:
    """Test database schema models."""
    
    def test_customer_interaction_model(self, sample_interaction_data):
        """Test CustomerInteraction model validation."""
        # Act
        interaction = CustomerInteraction(**sample_interaction_data)
        
        # Assert
        assert interaction.customer_email == "<EMAIL>"
        assert interaction.interaction_type == "email"
        assert interaction.content == "Customer inquiry about order status"
        assert interaction.metadata["agent"] == "email_monitor"
    
    def test_sku_mapping_model(self, sample_sku_mapping):
        """Test SKUMapping model validation."""
        # Act
        mapping = SKUMapping(**sample_sku_mapping)
        
        # Assert
        assert mapping.customer_email == "<EMAIL>"
        assert mapping.customer_sku == "CUST-PROD-001"
        assert mapping.internal_sku == "PROD001"
        assert mapping.active is True
    
    def test_order_record_model(self):
        """Test OrderRecord model validation."""
        # Arrange
        order_data = {
            "order_id": "ORD001",
            "customer_email": "<EMAIL>",
            "quantity": 2,
            "total_amount": 20.00,
            "status": OrderStatus.PENDING
        }
        
        # Act
        order = OrderRecord(**order_data)
        
        # Assert
        assert order.order_id == "ORD001"
        assert order.customer_email == "<EMAIL>"
        assert order.quantity == 2
        assert order.total_amount == 20.00
        assert order.status == OrderStatus.PENDING
    
    def test_email_processing_log_model(self):
        """Test EmailProcessingLog model validation."""
        # Arrange
        log_data = {
            "message_id": "test_msg_1",
            "customer_email": "<EMAIL>",
            "subject": "Test Subject",
            "processed": True,
            "processing_status": "completed"
        }
        
        # Act
        log_entry = EmailProcessingLog(**log_data)
        
        # Assert
        assert log_entry.message_id == "test_msg_1"
        assert log_entry.customer_email == "<EMAIL>"
        assert log_entry.subject == "Test Subject"
        assert log_entry.processed is True
        assert log_entry.processing_status == "completed"
    
    def test_inventory_alert_model(self):
        """Test InventoryAlert model validation."""
        # Arrange
        alert_data = {
            "internal_sku": "PROD001",
            "current_quantity": 5,
            "threshold_quantity": 10,
            "alert_type": "low_stock"
        }
        
        # Act
        alert = InventoryAlert(**alert_data)
        
        # Assert
        assert alert.internal_sku == "PROD001"
        assert alert.current_quantity == 5
        assert alert.threshold_quantity == 10
        assert alert.alert_type == "low_stock"
        assert alert.status == "active"
    
    def test_agent_memory_model(self):
        """Test AgentMemory model validation."""
        # Arrange
        memory_data = {
            "agent_name": "test_agent",
            "context_type": "test_context",
            "context_data": {"key": "value"},
            "customer_email": "<EMAIL>"
        }
        
        # Act
        memory = AgentMemory(**memory_data)
        
        # Assert
        assert memory.agent_name == "test_agent"
        assert memory.context_type == "test_context"
        assert memory.context_data == {"key": "value"}
        assert memory.customer_email == "<EMAIL>"
