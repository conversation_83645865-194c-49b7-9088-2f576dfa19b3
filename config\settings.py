""" 
Configuration settings for the multi-agent sales support system.
"""

import os
from typing import List, Optional
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict
from pathlib import Path


class Settings(BaseSettings):
    """Application settings with environment variable support."""

    model_config = SettingsConfigDict(
        extra='ignore',
        env_file='.env',
        env_file_encoding='utf-8',
        case_sensitive=False
    )

    # Application
    app_name: str = Field(default="sales-support-system", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    environment: str = Field(default="development", env="ENVIRONMENT")
    
    # AI and LLM Configuration
    mistral_api_key: str = Field(..., env="MISTRAL_API_KEY")
    mistral_model: str = Field(default="mistral-large-latest", env="MISTRAL_MODEL")
    mistral_temperature: float = Field(default=0.7, env="MISTRAL_TEMPERATURE")
    mistral_max_tokens: int = Field(default=4096, env="MISTRAL_MAX_TOKENS")
    
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    openai_embedding_model: str = Field(default="text-embedding-3-small", env="OPENAI_EMBEDDING_MODEL")
    
    # Google Services - Gmail API
    # Note: Gmail authentication now uses credentials.json file instead of individual client ID/secret
    gmail_scopes: List[str] = Field(
        default=[
            "https://www.googleapis.com/auth/gmail.readonly",
            "https://www.googleapis.com/auth/gmail.send",
            "https://www.googleapis.com/auth/gmail.modify"
        ],
        env="GMAIL_SCOPES"
    )
    
    # MYOB EXO ERP
    myob_exo_api_url: str = Field(..., env="MYOB_EXO_API_URL")
    myob_exo_api_key: str = Field(..., env="MYOB_EXO_API_KEY")
    myob_exo_username: str = Field(..., env="MYOB_EXO_USERNAME")
    myob_exo_password: str = Field(..., env="MYOB_EXO_PASSWORD")
    myob_exo_token: str = Field(..., env="MYOB_EXO_TOKEN")
    myob_exo_database: Optional[str] = Field(default=None, env="MYOB_EXO_DATABASE")
    myob_exo_timeout: int = Field(default=30, env="MYOB_EXO_TIMEOUT")
    
    # Supabase
    supabase_url: str = Field(..., env="SUPABASE_URL")
    supabase_service_key: str = Field(..., env="SUPABASE_SERVICE_KEY")
    supabase_anon_key: Optional[str] = Field(default=None, env="SUPABASE_ANON_KEY")
    supabase_jwt_secret: Optional[str] = Field(default=None, env="SUPABASE_JWT_SECRET")
    
    # Database
    database_url: Optional[str] = Field(default=None, env="DATABASE_URL")
    database_pool_size: int = Field(default=10, env="DATABASE_POOL_SIZE")
    database_max_overflow: int = Field(default=20, env="DATABASE_MAX_OVERFLOW")
    
    # FalkorDB / Redis
    falkordb_host: str = Field(default="localhost", env="FALKORDB_HOST")
    falkordb_port: int = Field(default=6379, env="FALKORDB_PORT")
    falkordb_password: Optional[str] = Field(default=None, env="FALKORDB_PASSWORD")
    falkordb_database: str = Field(default="sales_graph", env="FALKORDB_DATABASE")
    falkordb_ssl: bool = Field(default=False, env="FALKORDB_SSL")
    falkordb_timeout: int = Field(default=30, env="FALKORDB_TIMEOUT")
    
    redis_url: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    redis_password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    redis_ssl: bool = Field(default=False, env="REDIS_SSL")
    
    # MCP Server
    mcp_server_host: str = Field(default="0.0.0.0", env="MCP_SERVER_HOST")
    mcp_server_port: int = Field(default=8000, env="MCP_SERVER_PORT")
    mcp_server_workers: int = Field(default=4, env="MCP_SERVER_WORKERS")
    mcp_server_timeout: int = Field(default=60, env="MCP_SERVER_TIMEOUT")
    mcp_server_max_requests: int = Field(default=1000, env="MCP_SERVER_MAX_REQUESTS")
    
    # Agent Configuration
    email_check_interval: int = Field(default=300, env="EMAIL_CHECK_INTERVAL")
    email_batch_size: int = Field(default=10, env="EMAIL_BATCH_SIZE")
    email_max_attachments: int = Field(default=5, env="EMAIL_MAX_ATTACHMENTS")
    email_attachment_max_size: int = Field(default=10485760, env="EMAIL_ATTACHMENT_MAX_SIZE")
    
    support_context_limit: int = Field(default=5, env="SUPPORT_CONTEXT_LIMIT")
    support_response_timeout: int = Field(default=30, env="SUPPORT_RESPONSE_TIMEOUT")
    support_escalation_threshold: int = Field(default=3, env="SUPPORT_ESCALATION_THRESHOLD")
    
    order_processing_timeout: int = Field(default=60, env="ORDER_PROCESSING_TIMEOUT")
    order_batch_size: int = Field(default=20, env="ORDER_BATCH_SIZE")
    order_retry_attempts: int = Field(default=3, env="ORDER_RETRY_ATTEMPTS")
    
    inventory_check_interval: int = Field(default=3600, env="INVENTORY_CHECK_INTERVAL")
    inventory_low_threshold: int = Field(default=50, env="INVENTORY_LOW_THRESHOLD")
    purchase_order_batch_size: int = Field(default=100, env="PURCHASE_ORDER_BATCH_SIZE")
    
    # Vector Database
    vector_dimension: int = Field(default=1536, env="VECTOR_DIMENSION")
    vector_similarity_threshold: float = Field(default=0.8, env="VECTOR_SIMILARITY_THRESHOLD")
    vector_max_results: int = Field(default=10, env="VECTOR_MAX_RESULTS")
    
    embedding_batch_size: int = Field(default=100, env="EMBEDDING_BATCH_SIZE")
    embedding_cache_ttl: int = Field(default=3600, env="EMBEDDING_CACHE_TTL")
    
    # Security
    jwt_secret_key: str = Field(..., env="JWT_SECRET_KEY")
    jwt_algorithm: str = Field(default="HS256", env="JWT_ALGORITHM")
    jwt_expiration_hours: int = Field(default=24, env="JWT_EXPIRATION_HOURS")
    
    api_rate_limit: int = Field(default=100, env="API_RATE_LIMIT")
    api_cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        env="API_CORS_ORIGINS"
    )
    api_allowed_hosts: List[str] = Field(
        default=["localhost", "127.0.0.1"],
        env="API_ALLOWED_HOSTS"
    )
    
    encryption_key: Optional[str] = Field(default=None, env="ENCRYPTION_KEY")
    
    # Monitoring and Logging
    log_format: str = Field(default="json", env="LOG_FORMAT")
    log_file_path: str = Field(default="logs/sales_support.log", env="LOG_FILE_PATH")
    log_rotation_size: str = Field(default="100MB", env="LOG_ROTATION_SIZE")
    log_retention_days: int = Field(default=30, env="LOG_RETENTION_DAYS")
    
    prometheus_port: int = Field(default=9090, env="PROMETHEUS_PORT")
    metrics_enabled: bool = Field(default=True, env="METRICS_ENABLED")
    health_check_interval: int = Field(default=60, env="HEALTH_CHECK_INTERVAL")
    
    sentry_dsn: Optional[str] = Field(default=None, env="SENTRY_DSN")
    sentry_environment: str = Field(default="development", env="SENTRY_ENVIRONMENT")
    
    # File Processing
    pdf_max_size: int = Field(default=50485760, env="PDF_MAX_SIZE")
    pdf_temp_dir: str = Field(default="temp/pdfs", env="PDF_TEMP_DIR")
    pdf_cleanup_interval: int = Field(default=3600, env="PDF_CLEANUP_INTERVAL")
    
    upload_dir: str = Field(default="uploads", env="UPLOAD_DIR")
    temp_dir: str = Field(default="temp", env="TEMP_DIR")
    max_file_size: int = Field(default=104857600, env="MAX_FILE_SIZE")
    
    # Performance
    max_concurrent_requests: int = Field(default=50, env="MAX_CONCURRENT_REQUESTS")
    connection_pool_size: int = Field(default=20, env="CONNECTION_POOL_SIZE")
    request_timeout: int = Field(default=30, env="REQUEST_TIMEOUT")
    
    cache_ttl: int = Field(default=3600, env="CACHE_TTL")
    cache_max_size: int = Field(default=1000, env="CACHE_MAX_SIZE")
    
    # Feature Flags
    enable_email_monitoring: bool = Field(default=True, env="ENABLE_EMAIL_MONITORING")
    enable_pdf_processing: bool = Field(default=True, env="ENABLE_PDF_PROCESSING")
    enable_knowledge_graph: bool = Field(default=True, env="ENABLE_KNOWLEDGE_GRAPH")
    enable_vector_search: bool = Field(default=True, env="ENABLE_VECTOR_SEARCH")
    enable_auto_purchasing: bool = Field(default=False, env="ENABLE_AUTO_PURCHASING")
    enable_escalation: bool = Field(default=True, env="ENABLE_ESCALATION")
    
    # Business Logic
    sku_mapping_cache_ttl: int = Field(default=7200, env="SKU_MAPPING_CACHE_TTL")
    sku_mapping_auto_create: bool = Field(default=False, env="SKU_MAPPING_AUTO_CREATE")
    sku_mapping_validation: str = Field(default="strict", env="SKU_MAPPING_VALIDATION")
    
    order_auto_approval_limit: float = Field(default=1000.00, env="ORDER_AUTO_APPROVAL_LIMIT")
    order_currency: str = Field(default="USD", env="ORDER_CURRENCY")
    order_tax_rate: float = Field(default=0.10, env="ORDER_TAX_RATE")
    
    customer_interaction_retention_days: int = Field(default=365, env="CUSTOMER_INTERACTION_RETENTION_DAYS")
    customer_context_limit: int = Field(default=100, env="CUSTOMER_CONTEXT_LIMIT")
    
    # Temporarily disabled field validators to debug parsing issues
    # @field_validator("gmail_scopes", mode="before")
    # @classmethod
    # def parse_gmail_scopes(cls, v):
    #     if isinstance(v, str):
    #         return [scope.strip() for scope in v.split(",")]
    #     return v

    # @field_validator("api_cors_origins", mode="before")
    # @classmethod
    # def parse_cors_origins(cls, v):
    #     if isinstance(v, str):
    #         return [origin.strip() for origin in v.split(",")]
    #     return v

    # @field_validator("api_allowed_hosts", mode="before")
    # @classmethod
    # def parse_allowed_hosts(cls, v):
    #     if isinstance(v, str):
    #         return [host.strip() for host in v.split(",")]
    #     return v
    
    # Old Config class removed - using model_config instead


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get the application settings."""
    return settings


def create_directories():
    """Create necessary directories if they don't exist."""
    directories = [
        settings.upload_dir,
        settings.temp_dir,
        settings.pdf_temp_dir,
        "logs",
        "credentials"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
