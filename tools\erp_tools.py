"""
ERP integration tools for MYOB EXO API interactions.
"""

import asyncio
import base64
import logging
import time
from typing import Dict, Any, List, Optional, Union
import httpx
from datetime import datetime

from config.settings import get_settings
from database.operations import get_database_operations
from database.schemas import APIUsageLog

logger = logging.getLogger(__name__)


class ERPError(Exception):
    """Custom exception for ERP operations."""
    pass


class ERPClient:
    """Client for MYOB EXO ERP API interactions."""
    
    def __init__(self):
        """Initialize ERP client."""
        self.settings = get_settings()
        self.db_ops = get_database_operations()
        self.base_url = self.settings.myob_exo_api_url.rstrip('/')
        self.client: Optional[httpx.AsyncClient] = None
    
    async def _get_client(self) -> httpx.AsyncClient:
        """Get or create HTTP client."""
        if self.client is None:
            self.client = httpx.AsyncClient(
                timeout=httpx.Timeout(self.settings.myob_exo_timeout),
                limits=httpx.Limits(max_connections=self.settings.connection_pool_size)
            )
        return self.client
    
    async def _make_request_with_retry(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        customer_email: Optional[str] = None,
        retry_on_auth_error: bool = True
    ) -> Dict[str, Any]:
        """
        Make request with automatic token refresh on authentication errors.
        """
        try:
            return await self._make_request(method, endpoint, params, data, customer_email)

        except ERPError as e:
            # Check if it's an authentication error and we should retry
            if retry_on_auth_error and ("401" in str(e) or "unauthorized" in str(e).lower()):
                logger.warning("Authentication error detected, attempting token refresh")

                try:
                    # Refresh the token
                    new_token = await self.refresh_token()

                    # Update the settings with new token (this would need to be persisted)
                    # For now, we'll just log it - in production you'd want to update your config
                    logger.info(f"New token obtained: {new_token[:20]}...")

                    # Retry the original request once with the new token
                    return await self._make_request(method, endpoint, params, data, customer_email)

                except Exception as refresh_error:
                    logger.error(f"Token refresh failed: {refresh_error}")
                    raise ERPError(f"Authentication failed and token refresh unsuccessful: {refresh_error}")

            # Re-raise the original error if not an auth error or retry disabled
            raise

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        customer_email: Optional[str] = None
    ) -> Dict[str, Any]:
        """Make authenticated request to MYOB EXO API."""
        start_time = time.time()
        client = await self._get_client()
        
        # Prepare MYOB EXO API authentication headers
        # Base64 encode username:password for Basic Auth
        auth_string = f"{self.settings.myob_exo_username}:{self.settings.myob_exo_password}"
        encoded_auth = base64.b64encode(auth_string.encode()).decode()

        headers = {
            "Authorization": f"Basic {encoded_auth}",
            "x-myobapi-key": self.settings.myob_exo_api_key,
            "x-myobapi-exotoken": self.settings.myob_exo_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == "GET":
                response = await client.get(url, headers=headers, params=params)
            elif method.upper() == "POST":
                response = await client.post(url, headers=headers, params=params, json=data)
            elif method.upper() == "PUT":
                response = await client.put(url, headers=headers, params=params, json=data)
            elif method.upper() == "PATCH":
                response = await client.patch(url, headers=headers, params=params, json=data)
            else:
                raise ERPError(f"Unsupported HTTP method: {method}")
            
            response_time_ms = (time.time() - start_time) * 1000
            
            # Log API usage
            await self._log_api_usage(
                endpoint=endpoint,
                method=method,
                status_code=response.status_code,
                response_time_ms=response_time_ms,
                customer_email=customer_email,
                error_message=None if response.is_success else response.text
            )
            
            if response.is_success:
                return response.json() if response.content else {}
            else:
                error_msg = f"MYOB EXO API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                raise ERPError(error_msg)
        
        except httpx.RequestError as e:
            response_time_ms = (time.time() - start_time) * 1000
            await self._log_api_usage(
                endpoint=endpoint,
                method=method,
                status_code=0,
                response_time_ms=response_time_ms,
                customer_email=customer_email,
                error_message=str(e)
            )
            logger.error(f"MYOB EXO API request error: {e}")
            raise ERPError(f"API request failed: {e}")
    
    async def _log_api_usage(
        self,
        endpoint: str,
        method: str,
        status_code: int,
        response_time_ms: float,
        customer_email: Optional[str] = None,
        error_message: Optional[str] = None
    ) -> None:
        """Log API usage for monitoring."""
        try:
            usage_log = APIUsageLog(
                api_name="myob_exo",
                endpoint=endpoint,
                method=method,
                status_code=status_code,
                response_time_ms=response_time_ms,
                customer_email=customer_email,
                agent_name="erp_tools",
                error_message=error_message
            )
            
            await self.db_ops.log_api_usage(usage_log)
            
        except Exception as e:
            logger.error(f"Failed to log API usage: {e}")

    async def refresh_token(self) -> str:
        """
        Refresh the MYOB EXO token.

        MYOB EXO tokens expire annually and need to be refreshed.
        A call to the token endpoint automatically refreshes the token.

        Returns:
            New refreshed token string
        """
        try:
            logger.info("Refreshing MYOB EXO token")

            response_data = await self._make_request(
                method="GET",
                endpoint="/token"
            )

            # The response should contain the refreshed token
            if isinstance(response_data, str):
                new_token = response_data
            elif isinstance(response_data, dict) and "token" in response_data:
                new_token = response_data["token"]
            else:
                raise ERPError("Invalid token refresh response format")

            logger.info("MYOB EXO token refreshed successfully")
            return new_token

        except Exception as e:
            logger.error(f"Failed to refresh MYOB EXO token: {e}")
            raise ERPError(f"Token refresh failed: {e}")

    async def validate_token(self) -> bool:
        """
        Validate if the current token is still valid.

        Returns:
            True if token is valid, False otherwise
        """
        try:
            # Try a simple API call to validate token
            await self._make_request(
                method="GET",
                endpoint="/discovery"
            )
            return True

        except ERPError as e:
            if "401" in str(e) or "unauthorized" in str(e).lower():
                logger.warning("MYOB EXO token appears to be invalid or expired")
                return False
            # Re-raise other errors
            raise

    # Sales Order Operations
    async def get_sales_orders(
        self,
        customer_email: Optional[str] = None,
        order_id: Optional[str] = None,
        status: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get sales orders from MYOB EXO."""
        try:
            params = {"limit": limit}
            
            if order_id:
                params["order_id"] = order_id
            if status:
                params["status"] = status
            
            response = await self._make_request_with_retry(
                method="GET",
                endpoint="/salesorder",
                params=params,
                customer_email=customer_email
            )
            
            return response.get("orders", [])
            
        except Exception as e:
            logger.error(f"Error getting sales orders: {e}")
            raise ERPError(f"Failed to get sales orders: {e}")
    
    async def create_sales_order(
        self,
        order_data: Dict[str, Any],
        customer_email: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create a new sales order in MYOB EXO."""
        try:
            response = await self._make_request_with_retry(
                method="POST",
                endpoint="/salesorder",
                data=order_data,
                customer_email=customer_email
            )
            
            logger.info(f"Created sales order: {response.get('order_id', 'unknown')}")
            return response
            
        except Exception as e:
            logger.error(f"Error creating sales order: {e}")
            raise ERPError(f"Failed to create sales order: {e}")
    
    async def update_sales_order(
        self,
        order_id: str,
        update_data: Dict[str, Any],
        customer_email: Optional[str] = None
    ) -> Dict[str, Any]:
        """Update an existing sales order."""
        try:
            response = await self._make_request(
                method="PUT",
                endpoint=f"/salesorder/{order_id}",
                data=update_data,
                customer_email=customer_email
            )
            
            logger.info(f"Updated sales order: {order_id}")
            return response
            
        except Exception as e:
            logger.error(f"Error updating sales order {order_id}: {e}")
            raise ERPError(f"Failed to update sales order: {e}")
    
    # Stock Operations
    async def get_stock_items(
        self,
        sku: Optional[str] = None,
        search_term: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get stock items from MYOB EXO."""
        try:
            params = {"limit": limit}
            
            if sku:
                params["sku"] = sku
            if search_term:
                params["search"] = search_term
            
            response = await self._make_request(
                method="GET",
                endpoint="/stock",
                params=params
            )
            
            return response.get("stock", [])
            
        except Exception as e:
            logger.error(f"Error getting stock items: {e}")
            raise ERPError(f"Failed to get stock items: {e}")
    
    async def get_stock_levels(self, sku: str) -> Dict[str, Any]:
        """Get stock levels for a specific SKU."""
        try:
            response = await self._make_request(
                method="GET",
                endpoint=f"/stock_item",
                params={"sku": sku}
            )
            
            return response.get("items", [{}])[0] if response.get("items") else {}
            
        except Exception as e:
            logger.error(f"Error getting stock levels for {sku}: {e}")
            raise ERPError(f"Failed to get stock levels: {e}")
    
    # Purchase Order Operations
    async def get_purchase_orders(
        self,
        order_id: Optional[str] = None,
        status: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get purchase orders from MYOB EXO."""
        try:
            params = {"limit": limit}
            
            if order_id:
                params["order_id"] = order_id
            if status:
                params["status"] = status
            
            response = await self._make_request(
                method="GET",
                endpoint="/purchaseorder",
                params=params
            )
            
            return response.get("orders", [])
            
        except Exception as e:
            logger.error(f"Error getting purchase orders: {e}")
            raise ERPError(f"Failed to get purchase orders: {e}")
    
    async def create_purchase_order(
        self,
        order_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create a new purchase order in MYOB EXO."""
        try:
            response = await self._make_request(
                method="POST",
                endpoint="/purchaseorder",
                data=order_data
            )
            
            logger.info(f"Created purchase order: {response.get('order_id', 'unknown')}")
            return response
            
        except Exception as e:
            logger.error(f"Error creating purchase order: {e}")
            raise ERPError(f"Failed to create purchase order: {e}")
    
    # Customer/Debtor Operations
    async def get_customers(
        self,
        customer_id: Optional[str] = None,
        email: Optional[str] = None,
        search_term: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get customers/debtors from MYOB EXO."""
        try:
            params = {"limit": limit}
            
            if customer_id:
                params["customer_id"] = customer_id
            if email:
                params["email"] = email
            if search_term:
                params["search"] = search_term
            
            response = await self._make_request(
                method="GET",
                endpoint="/debtor",
                params=params
            )
            
            return response.get("debtors", [])
            
        except Exception as e:
            logger.error(f"Error getting customers: {e}")
            raise ERPError(f"Failed to get customers: {e}")
    
    async def get_customer_transactions(
        self,
        customer_id: str,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get customer transaction history."""
        try:
            params = {"customer_id": customer_id, "limit": limit}
            
            response = await self._make_request(
                method="GET",
                endpoint="/debtordebtortrans",
                params=params
            )
            
            return response.get("transactions", [])
            
        except Exception as e:
            logger.error(f"Error getting customer transactions: {e}")
            raise ERPError(f"Failed to get customer transactions: {e}")
    
    # Pricing Operations
    async def get_item_pricing(
        self,
        sku: str,
        customer_id: Optional[str] = None,
        quantity: Optional[int] = None
    ) -> Dict[str, Any]:
        """Get pricing information for an item."""
        try:
            params = {"sku": sku}
            
            if customer_id:
                params["customer_id"] = customer_id
            if quantity:
                params["quantity"] = quantity
            
            response = await self._make_request(
                method="GET",
                endpoint="/stock_itemstock_bestprice",
                params=params
            )
            
            return response.get("prices", [{}])[0] if response.get("prices") else {}
            
        except Exception as e:
            logger.error(f"Error getting item pricing for {sku}: {e}")
            raise ERPError(f"Failed to get item pricing: {e}")
    
    # Report Operations
    async def run_report(
        self,
        report_name: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Run a report in MYOB EXO."""
        try:
            data = {"report_name": report_name}
            if parameters:
                data["parameters"] = parameters
            
            response = await self._make_request(
                method="POST",
                endpoint="/reportrunreport",
                data=data
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Error running report {report_name}: {e}")
            raise ERPError(f"Failed to run report: {e}")
    
    async def close(self) -> None:
        """Close the HTTP client."""
        if self.client:
            await self.client.aclose()
            self.client = None


# Global ERP client instance
_erp_client: Optional[ERPClient] = None


async def get_erp_client() -> ERPClient:
    """Get the global ERP client instance."""
    global _erp_client
    
    if _erp_client is None:
        _erp_client = ERPClient()
    
    return _erp_client


# Convenience functions
async def get_order_status(order_id: str, customer_email: Optional[str] = None) -> Dict[str, Any]:
    """Get order status from ERP."""
    client = await get_erp_client()
    orders = await client.get_sales_orders(order_id=order_id, customer_email=customer_email)
    return orders[0] if orders else {}


async def create_order(order_data: Dict[str, Any], customer_email: Optional[str] = None) -> Dict[str, Any]:
    """Create a new order in ERP."""
    client = await get_erp_client()
    return await client.create_sales_order(order_data, customer_email)


async def get_stock_level(sku: str) -> Dict[str, Any]:
    """Get stock level for a SKU."""
    client = await get_erp_client()
    return await client.get_stock_levels(sku)


async def get_customer_info(customer_email: str) -> Dict[str, Any]:
    """Get customer information by email."""
    client = await get_erp_client()
    customers = await client.get_customers(email=customer_email)
    return customers[0] if customers else {}


async def get_item_price(sku: str, customer_id: Optional[str] = None, quantity: Optional[int] = None) -> Dict[str, Any]:
    """Get item pricing."""
    client = await get_erp_client()
    return await client.get_item_pricing(sku, customer_id, quantity)
