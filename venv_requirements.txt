aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.12.14
aiosignal==1.4.0
alembic==1.16.4
annotated-types==0.7.0
anthropic==0.60.0
anyio==4.9.0
appdirs==1.4.4
asgiref==3.9.1
asttokens==3.0.0
asyncio-mqtt==0.16.2
asyncpg==0.30.0
attrs==25.3.0
Authlib==1.6.1
backoff==2.2.1
bcrypt==4.3.0
beautifulsoup4==4.13.4
black==25.1.0
blinker==1.9.0
browserbase==1.4.0
build==1.2.2.post1
cachetools==6.1.0
certifi==2025.7.14
cffi==1.17.1
cfgv==3.4.0
charset-normalizer==3.4.2
chroma-hnswlib==0.7.6
chromadb==1.0.15
click==8.2.1
cohere==5.16.1
colorama==0.4.6
coloredlogs==15.0.1
crewai==0.150.0
crewai-tools==0.58.0
cryptography==45.0.5
dataclasses-json==0.6.7
decorator==5.2.1
deprecation==2.1.0
diskcache==5.6.3
distlib==0.4.0
distro==1.9.0
dnspython==2.7.0
docker==7.1.0
docstring_parser==0.17.0
durationpy==0.10
dynaconf==3.2.11
ecdsa==0.19.1
email_validator==2.2.0
embedchain==0.1.128
et_xmlfile==2.0.0
eval_type_backport==0.2.2
executing==2.2.0
factory_boy==3.3.3
Faker==37.4.2
FalkorDB==1.2.0
fastapi==0.116.1
fastavro==1.11.1
filelock==3.18.0
filetype==1.2.0
flake8==7.3.0
flatbuffers==25.2.10
frozenlist==1.7.0
fsspec==2025.7.0
google-ai-generativelanguage==0.6.18
google-api-core==2.25.1
google-api-python-client==2.177.0
google-auth==2.40.3
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.2.2
google-generativeai==0.8.5
googleapis-common-protos==1.70.0
gotrue==2.12.3
gptcache==0.1.44
graphiti-core==0.18.0
greenlet==3.2.3
griffe==1.9.0
groq==0.30.0
grpcio==1.74.0
grpcio-health-checking==1.74.0
grpcio-status==1.74.0
h11==0.16.0
h2==4.2.0
hpack==4.1.0
httpcore==1.0.9
httplib2==0.22.0
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.34.2
humanfriendly==10.0
hyperframe==6.1.0
identify==2.6.12
idna==3.10
importlib_metadata==8.7.0
importlib_resources==6.5.2
iniconfig==2.1.0
instructor==1.10.0
ipython==9.4.0
ipython_pygments_lexers==1.1.1
isort==6.0.1
jedi==0.19.2
Jinja2==3.1.6
jiter==0.10.0
json5==0.12.0
json_repair==0.48.0
jsonpatch==1.33
jsonpickle==4.1.1
jsonpointer==3.0.0
jsonref==1.1.0
jsonschema==4.25.0
jsonschema-specifications==2025.4.1
kubernetes==33.1.0
lancedb==0.24.2
langchain==0.3.27
langchain-anthropic==0.3.17
langchain-chroma==0.2.5
langchain-cohere==0.4.4
langchain-community==0.3.27
langchain-core==0.3.72
langchain-experimental==0.3.4
langchain-google-genai==2.1.8
langchain-groq==0.3.6
langchain-openai==0.3.28
langchain-text-splitters==0.3.9
langsmith==0.4.8
litellm==1.74.9
logfire-api==4.0.0
Mako==1.3.10
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib-inline==0.1.7
mccabe==0.7.0
mdurl==0.1.2
mem0ai==0.1.115
mistralai==1.9.3
mmh3==5.1.0
monotonic==1.6
mpmath==1.3.0
msgpack==1.1.1
multidict==6.6.3
mypy==1.17.0
mypy_extensions==1.1.0
neo4j==5.28.1
networkx==3.5
nodeenv==1.9.1
numpy==2.3.2
oauthlib==3.3.1
onnxruntime==1.22.1
openai==1.97.1
openpyxl==3.1.5
opentelemetry-api==1.35.0
opentelemetry-exporter-otlp-proto-common==1.35.0
opentelemetry-exporter-otlp-proto-grpc==1.35.0
opentelemetry-exporter-otlp-proto-http==1.35.0
opentelemetry-instrumentation==0.56b0
opentelemetry-instrumentation-asgi==0.56b0
opentelemetry-instrumentation-fastapi==0.56b0
opentelemetry-proto==1.35.0
opentelemetry-sdk==1.35.0
opentelemetry-semantic-conventions==0.56b0
opentelemetry-util-http==0.56b0
orjson==3.11.1
overrides==7.7.0
packaging==25.0
paho-mqtt==2.1.0
pandas==2.3.1
parso==0.8.4
passlib==1.7.4
pathspec==0.12.1
pdfminer.six==20250506
pdfplumber==0.11.7
pgvector==0.4.1
pillow==11.3.0
pinecone==7.3.0
pinecone-plugin-assistant==1.7.0
pinecone-plugin-interface==0.0.7
platformdirs==4.3.8
playwright==1.54.0
pluggy==1.6.0
portalocker==3.2.0
postgrest==1.1.1
posthog==6.3.1
pre_commit==4.2.0
prometheus_client==0.22.1
prompt_toolkit==3.0.51
propcache==0.3.2
proto-plus==1.26.1
protobuf==6.31.1
psutil==7.0.0
psycopg2-binary==2.9.10
pure_eval==0.2.3
pyarrow==21.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pybase64==1.4.2
pycodestyle==2.14.0
pycparser==2.22
pydantic==2.11.7
pydantic-ai-slim==0.4.8
pydantic-graph==0.4.8
pydantic-settings==2.10.1
pydantic_core==2.33.2
pyee==13.0.0
pyflakes==3.4.0
Pygments==2.19.2
PyJWT==2.10.1
PyMuPDF==1.26.3
pymupdf4llm==0.0.27
pyparsing==3.2.3
pypdf==5.9.0
pypdfium2==4.30.0
PyPika==0.48.9
pyproject_hooks==1.2.0
pyreadline3==3.5.4
pyright==1.1.403
pysbd==0.3.4
pytest==8.4.1
pytest-asyncio==1.1.0
pytest-mock==3.14.1
python-dateutil==2.9.0.post0
python-decouple==3.8
python-dotenv==1.1.1
python-jose==3.5.0
python-magic==0.4.27
python-multipart==0.0.20
pytube==15.0.0
pytz==2025.2
pyvis==0.3.2
pywin32==311
PyYAML==6.0.2
qdrant-client==1.15.0
realtime==2.7.0
redis==6.2.0
referencing==0.36.2
regex==2024.11.6
requests==2.32.4
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rich==14.1.0
rpds-py==0.26.0
rsa==4.9.1
schema==0.7.7
sentry-sdk==2.33.2
shellingham==1.5.4
six==1.17.0
sniffio==1.3.1
soupsieve==2.7
SQLAlchemy==2.0.41
stack-data==0.6.3
stagehand==0.4.1
starlette==0.47.2
storage3==0.12.0
StrEnum==0.4.15
structlog==25.4.0
supabase==2.17.0
supafunc==0.10.1
sympy==1.14.0
tabulate==0.9.0
tenacity==9.1.2
tiktoken==0.9.0
tokenizers==0.21.4
tomli==2.2.1
tomli_w==1.2.0
tqdm==4.67.1
traitlets==5.14.3
typer==0.16.0
types-PyYAML==6.0.12.20250516
types-requests==2.32.4.20250611
typing-inspect==0.9.0
typing-inspection==0.4.1
typing_extensions==4.14.1
tzdata==2025.2
uritemplate==4.2.0
urllib3==2.5.0
uv==0.8.3
uvicorn==0.35.0
validators==0.35.0
virtualenv==20.32.0
watchfiles==1.1.0
wcwidth==0.2.13
weaviate-client==4.16.4
websocket-client==1.8.0
websockets==15.0.1
wrapt==1.17.2
xlsxwriter==3.2.5
yarl==1.20.1
zipp==3.23.0
zstandard==0.23.0
