version: '3.8'

services:
  # FalkorDB - Redis-based graph database
  falkordb:
    image: falkordb/falkordb:latest
    container_name: sales-support-falkordb
    ports:
      - "6379:6379"
      - "3000:3000"  # FalkorDB Browser UI
    environment:
      - FALKORDB_PASSWORD=${FALKORDB_PASSWORD:-}
    volumes:
      - falkordb_data:/data
    networks:
      - sales-support-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching (separate from FalkorDB)
  redis:
    image: redis:7-alpine
    container_name: sales-support-redis
    ports:
      - "6380:6379"
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    volumes:
      - redis_data:/data
    networks:
      - sales-support-network
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD:-}
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL for local development (optional - use if not using Supabase)
  postgres:
    image: postgres:15-alpine
    container_name: sales-support-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=sales_support
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${DATABASE_PASSWORD:-postgres}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    networks:
      - sales-support-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MCP Server
  mcp-server:
    build:
      context: .
      dockerfile: Dockerfile.mcp
    container_name: sales-support-mcp
    ports:
      - "8000:8000"
    environment:
      - MYOB_EXO_API_URL=${MYOB_EXO_API_URL}
      - MYOB_EXO_API_KEY=${MYOB_EXO_API_KEY}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      - FALKORDB_HOST=falkordb
      - FALKORDB_PORT=6379
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - falkordb
      - redis
    networks:
      - sales-support-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Main Application
  sales-support-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sales-support-main
    ports:
      - "8080:8080"
    environment:
      - MISTRAL_API_KEY=${MISTRAL_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      - FALKORDB_HOST=falkordb
      - FALKORDB_PORT=6379
      - MCP_SERVER_URL=http://mcp-server:8000
    volumes:
      - ./credentials:/app/credentials:ro
      - ./logs:/app/logs
      - ./temp:/app/temp
    depends_on:
      - mcp-server
      - falkordb
      - redis
    networks:
      - sales-support-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus for monitoring (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: sales-support-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - sales-support-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Grafana for dashboards (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: sales-support-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - sales-support-network
    restart: unless-stopped
    profiles:
      - monitoring

networks:
  sales-support-network:
    driver: bridge
    name: sales-support-network

volumes:
  falkordb_data:
    name: sales-support-falkordb-data
  redis_data:
    name: sales-support-redis-data
  postgres_data:
    name: sales-support-postgres-data
  prometheus_data:
    name: sales-support-prometheus-data
  grafana_data:
    name: sales-support-grafana-data
