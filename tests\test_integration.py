"""
Integration tests for the multi-agent sales support system.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock

from agents.orchestrator import execute_workflow
from agents.customer_support import process_support_query
from agents.order_processing import process_order
from mcp_server.sku_mapping import create_sku_mapping
from knowledge_graph.relationships import build_customer_relationships


@pytest.mark.integration
class TestEndToEndWorkflows:
    """Test end-to-end workflows."""
    
    @pytest.fixture
    async def setup_mocks(self, mock_database_operations, mock_myob_api, mock_supabase_client):
        """Setup comprehensive mocks for integration tests."""
        # Mock database responses
        mock_database_operations.get_interactions.return_value = [
            {
                "customer_email": "<EMAIL>",
                "interaction_type": "email",
                "content": "Previous order inquiry",
                "metadata": {"agent": "email_monitor"}
            }
        ]
        
        mock_database_operations.get_customer_orders.return_value = [
            {
                "order_id": "ORD001",
                "customer_email": "<EMAIL>",
                "status": "shipped",
                "total_amount": 100.00
            }
        ]
        
        mock_database_operations.get_sku_mapping.return_value = {
            "customer_sku": "CUST-001",
            "internal_sku": "PROD001"
        }
        
        # Mock ERP responses
        mock_myob_api.get.return_value.json.return_value = {
            "orders": [{"order_id": "ORD001", "status": "shipped"}],
            "stock": [{"sku": "PROD001", "quantity_available": 50}]
        }
        
        mock_myob_api.post.return_value.json.return_value = {
            "order_id": "ORD002",
            "status": "pending"
        }
        
        return {
            "db": mock_database_operations,
            "erp": mock_myob_api,
            "supabase": mock_supabase_client
        }
    
    async def test_email_to_order_workflow(self, setup_mocks):
        """Test complete email-to-order workflow."""
        # Arrange
        email_data = {
            "from": "<EMAIL>",
            "subject": "New order request",
            "body": "I would like to order 5 units of CUST-001",
            "extracted_query": "Order 5 units of CUST-001"
        }
        
        with patch('agents.orchestrator.get_customer_insights') as mock_insights:
            mock_insights.return_value = {"cross_sell_opportunities": []}
            
            with patch('agents.orchestrator.process_support_query') as mock_support:
                mock_support.return_value.response = "I can help you with that order"
                mock_support.return_value.confidence = 0.9
                mock_support.return_value.requires_escalation = False
                mock_support.return_value.model_dump.return_value = {
                    "response": "I can help you with that order",
                    "confidence": 0.9,
                    "requires_escalation": False
                }
                
                # Act
                response = await execute_workflow(
                    workflow_type="email_to_support",
                    customer_email="<EMAIL>",
                    input_data=email_data
                )
                
                # Assert
                assert response.status.value == "completed"
                assert "email_to_support" in response.result["workflow_type"]
                assert len(response.steps_completed) > 0
    
    async def test_order_creation_with_sku_mapping(self, setup_mocks):
        """Test order creation with automatic SKU mapping."""
        # Arrange
        order_data = {
            "action": "create",
            "items": [
                {"sku": "CUST-001", "quantity": 5, "unit_price": 20.00}
            ]
        }
        
        with patch('agents.order_processing.get_erp_client') as mock_get_erp:
            mock_erp = AsyncMock()
            mock_erp.create_sales_order.return_value = {"order_id": "ORD002"}
            mock_erp.get_stock_levels.return_value = {"quantity_available": 50}
            mock_erp.get_item_pricing.return_value = {"unit_price": 20.00}
            mock_get_erp.return_value = mock_erp
            
            with patch('agents.order_processing.get_sku_mapping_manager') as mock_get_sku:
                mock_sku_manager = AsyncMock()
                mock_sku_manager.get_internal_sku.return_value = "PROD001"
                mock_get_sku.return_value = mock_sku_manager
                
                # Act
                response = await process_order(
                    customer_email="<EMAIL>",
                    action="create",
                    items=order_data["items"]
                )
                
                # Assert
                assert response.success is True
                assert response.order_id == "ORD002"
                assert len(response.sku_mappings_applied) > 0
    
    async def test_customer_support_with_context(self, setup_mocks):
        """Test customer support with full context."""
        # Arrange
        query = "What's the status of my recent order?"
        
        with patch('agents.customer_support.get_sku_mapping_manager') as mock_get_sku:
            mock_sku_manager = AsyncMock()
            mock_sku_manager.get_customer_mappings.return_value = [
                {"customer_sku": "CUST-001", "internal_sku": "PROD001"}
            ]
            mock_get_sku.return_value = mock_sku_manager
            
            with patch('pydantic_ai.Agent') as mock_agent_class:
                mock_agent = AsyncMock()
                mock_agent.run.return_value.data.response = "Your order ORD001 has been shipped"
                mock_agent.run.return_value.data.confidence = 0.95
                mock_agent.run.return_value.data.requires_escalation = False
                mock_agent.run.return_value.data.suggested_actions = ["Track package"]
                mock_agent.run.return_value.data.referenced_orders = ["ORD001"]
                mock_agent.run.return_value.data.referenced_skus = []
                mock_agent_class.return_value = mock_agent
                
                # Act
                response = await process_support_query(
                    customer_email="<EMAIL>",
                    query=query
                )
                
                # Assert
                assert response.response == "Your order ORD001 has been shipped"
                assert response.confidence == 0.95
                assert "ORD001" in response.referenced_orders
    
    async def test_inventory_alert_to_purchase_order(self, setup_mocks):
        """Test inventory alert triggering purchase order creation."""
        # Arrange
        with patch('agents.purchasing.get_erp_client') as mock_get_erp:
            mock_erp = AsyncMock()
            mock_erp.get_stock_items.return_value = [
                {"sku": "PROD001", "quantity_available": 2, "reorder_point": 10}
            ]
            mock_erp.get_stock_levels.return_value = {"quantity_available": 2}
            mock_erp.create_purchase_order.return_value = {"order_id": "PO001"}
            mock_get_erp.return_value = mock_erp
            
            with patch('agents.purchasing.PurchasingAgent._generate_purchase_recommendation') as mock_recommend:
                mock_recommend.return_value.sku = "PROD001"
                mock_recommend.return_value.recommended_quantity = 100
                mock_recommend.return_value.priority = "high"
                mock_recommend.return_value.estimated_cost = 1000.00
                
                from agents.purchasing import PurchasingAgent
                agent = PurchasingAgent()
                await agent.initialize()
                
                # Act
                await agent._monitoring_cycle()
                
                # Assert
                setup_mocks["db"].create_inventory_alert.assert_called()
    
    async def test_knowledge_graph_integration(self, setup_mocks):
        """Test knowledge graph integration with customer relationships."""
        # Arrange
        with patch('knowledge_graph.relationships.get_graphiti_manager') as mock_get_graphiti:
            mock_graphiti = AsyncMock()
            mock_graphiti.record_customer_order.return_value = None
            mock_graphiti.query_customer_relationships.return_value = [
                {
                    "customer": {"email": "<EMAIL>"},
                    "relationship": {"type": "PLACED_ORDER"},
                    "target": {"order_id": "ORD001"}
                }
            ]
            mock_get_graphiti.return_value = mock_graphiti
            
            # Act
            result = await build_customer_relationships("<EMAIL>")
            
            # Assert
            assert result["customer_email"] == "<EMAIL>"
            assert result["total_orders"] > 0
    
    async def test_cross_sell_workflow(self, setup_mocks):
        """Test cross-sell opportunity workflow."""
        # Arrange
        with patch('agents.orchestrator.get_customer_insights') as mock_insights:
            mock_insights.return_value = {
                "cross_sell_opportunities": [
                    {
                        "type": "product_recommendation",
                        "recommended_product": {"sku": "PROD002", "name": "Related Product"},
                        "confidence": 0.8,
                        "basis": "Frequently bought together"
                    }
                ]
            }
            
            # Act
            response = await execute_workflow(
                workflow_type="cross_sell",
                customer_email="<EMAIL>",
                input_data={}
            )
            
            # Assert
            assert response.status.value == "completed"
            assert len(response.result["cross_sell_opportunities"]) > 0
    
    async def test_error_handling_and_recovery(self, setup_mocks):
        """Test error handling and recovery mechanisms."""
        # Arrange - Simulate database failure
        setup_mocks["db"].get_interactions.side_effect = Exception("Database connection failed")
        
        # Act
        response = await execute_workflow(
            workflow_type="customer_inquiry",
            customer_email="<EMAIL>",
            input_data={"query": "Test query"}
        )
        
        # Assert
        assert response.status.value == "failed"
        assert "Database connection failed" in response.error_message


@pytest.mark.integration
class TestSystemIntegration:
    """Test system-wide integration scenarios."""
    
    async def test_concurrent_workflow_execution(self, mock_database_operations):
        """Test concurrent execution of multiple workflows."""
        # Arrange
        workflows = []
        for i in range(5):
            workflows.append(
                execute_workflow(
                    workflow_type="customer_inquiry",
                    customer_email=f"customer{i}@example.com",
                    input_data={"query": f"Test query {i}"}
                )
            )
        
        with patch('agents.orchestrator.process_support_query') as mock_support:
            mock_support.return_value.response = "Test response"
            mock_support.return_value.confidence = 0.8
            mock_support.return_value.requires_escalation = False
            mock_support.return_value.model_dump.return_value = {
                "response": "Test response",
                "confidence": 0.8
            }
            
            # Act
            responses = await asyncio.gather(*workflows, return_exceptions=True)
            
            # Assert
            assert len(responses) == 5
            successful_responses = [r for r in responses if not isinstance(r, Exception)]
            assert len(successful_responses) > 0
    
    async def test_data_consistency_across_components(self, setup_mocks):
        """Test data consistency across different components."""
        # Arrange
        customer_email = "<EMAIL>"
        
        # Act 1: Create SKU mapping
        with patch('mcp_server.sku_mapping.get_database_operations', return_value=setup_mocks["db"]):
            sku_result = await create_sku_mapping(
                customer_email=customer_email,
                customer_sku="CUST-CONSISTENCY",
                internal_sku="PROD-CONSISTENCY"
            )
        
        # Act 2: Use SKU mapping in order
        with patch('agents.order_processing.get_erp_client') as mock_get_erp:
            mock_erp = AsyncMock()
            mock_erp.create_sales_order.return_value = {"order_id": "ORD-CONSISTENCY"}
            mock_get_erp.return_value = mock_erp
            
            with patch('agents.order_processing.get_sku_mapping_manager') as mock_get_sku:
                mock_sku_manager = AsyncMock()
                mock_sku_manager.get_internal_sku.return_value = "PROD-CONSISTENCY"
                mock_get_sku.return_value = mock_sku_manager
                
                order_result = await process_order(
                    customer_email=customer_email,
                    action="create",
                    items=[{"sku": "CUST-CONSISTENCY", "quantity": 1}]
                )
        
        # Assert
        assert sku_result["id"] == 1  # SKU mapping created
        assert order_result.success is True  # Order created successfully
        assert len(order_result.sku_mappings_applied) > 0  # SKU mapping was applied
    
    async def test_performance_under_load(self, mock_database_operations):
        """Test system performance under load."""
        import time
        
        # Arrange
        start_time = time.time()
        num_requests = 10
        
        tasks = []
        for i in range(num_requests):
            task = execute_workflow(
                workflow_type="customer_inquiry",
                customer_email=f"load_test_{i}@example.com",
                input_data={"query": f"Load test query {i}"}
            )
            tasks.append(task)
        
        with patch('agents.orchestrator.process_support_query') as mock_support:
            mock_support.return_value.response = "Load test response"
            mock_support.return_value.confidence = 0.8
            mock_support.return_value.requires_escalation = False
            mock_support.return_value.model_dump.return_value = {
                "response": "Load test response"
            }
            
            # Act
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            # Assert
            execution_time = end_time - start_time
            successful_results = [r for r in results if not isinstance(r, Exception)]
            
            assert len(successful_results) == num_requests
            assert execution_time < 30  # Should complete within 30 seconds
            
            # Check average response time
            avg_response_time = sum(r.execution_time_ms for r in successful_results) / len(successful_results)
            assert avg_response_time < 5000  # Average response time under 5 seconds
