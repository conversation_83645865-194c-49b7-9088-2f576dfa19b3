#!/usr/bin/env python3
"""
Test script to validate Gmail API authentication setup using credentials.json.
"""

import async<PERSON>
import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from tools.gmail_tools import GmailManager, GmailError


async def test_gmail_authentication():
    """Test Gmail API authentication."""
    print("🔧 Testing Gmail API Authentication...")
    print("=" * 50)
    
    try:
        # Check for required files
        required_files = [
            "credentials.json"
        ]
        
        print("📋 Checking required files...")
        for file_name in required_files:
            if os.path.exists(file_name):
                print(f"✅ {file_name}: Found")
            else:
                print(f"❌ {file_name}: Missing")
                print(f"   Please download {file_name} from Google Cloud Console")
                return False
        
        # Check for existing token files
        token_files = [
            "token.json",
            "token.pickle"
        ]
        
        print("\n📋 Checking existing token files...")
        for file_name in token_files:
            if os.path.exists(file_name):
                print(f"✅ {file_name}: Found (will be used for authentication)")
            else:
                print(f"⚠️  {file_name}: Not found (will be created during OAuth flow)")
        
        print("\n🔐 Initializing Gmail Manager...")
        
        # Initialize Gmail manager
        gmail_manager = GmailManager()
        
        print("🔑 Getting credentials...")
        await gmail_manager.initialize()
        
        print("✅ Gmail API authentication successful!")
        
        print("\n📧 Testing basic Gmail API operations...")
        
        # Test getting labels
        try:
            if gmail_manager.service:
                results = gmail_manager.service.users().labels().list(userId='me').execute()
                labels = results.get('labels', [])
                
                print(f"📄 Found {len(labels)} Gmail labels:")
                for i, label in enumerate(labels[:10]):  # Show first 10 labels
                    print(f"   {i+1}. {label['name']} (ID: {label['id']})")
                
                if len(labels) > 10:
                    print(f"   ... and {len(labels) - 10} more labels")
                
            else:
                print("❌ Gmail service not initialized")
                return False
                
        except Exception as e:
            print(f"❌ Failed to get Gmail labels: {e}")
            return False
        
        print("\n🧪 Testing Gmail manager methods...")
        
        # Test getting unread messages
        try:
            messages = await gmail_manager.get_messages(
                query="is:unread",
                max_results=5
            )
            print(f"📬 Found {len(messages)} unread messages")
            
        except Exception as e:
            print(f"⚠️  Could not get unread messages: {e}")
        
        print("\n🎉 All Gmail authentication tests passed!")
        
        # Check token files were created
        print("\n📋 Checking created token files...")
        for file_name in token_files:
            if os.path.exists(file_name):
                file_size = os.path.getsize(file_name)
                print(f"✅ {file_name}: Created ({file_size} bytes)")
            else:
                print(f"⚠️  {file_name}: Not created")
        
        return True
        
    except GmailError as e:
        print(f"❌ Gmail authentication error: {e}")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_gmail_scopes():
    """Test Gmail API scopes."""
    print("\n🔍 Testing Gmail API Scopes...")
    print("=" * 40)
    
    try:
        from config.settings import get_settings
        settings = get_settings()
        
        print("📋 Configured Gmail scopes:")
        for i, scope in enumerate(settings.gmail_scopes, 1):
            print(f"   {i}. {scope}")
        
        # Validate scopes
        valid_scopes = [
            "https://www.googleapis.com/auth/gmail.readonly",
            "https://www.googleapis.com/auth/gmail.send",
            "https://www.googleapis.com/auth/gmail.modify",
            "https://www.googleapis.com/auth/gmail.compose"
        ]
        
        print("\n🔍 Scope validation:")
        for scope in settings.gmail_scopes:
            if scope in valid_scopes:
                print(f"✅ {scope}: Valid")
            else:
                print(f"⚠️  {scope}: Unknown scope")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to test scopes: {e}")
        return False


async def test_credentials_file():
    """Test credentials.json file format."""
    print("\n📄 Testing credentials.json file...")
    print("=" * 40)
    
    try:
        import json
        
        if not os.path.exists("credentials.json"):
            print("❌ credentials.json file not found")
            return False
        
        with open("credentials.json", 'r') as f:
            creds_data = json.load(f)
        
        print("✅ credentials.json is valid JSON")
        
        # Check for required fields
        if "web" in creds_data:
            web_config = creds_data["web"]
            required_fields = [
                "client_id",
                "client_secret", 
                "auth_uri",
                "token_uri"
            ]
            
            print("🔍 Checking required fields:")
            for field in required_fields:
                if field in web_config:
                    value = web_config[field]
                    if field in ["client_secret"]:
                        display_value = f"{value[:10]}..." if len(value) > 10 else "***"
                    else:
                        display_value = value
                    print(f"✅ {field}: {display_value}")
                else:
                    print(f"❌ {field}: Missing")
                    return False
            
            print("✅ credentials.json format is correct")
            return True
            
        elif "installed" in creds_data:
            print("⚠️  Found 'installed' app credentials (desktop app)")
            print("   This should work but 'web' credentials are preferred")
            return True
            
        else:
            print("❌ Invalid credentials.json format")
            print("   Expected 'web' or 'installed' configuration")
            return False
            
    except json.JSONDecodeError as e:
        print(f"❌ credentials.json is not valid JSON: {e}")
        return False
        
    except Exception as e:
        print(f"❌ Error reading credentials.json: {e}")
        return False


async def main():
    """Run all Gmail authentication tests."""
    print("🚀 Gmail API Authentication Test Suite")
    print("=" * 60)
    
    # Test credentials file
    creds_success = await test_credentials_file()
    
    if not creds_success:
        print("\n❌ Credentials file test failed!")
        print("\n🔧 Please ensure:")
        print("   1. Download credentials.json from Google Cloud Console")
        print("   2. Place it in the project root directory")
        print("   3. Ensure it contains valid OAuth 2.0 client configuration")
        return
    
    # Test scopes
    await test_gmail_scopes()
    
    # Test authentication
    auth_success = await test_gmail_authentication()
    
    if auth_success:
        print("\n🎉 All Gmail authentication tests passed!")
        print("\n💡 Next steps:")
        print("   1. Your Gmail API authentication is working correctly")
        print("   2. Token files have been created for future use")
        print("   3. You can now use Gmail functionality in your application")
        print("   4. The token.pickle file will be used for faster authentication")
    else:
        print("\n❌ Gmail authentication test failed!")
        print("\n🔧 Troubleshooting:")
        print("   1. Check your credentials.json file is valid")
        print("   2. Ensure you have the correct Gmail API scopes")
        print("   3. Verify your Google Cloud project has Gmail API enabled")
        print("   4. Check your OAuth consent screen configuration")


if __name__ == "__main__":
    asyncio.run(main())
