#!/usr/bin/env python3
"""
Email Fetching Parameters Information
Shows what the system is looking for when fetching emails
"""

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.columns import Columns
from rich.text import Text

console = Console()

def show_email_params():
    """Display email fetching parameters and filters."""
    
    console.print("\n🔍 [bold blue]EMAIL FETCHING PARAMETERS[/bold blue]\n")
    
    # Gmail Query Parameters
    gmail_table = Table(title="📬 Gmail API Query Parameters", show_header=True, header_style="bold magenta")
    gmail_table.add_column("Parameter", style="cyan", width=20)
    gmail_table.add_column("Value", style="green", width=30)
    gmail_table.add_column("Description", style="white", width=50)
    
    gmail_table.add_row("userId", "'me'", "Current authenticated user's mailbox")
    gmail_table.add_row("q", "'is:unread'", "Gmail search query - only unread emails")
    gmail_table.add_row("maxResults", "10", "Maximum number of emails to fetch per check")
    gmail_table.add_row("labelIds", "None", "No specific label filtering (gets all unread)")
    gmail_table.add_row("includeSpamTrash", "False", "Excludes spam and trash folders")
    
    console.print(gmail_table)
    
    # Email Processing Filters
    filter_table = Table(title="🔍 Email Processing Filters", show_header=True, header_style="bold yellow")
    filter_table.add_column("Filter Type", style="cyan", width=25)
    filter_table.add_column("Criteria", style="green", width=40)
    filter_table.add_column("Action", style="white", width=35)
    
    filter_table.add_row("Duplicate Prevention", "Email ID tracking", "Skip already processed emails")
    filter_table.add_row("Internal Senders", "Known team email addresses", "Special handling for internal emails")
    filter_table.add_row("Attachment Count", "> 3 attachments", "Flag for human review")
    filter_table.add_row("Thread Complexity", "Long email threads", "Enhanced context analysis")
    filter_table.add_row("Urgency Detection", "Keywords + AI analysis", "Priority routing")
    
    console.print(filter_table)
    
    # Internal Senders List
    internal_panel = Panel(
        """[bold cyan]Internal Senders (Special Handling):[/bold cyan]
        
• <EMAIL>
• <EMAIL>  
• <EMAIL>
• <EMAIL>
• <EMAIL>

[italic]These emails get enhanced processing and context awareness[/italic]""",
        title="👥 Internal Email Handling",
        border_style="blue"
    )
    
    # AI Analysis Parameters
    ai_panel = Panel(
        """[bold red]Mistral AI Analysis Parameters:[/bold red]

• [cyan]Intent Detection:[/cyan] order, support, inquiry, complaint, other
• [cyan]Urgency Levels:[/cyan] low, medium, high, critical
• [cyan]Confidence Scoring:[/cyan] 0.0 - 1.0 scale
• [cyan]Entity Extraction:[/cyan] Names, companies, products, amounts
• [cyan]Sentiment Analysis:[/cyan] Positive, neutral, negative
• [cyan]Action Suggestions:[/cyan] respond, escalate, process_order, etc.

[italic]Each email gets structured analysis for intelligent routing[/italic]""",
        title="🧠 AI Processing Parameters",
        border_style="red"
    )
    
    # Display panels side by side
    console.print(Columns([internal_panel, ai_panel]))
    
    # Email Monitoring Schedule
    schedule_table = Table(title="⏰ Email Monitoring Schedule", show_header=True, header_style="bold green")
    schedule_table.add_column("Frequency", style="cyan", width=20)
    schedule_table.add_column("Interval", style="green", width=20)
    schedule_table.add_column("Purpose", style="white", width=50)
    
    schedule_table.add_row("Email Check", "Every 30 seconds", "Continuous monitoring for new emails")
    schedule_table.add_row("Health Check", "Every 60 seconds", "System status and connection verification")
    schedule_table.add_row("Cleanup", "Every 5 minutes", "Remove processed email IDs from memory")
    
    console.print(schedule_table)
    
    # Current Query Details
    query_panel = Panel(
        """[bold yellow]Current Gmail Search Query:[/bold yellow]

[cyan]Query String:[/cyan] [green]'is:unread'[/green]

[cyan]What this finds:[/cyan]
• All unread emails in the inbox
• Excludes read emails (already processed)
• Excludes spam and trash automatically
• Includes emails with attachments
• Includes emails in threads

[cyan]Additional Filters Applied:[/cyan]
• Duplicate prevention by message ID
• Internal sender detection
• Attachment count analysis
• Thread context extraction

[italic]The system processes emails in real-time as they arrive[/italic]""",
        title="📋 Current Email Query Details",
        border_style="yellow"
    )
    
    console.print(query_panel)
    
    # Performance Stats
    perf_table = Table(title="📊 Performance Parameters", show_header=True, header_style="bold cyan")
    perf_table.add_column("Metric", style="cyan", width=25)
    perf_table.add_column("Current Setting", style="green", width=20)
    perf_table.add_column("Impact", style="white", width=45)
    
    perf_table.add_row("Max Results per Check", "10 emails", "Prevents API rate limiting")
    perf_table.add_row("Processing Timeout", "30 seconds", "Per email processing limit")
    perf_table.add_row("API Rate Limit", "250 requests/user/second", "Gmail API constraint")
    perf_table.add_row("Concurrent Processing", "1 email at a time", "Sequential processing for reliability")
    
    console.print(perf_table)

if __name__ == "__main__":
    show_email_params()
