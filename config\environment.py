"""
Environment configuration and validation for the multi-agent sales support system.
"""

import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional
import logging
from dotenv import load_dotenv

logger = logging.getLogger(__name__)


class EnvironmentError(Exception):
    """Custom exception for environment configuration errors."""
    pass


class EnvironmentManager:
    """Manages environment configuration and validation."""
    
    def __init__(self, env_file: Optional[str] = None):
        """Initialize the environment manager.
        
        Args:
            env_file: Path to the .env file. If None, uses default .env
        """
        self.env_file = env_file or ".env"
        self.required_vars = {
            "MISTRAL_API_KEY": "Mistral AI API key is required",
            "MYOB_EXO_API_URL": "MYOB EXO API URL is required",
            "MYOB_EXO_API_KEY": "MYOB EXO API key is required",
            "SUPABASE_URL": "Supabase URL is required",
            "SUPABASE_SERVICE_KEY": "Supabase service key is required",
            "JWT_SECRET_KEY": "JWT secret key is required for security"
        }

        # Optional vars with helpful messages
        self.optional_vars = {
            "GMAIL_SCOPES": "Gmail API scopes (uses default if not specified)",
        }

        # File-based requirements
        self.required_files = {
            "credentials.json": "Gmail API credentials file is required for Gmail integration. Download from Google Cloud Console."
        }
        self.optional_vars = {
            "OPENAI_API_KEY": "OpenAI API key (used for embeddings fallback)",
            "FALKORDB_PASSWORD": "FalkorDB password for secure connections",
            "REDIS_PASSWORD": "Redis password for secure connections",
            "SENTRY_DSN": "Sentry DSN for error tracking",
            "ENCRYPTION_KEY": "Encryption key for sensitive data"
        }
        
    def load_environment(self) -> bool:
        """Load environment variables from .env file.
        
        Returns:
            True if environment loaded successfully, False otherwise
        """
        try:
            # Check if .env file exists
            env_path = Path(self.env_file)
            if not env_path.exists():
                logger.warning(f"Environment file {self.env_file} not found. Using system environment variables.")
                return True
            
            # Load environment variables
            load_dotenv(self.env_file, override=True)
            logger.info(f"Environment variables loaded from {self.env_file}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load environment file {self.env_file}: {e}")
            return False
    
    def validate_environment(self) -> Dict[str, Any]:
        """Validate that all required environment variables are set.
        
        Returns:
            Dictionary with validation results
            
        Raises:
            EnvironmentError: If required variables are missing
        """
        validation_result = {
            "valid": True,
            "missing_required": [],
            "missing_optional": [],
            "warnings": []
        }
        
        # Check required variables
        for var_name, description in self.required_vars.items():
            if not os.getenv(var_name):
                validation_result["missing_required"].append({
                    "name": var_name,
                    "description": description
                })

        # Check required files
        if hasattr(self, 'required_files'):
            for file_name, description in self.required_files.items():
                if not os.path.exists(file_name):
                    validation_result["missing_required"].append({
                        "name": file_name,
                        "description": description
                    })
                    validation_result["valid"] = False
        
        # Check optional variables
        for var_name, description in self.optional_vars.items():
            if not os.getenv(var_name):
                validation_result["missing_optional"].append({
                    "name": var_name,
                    "description": description
                })
        
        # Additional validations
        self._validate_urls(validation_result)
        self._validate_numeric_values(validation_result)
        self._validate_file_paths(validation_result)
        
        if not validation_result["valid"]:
            missing_vars = [var["name"] for var in validation_result["missing_required"]]
            raise EnvironmentError(
                f"Missing required environment variables: {', '.join(missing_vars)}. "
                f"Please check your .env file or set these variables in your environment."
            )
        
        return validation_result
    
    def _validate_urls(self, validation_result: Dict[str, Any]) -> None:
        """Validate URL format for API endpoints."""
        url_vars = [
            "MYOB_EXO_API_URL",
            "SUPABASE_URL",
            "GOOGLE_REDIRECT_URI"
        ]
        
        for var_name in url_vars:
            value = os.getenv(var_name)
            if value and not (value.startswith("http://") or value.startswith("https://")):
                validation_result["warnings"].append(
                    f"{var_name} should start with http:// or https://"
                )
    
    def _validate_numeric_values(self, validation_result: Dict[str, Any]) -> None:
        """Validate numeric environment variables."""
        numeric_vars = {
            "FALKORDB_PORT": (1, 65535),
            "MCP_SERVER_PORT": (1, 65535),
            "PROMETHEUS_PORT": (1, 65535),
            "EMAIL_CHECK_INTERVAL": (1, None),
            "SUPPORT_RESPONSE_TIMEOUT": (1, None),
            "ORDER_PROCESSING_TIMEOUT": (1, None)
        }
        
        for var_name, (min_val, max_val) in numeric_vars.items():
            value = os.getenv(var_name)
            if value:
                try:
                    num_value = int(value)
                    if num_value < min_val:
                        validation_result["warnings"].append(
                            f"{var_name} should be at least {min_val}"
                        )
                    if max_val and num_value > max_val:
                        validation_result["warnings"].append(
                            f"{var_name} should be at most {max_val}"
                        )
                except ValueError:
                    validation_result["warnings"].append(
                        f"{var_name} should be a valid integer"
                    )
    
    def _validate_file_paths(self, validation_result: Dict[str, Any]) -> None:
        """Validate file path environment variables."""
        path_vars = [
            "UPLOAD_DIR",
            "TEMP_DIR",
            "PDF_TEMP_DIR",
            "LOG_FILE_PATH"
        ]
        
        for var_name in path_vars:
            value = os.getenv(var_name)
            if value:
                try:
                    path = Path(value)
                    # Create directory if it doesn't exist
                    if var_name.endswith("_DIR"):
                        path.mkdir(parents=True, exist_ok=True)
                    else:
                        # For file paths, create parent directory
                        path.parent.mkdir(parents=True, exist_ok=True)
                except Exception as e:
                    validation_result["warnings"].append(
                        f"Could not create path for {var_name}: {e}"
                    )
    
    def get_environment_info(self) -> Dict[str, Any]:
        """Get information about the current environment.
        
        Returns:
            Dictionary with environment information
        """
        return {
            "python_version": sys.version,
            "platform": sys.platform,
            "environment": os.getenv("ENVIRONMENT", "development"),
            "debug": os.getenv("DEBUG", "false").lower() == "true",
            "log_level": os.getenv("LOG_LEVEL", "INFO"),
            "app_name": os.getenv("APP_NAME", "sales-support-system"),
            "app_version": os.getenv("APP_VERSION", "1.0.0")
        }
    
    def create_example_env(self, output_path: str = ".env.example") -> None:
        """Create an example .env file with all variables.
        
        Args:
            output_path: Path where to create the example file
        """
        example_content = []
        
        # Add header
        example_content.extend([
            "# =============================================================================",
            "# Multi-Agent Sales Support System - Environment Configuration",
            "# =============================================================================",
            "",
            "# Copy this file to .env and fill in your actual values",
            ""
        ])
        
        # Add required variables
        example_content.extend([
            "# Required Variables",
            "# =================="
        ])
        
        for var_name, description in self.required_vars.items():
            example_content.extend([
                f"# {description}",
                f"{var_name}=your_{var_name.lower()}_here",
                ""
            ])
        
        # Add optional variables
        example_content.extend([
            "# Optional Variables",
            "# =================="
        ])
        
        for var_name, description in self.optional_vars.items():
            example_content.extend([
                f"# {description}",
                f"# {var_name}=your_{var_name.lower()}_here",
                ""
            ])
        
        # Write to file
        with open(output_path, "w") as f:
            f.write("\n".join(example_content))
        
        logger.info(f"Example environment file created at {output_path}")


# Global environment manager instance
env_manager = EnvironmentManager()


def setup_environment(env_file: Optional[str] = None) -> Dict[str, Any]:
    """Setup and validate the environment.
    
    Args:
        env_file: Path to the .env file
        
    Returns:
        Environment validation results
        
    Raises:
        EnvironmentError: If environment setup fails
    """
    global env_manager
    
    if env_file:
        env_manager = EnvironmentManager(env_file)
    
    # Load environment variables
    if not env_manager.load_environment():
        raise EnvironmentError("Failed to load environment configuration")
    
    # Validate environment
    validation_result = env_manager.validate_environment()
    
    # Log warnings
    for warning in validation_result.get("warnings", []):
        logger.warning(warning)
    
    # Log missing optional variables
    for missing in validation_result.get("missing_optional", []):
        logger.info(f"Optional variable not set: {missing['name']} - {missing['description']}")
    
    logger.info("Environment setup completed successfully")
    return validation_result


def get_environment_info() -> Dict[str, Any]:
    """Get current environment information."""
    return env_manager.get_environment_info()


def is_development() -> bool:
    """Check if running in development environment."""
    return os.getenv("ENVIRONMENT", "development").lower() == "development"


def is_production() -> bool:
    """Check if running in production environment."""
    return os.getenv("ENVIRONMENT", "development").lower() == "production"


def is_testing() -> bool:
    """Check if running in testing environment."""
    return os.getenv("ENVIRONMENT", "development").lower() == "testing"
