#!/usr/bin/env python3
"""
Test script to validate MYOB EXO API authentication setup.
"""

import asyncio
import base64
import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import httpx
from config.settings import get_settings


async def test_myob_authentication():
    """Test MYOB EXO API authentication."""
    print("🔧 Testing MYOB EXO API Authentication...")
    print("=" * 50)
    
    try:
        # Load settings
        settings = get_settings()
        
        # Validate required settings
        required_settings = [
            'myob_exo_api_url',
            'myob_exo_username', 
            'myob_exo_password',
            'myob_exo_api_key',
            'myob_exo_token'
        ]
        
        print("📋 Checking configuration...")
        for setting in required_settings:
            value = getattr(settings, setting, None)
            if not value:
                print(f"❌ Missing required setting: {setting}")
                return False
            else:
                # Mask sensitive values
                if setting in ['myob_exo_password', 'myob_exo_api_key', 'myob_exo_token']:
                    display_value = f"{value[:8]}..." if len(value) > 8 else "***"
                else:
                    display_value = value
                print(f"✅ {setting}: {display_value}")
        
        print("\n🔐 Preparing authentication headers...")
        
        # Prepare authentication headers
        auth_string = f"{settings.myob_exo_username}:{settings.myob_exo_password}"
        encoded_auth = base64.b64encode(auth_string.encode()).decode()
        
        headers = {
            "Authorization": f"Basic {encoded_auth}",
            "x-myobapi-key": settings.myob_exo_api_key,
            "x-myobapi-exotoken": settings.myob_exo_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        print(f"✅ Authorization: Basic {encoded_auth[:20]}...")
        print(f"✅ x-myobapi-key: {settings.myob_exo_api_key[:20]}...")
        print(f"✅ x-myobapi-exotoken: {settings.myob_exo_token[:20]}...")
        
        print("\n🌐 Testing API connection...")
        
        # Test discovery endpoint
        async with httpx.AsyncClient(timeout=30.0) as client:
            url = f"{settings.myob_exo_api_url.rstrip('/')}/discovery"
            print(f"📡 Calling: {url}")
            
            response = await client.get(url, headers=headers)
            
            print(f"📊 Response Status: {response.status_code}")
            print(f"📊 Response Headers: {dict(response.headers)}")
            
            if response.is_success:
                print("✅ Authentication successful!")
                
                try:
                    data = response.json()
                    print(f"📄 Response data type: {type(data)}")
                    
                    if isinstance(data, list):
                        print(f"📄 Found {len(data)} endpoints")
                        # Show first few endpoints
                        for i, endpoint in enumerate(data[:5]):
                            if isinstance(endpoint, dict):
                                title = endpoint.get('title', 'Unknown')
                                href = endpoint.get('href', 'Unknown')
                                print(f"   {i+1}. {title}: {href}")
                    elif isinstance(data, dict):
                        print(f"📄 Response keys: {list(data.keys())}")
                    
                except Exception as e:
                    print(f"⚠️  Could not parse JSON response: {e}")
                    print(f"📄 Raw response: {response.text[:200]}...")
                
                return True
                
            else:
                print(f"❌ Authentication failed!")
                print(f"📄 Error response: {response.text}")
                
                # Check for common error patterns
                if response.status_code == 401:
                    print("\n🔍 Troubleshooting 401 Unauthorized:")
                    print("   - Check username and password are correct")
                    print("   - Verify EXO token is valid and not expired")
                    print("   - Ensure API key is correct")
                elif response.status_code == 404:
                    print("\n🔍 Troubleshooting 404 Not Found:")
                    print("   - Check API URL is correct")
                    print("   - Verify MYOB EXO API server is running")
                elif response.status_code == 403:
                    print("\n🔍 Troubleshooting 403 Forbidden:")
                    print("   - Check user has API access permissions")
                    print("   - Verify EXO API module is licensed")
                
                return False
                
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_token_refresh():
    """Test token refresh functionality."""
    print("\n🔄 Testing token refresh...")
    print("=" * 30)
    
    try:
        settings = get_settings()
        
        # Prepare headers
        auth_string = f"{settings.myob_exo_username}:{settings.myob_exo_password}"
        encoded_auth = base64.b64encode(auth_string.encode()).decode()
        
        headers = {
            "Authorization": f"Basic {encoded_auth}",
            "x-myobapi-key": settings.myob_exo_api_key,
            "x-myobapi-exotoken": settings.myob_exo_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            url = f"{settings.myob_exo_api_url.rstrip('/')}/token"
            print(f"📡 Calling token refresh: {url}")
            
            response = await client.get(url, headers=headers)
            
            if response.is_success:
                print("✅ Token refresh successful!")
                
                try:
                    # Token response might be a string or JSON
                    if response.headers.get('content-type', '').startswith('application/json'):
                        data = response.json()
                        print(f"📄 Token response: {data}")
                    else:
                        token_text = response.text
                        print(f"📄 New token: {token_text[:20]}...")
                        
                except Exception as e:
                    print(f"⚠️  Could not parse token response: {e}")
                    print(f"📄 Raw response: {response.text[:100]}...")
                
                return True
            else:
                print(f"❌ Token refresh failed: {response.status_code}")
                print(f"📄 Error: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Token refresh test failed: {e}")
        return False


async def main():
    """Run all authentication tests."""
    print("🚀 MYOB EXO API Authentication Test Suite")
    print("=" * 60)
    
    # Test basic authentication
    auth_success = await test_myob_authentication()
    
    if auth_success:
        # Test token refresh
        await test_token_refresh()
        
        print("\n🎉 All tests completed!")
        print("\n💡 Next steps:")
        print("   1. If authentication works, your setup is correct")
        print("   2. You can now use the MYOB EXO API in your application")
        print("   3. Remember to handle token refresh in production")
    else:
        print("\n❌ Authentication test failed!")
        print("\n🔧 Please check your .env file and MYOB EXO configuration")


if __name__ == "__main__":
    asyncio.run(main())
