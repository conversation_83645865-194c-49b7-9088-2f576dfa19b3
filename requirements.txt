# Multi-Agent Sales Support System - Requirements
# Updated: January 2025 with latest versions
# Python 3.9+ required for most AI frameworks
#
# Key Updates (All packages upgraded to latest versions):
# - Pydantic-AI: 0.4.8 (Latest)
# - LangChain: 0.3.27 (Latest)
# - FastAPI: 0.116.1 (Latest)
# - Graphiti: 0.18.0 (Latest)
# - Supabase: 2.17.0 (Latest)
# - Anthropic: 0.60.0 (Latest)
# - OpenAI: 1.97.1 (Latest)
# - Redis: 6.2.0 (Latest)
# - NumPy: 2.3.2 (Latest)
# - Pandas: 2.3.1 (Latest)

# Core AI and Agent Frameworks
# Using pydantic-ai-slim with specific providers for cleaner dependencies
pydantic-ai-slim[openai,anthropic,mistral,groq]>=0.4.8  # Latest version
langchain>=0.3.27  # Latest version
langchain-core>=0.3.72
langchain-openai>=0.3.28  # Latest version
langchain-community>=0.3.27  # Latest version
langchain-anthropic>=0.3.17  # Latest version
langchain-text-splitters>=0.3.9  # Latest version
crewai>=0.150.0  # Latest version
crewai-tools>=0.58.0  # Latest version

# AI Provider Dependencies
anthropic>=0.60.0  # Latest version
groq>=0.30.0  # Latest version
openai>=1.97.1  # Latest version
mistralai>=1.9.3  # Latest version
cohere>=5.16.1  # Latest version

# Additional AI Provider Dependencies
google-generativeai>=0.8.5  # Latest version
google-genai>=1.27.0  # Latest version

# Google APIs and Gmail Integration
google-api-python-client>=2.177.0
google-auth-httplib2>=0.2.0
google-auth-oauthlib>=1.2.2
google-auth>=2.40.3
google-api-core>=2.25.1
googleapis-common-protos>=1.70.0

# PDF Processing
PyMuPDF>=1.26.3
pymupdf4llm>=0.0.27
pypdf>=5.9.0
pdfplumber>=0.11.7

# Database and Vector Storage
supabase>=2.17.0  # Latest version
psycopg2-binary>=2.9.9
pgvector>=0.3.6
asyncpg>=0.30.0

# Knowledge Graph
graphiti-core>=0.18.0  # Latest version
falkordb>=1.2.0  # Latest version
redis>=6.2.0  # Latest version

# Web Framework and API
fastapi>=0.116.1  # Latest version
uvicorn[standard]>=0.35.0
pydantic>=2.11.7  # Latest stable version
pydantic-settings>=2.10.1  # Latest version
httpx>=0.28.0
starlette>=0.47.2

# Data Processing and Utilities
pandas>=2.3.1  # Latest version
numpy>=2.3.2  # Latest version
python-dotenv>=1.1.1  # Latest version
pyyaml>=6.0.2
jinja2>=3.1.4

# Async and Concurrency
asyncio-mqtt>=0.16.2
aiofiles>=24.1.0
aiohttp>=3.12.14  # Latest version
anyio>=4.9.0

# Monitoring and Logging
structlog>=25.4.0  # Latest version
prometheus-client>=0.21.0
sentry-sdk>=2.18.0

# Security and Authentication
cryptography>=45.0.5  # Latest version
python-jose[cryptography]>=3.5.0
passlib[bcrypt]>=1.7.4

# Testing
pytest>=8.4.1  # Latest version
pytest-asyncio>=1.1.0  # Latest version
pytest-mock>=3.14.1  # Latest version
pytest-cov>=6.2.1  # Latest version
factory-boy>=3.3.3  # Latest version

# Development Tools
black>=25.1.0  # Latest version
isort>=5.13.0
flake8>=7.3.0  # Latest version
mypy>=1.17.0  # Latest version
pre-commit>=4.0.0

# Email and Communication
email-validator>=2.2.0
python-multipart>=0.0.12

# File Processing
openpyxl>=3.1.5
xlsxwriter>=3.2.0
python-magic>=0.4.27

# Caching and Performance
cachetools>=6.1.0  # Latest version

# Configuration Management
dynaconf>=3.2.6
click>=8.2.1  # Latest version

# Date and Time
python-dateutil>=2.9.0
pytz>=2025.2  # Latest version

# HTTP and Requests
requests>=2.32.4  # Latest version
urllib3>=2.5.0  # Latest version

# JSON and Data Serialization
orjson>=3.11.1  # Latest version
msgpack>=1.1.1  # Latest version

# Environment and System
psutil>=6.1.0
python-decouple>=3.8

# Additional AI/ML Tools and Utilities
tiktoken>=0.9.0  # Latest version for OpenAI tokenization
tenacity>=9.1.2  # Latest version for retry mechanisms
backoff>=2.2.1  # Alternative retry library
instructor>=1.10.0  # Latest version for structured LLM outputs
litellm>=1.74.9  # Latest version multi-provider LLM interface

# Vector Database and Search
chromadb>=1.0.15  # Latest version alternative vector database
pinecone-client>=5.0.0  # Pinecone vector database
weaviate-client>=4.16.4  # Latest version Weaviate vector database
qdrant-client>=1.15.0  # Latest version Qdrant vector database

# Additional LangChain Integrations
langchain-google-genai>=2.1.8  # Latest version Google Gemini integration
langchain-groq>=0.3.6  # Latest version Groq integration
langchain-chroma>=0.2.5  # Latest version ChromaDB integration
langchain-cohere>=0.4.4  # Latest version Cohere integration

# Observability and Monitoring
langsmith>=0.4.8  # Latest version LangChain monitoring
logfire-api>=4.0.0  # Latest version observability

# Memory and Knowledge Management
mem0ai>=0.1.115  # Latest version for memory management

# Additional Utilities
sympy>=1.14.0  # Latest version for symbolic mathematics
networkx>=3.5  # Latest version for graph algorithms
jsonschema>=4.25.0  # Latest version for JSON schema validation
validators>=0.35.0  # Latest version for data validation
