#!/usr/bin/env python3
"""
Test script for the Simplified AI Sales Support System
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def test_environment_setup():
    """Test environment setup."""
    try:
        print("🔧 Testing environment setup...")
        
        from dotenv import load_dotenv
        load_dotenv('.env.simple')
        
        # Check required environment variables
        required_vars = [
            'MISTRAL_API_KEY',
            'SUPABASE_URL',
            'SUPABASE_SERVICE_KEY'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var) or os.getenv(var) == f'your_{var.lower()}_here':
                missing_vars.append(var)
        
        if missing_vars:
            print(f"⚠️ Missing environment variables: {', '.join(missing_vars)}")
            print("Please update .env.simple with your actual API keys")
            return False
        
        print("✅ Environment setup complete")
        return True
        
    except Exception as e:
        logger.error(f"❌ Environment setup failed: {e}")
        return False


async def test_supabase_connection():
    """Test Supabase connection."""
    try:
        print("🗄️ Testing Supabase connection...")
        
        from supabase import create_client
        
        supabase_url = os.getenv('SUPABASE_URL')
        supabase_key = os.getenv('SUPABASE_SERVICE_KEY')
        
        if not supabase_url or not supabase_key:
            print("⚠️ Supabase credentials not configured")
            return False
        
        supabase = create_client(supabase_url, supabase_key)
        
        # Test connection
        response = supabase.table("customer_interactions").select("count").limit(1).execute()
        
        print("✅ Supabase connection successful")
        return True
        
    except Exception as e:
        logger.error(f"❌ Supabase connection failed: {e}")
        return False


async def test_mistral_api():
    """Test Mistral AI API."""
    try:
        print("🤖 Testing Mistral AI API...")
        
        import httpx
        
        mistral_api_key = os.getenv('MISTRAL_API_KEY')
        
        if not mistral_api_key or mistral_api_key == 'your_mistral_api_key_here':
            print("⚠️ Mistral API key not configured")
            return False
        
        # Test API call
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://api.mistral.ai/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {mistral_api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "mistral-large-latest",
                    "messages": [
                        {"role": "user", "content": "Hello, this is a test. Please respond with 'Test successful'."}
                    ],
                    "max_tokens": 50
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                print(f"✅ Mistral AI API successful: {content}")
                return True
            else:
                print(f"❌ Mistral API error: {response.status_code}")
                return False
                
    except Exception as e:
        logger.error(f"❌ Mistral AI API test failed: {e}")
        return False


async def test_graphiti():
    """Test Graphiti temporal knowledge."""
    try:
        print("🧠 Testing Graphiti temporal knowledge...")
        
        from graphiti import Graphiti
        
        graphiti = Graphiti()
        await graphiti.build_indices_if_empty()
        
        # Test adding and retrieving memories
        test_memories = [
            "Customer <EMAIL> placed an order for SKU ABC123",
            "Customer <EMAIL> asked about delivery times"
        ]
        
        await graphiti.add_episodic_memories(test_memories)
        
        # Search for memories
        results = await graphiti.search("customer order", limit=2)
        
        print(f"✅ Graphiti test successful: {len(results)} memories found")
        return True
        
    except Exception as e:
        logger.warning(f"⚠️ Graphiti test failed (optional component): {e}")
        return False


async def test_agents():
    """Test agent initialization."""
    try:
        print("🤖 Testing agent initialization...")
        
        # Test imports
        from agents.simple_email_agent import EmailAgent
        from agents.simple_support_agent import SupportAgent
        from agents.simple_myob_agent import MyobAgent
        
        # Mock Supabase for testing
        class MockSupabase:
            def table(self, name):
                return self
            def select(self, *args):
                return self
            def eq(self, *args):
                return self
            def execute(self):
                return type('obj', (object,), {'data': []})()
        
        mock_supabase = MockSupabase()
        
        # Initialize agents
        email_agent = EmailAgent(
            mistral_api_key="test_key",
            supabase=mock_supabase
        )
        
        support_agent = SupportAgent(
            mistral_api_key="test_key",
            supabase=mock_supabase
        )
        
        myob_agent = MyobAgent(
            mistral_api_key="test_key",
            myob_api_url="http://test.com",
            myob_api_key="test_key",
            supabase=mock_supabase
        )
        
        print("✅ All agents initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Agent initialization failed: {e}")
        return False


async def test_simple_system():
    """Test the simplified system."""
    try:
        print("🚀 Testing simplified system...")
        
        # Import and test basic initialization
        from simple_main import SimplifiedSalesSystem
        
        system = SimplifiedSalesSystem()
        
        print("✅ Simplified system initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Simplified system test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🧪 Starting Simplified AI Framework Tests")
    print("=" * 60)
    
    tests = [
        ("Environment Setup", test_environment_setup),
        ("Supabase Connection", test_supabase_connection),
        ("Mistral AI API", test_mistral_api),
        ("Graphiti Temporal Knowledge", test_graphiti),
        ("Agent Initialization", test_agents),
        ("Simplified System", test_simple_system),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            result = await test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{status}: {test_name}")
        except Exception as e:
            logger.error(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed >= 4:  # Allow some optional components to fail
        print("\n🎉 Framework is ready! Core components are working.")
        print("\n📝 Next steps:")
        print("1. Update .env.simple with your actual API keys")
        print("2. Set up Gmail credentials.json file")
        print("3. Configure MYOB EXO API settings")
        print("4. Run: python simple_main.py")
    else:
        print("\n⚠️ Some critical tests failed. Please check the configuration.")


if __name__ == "__main__":
    # Create logs directory
    os.makedirs("logs", exist_ok=True)
    
    # Run tests
    asyncio.run(main())
