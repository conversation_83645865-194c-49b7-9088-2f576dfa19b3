#!/usr/bin/env python3
"""
Email Processing Viewer - CLI tool with rich formatting
View processed emails, analysis results, and system status
"""

import asyncio
import json
import os
from datetime import datetime
from typing import Dict, List, Any

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.columns import Columns
from rich.text import Text
from rich.prompt import Prompt, Confirm
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.syntax import Syntax
from rich.tree import Tree
from rich.layout import Layout
from rich.live import Live

from dotenv import load_dotenv
from supabase import create_client

# Load environment
load_dotenv()

console = Console()

class EmailViewer:
    """CLI tool for viewing email processing results."""
    
    def __init__(self):
        """Initialize the email viewer."""
        self.supabase = create_client(
            os.getenv("SUPABASE_URL"),
            os.getenv("SUPABASE_SERVICE_KEY")
        )
    
    def show_recent_emails(self, limit: int = 10):
        """Show recent processed emails with rich formatting."""
        try:
            # Fetch recent emails
            response = self.supabase.table("customer_interactions").select("*").eq(
                "interaction_type", "email"
            ).order("created_at", desc=True).limit(limit).execute()
            
            emails = response.data or []
            
            if not emails:
                console.print("[yellow]No emails found in database[/yellow]")
                return
            
            # Create main table
            table = Table(title=f"📧 Recent {len(emails)} Processed Emails", show_header=True, header_style="bold magenta")
            table.add_column("Time", style="dim", width=12)
            table.add_column("From", style="cyan", width=30)
            table.add_column("Subject", style="green", width=40)
            table.add_column("Intent", style="yellow", width=15)
            table.add_column("Urgency", style="red", width=10)
            
            for email in emails:
                # Parse metadata
                metadata = email.get('metadata', {})
                analysis = metadata.get('analysis', {})
                
                # Format timestamp
                created_at = datetime.fromisoformat(email['created_at'].replace('Z', '+00:00'))
                time_str = created_at.strftime("%H:%M:%S")
                
                # Extract analysis data
                intent = analysis.get('intent', 'unknown')
                urgency = analysis.get('urgency', 'low')
                subject = metadata.get('subject', 'No subject')[:37] + "..." if len(metadata.get('subject', '')) > 40 else metadata.get('subject', 'No subject')
                
                # Color code urgency
                urgency_color = {
                    'urgent': '[red]urgent[/red]',
                    'high': '[orange1]high[/orange1]',
                    'medium': '[yellow]medium[/yellow]',
                    'low': '[green]low[/green]'
                }.get(urgency.lower(), urgency)
                
                table.add_row(
                    time_str,
                    email['customer_email'],
                    subject,
                    intent,
                    urgency_color
                )
            
            console.print(table)
            
        except Exception as e:
            console.print(f"[red]Error fetching emails: {e}[/red]")
    
    def show_email_details(self, email_index: int = 0):
        """Show detailed analysis for a specific email."""
        try:
            # Fetch recent email
            response = self.supabase.table("customer_interactions").select("*").eq(
                "interaction_type", "email"
            ).order("created_at", desc=True).limit(10).execute()
            
            emails = response.data or []
            
            if not emails or email_index >= len(emails):
                console.print("[red]Email not found[/red]")
                return
            
            email = emails[email_index]
            metadata = email.get('metadata', {})
            analysis = metadata.get('analysis', {})
            
            # Create layout
            layout = Layout()
            layout.split_column(
                Layout(name="header", size=3),
                Layout(name="body"),
                Layout(name="footer", size=3)
            )
            
            # Header
            header_text = Text(f"📧 Email Analysis Details", style="bold blue")
            layout["header"].update(Panel(header_text, style="blue"))
            
            # Body - split into left and right
            layout["body"].split_row(
                Layout(name="left"),
                Layout(name="right")
            )
            
            # Left panel - Email content
            email_content = f"""From: {email['customer_email']}
Subject: {metadata.get('subject', 'No subject')}
Time: {email['created_at']}

Content:
{email.get('content', 'No content')[:500]}{'...' if len(email.get('content', '')) > 500 else ''}"""
            
            layout["left"].update(Panel(email_content, title="📨 Email Content", border_style="green"))
            
            # Right panel - Analysis results
            analysis_text = json.dumps(analysis, indent=2) if analysis else "No analysis available"
            syntax = Syntax(analysis_text, "json", theme="monokai", line_numbers=True)
            layout["right"].update(Panel(syntax, title="🤖 AI Analysis", border_style="yellow"))
            
            # Footer
            footer_text = Text("Press Enter to continue...", style="dim")
            layout["footer"].update(Panel(footer_text, style="dim"))
            
            console.print(layout)
            input()  # Wait for user input
            
        except Exception as e:
            console.print(f"[red]Error fetching email details: {e}[/red]")
    
    def show_system_stats(self):
        """Show system statistics."""
        try:
            # Get email counts
            total_response = self.supabase.table("customer_interactions").select("count").eq(
                "interaction_type", "email"
            ).execute()
            
            total_emails = len(total_response.data) if total_response.data else 0
            
            # Get recent activity (last hour)
            from datetime import datetime, timedelta
            one_hour_ago = (datetime.utcnow() - timedelta(hours=1)).isoformat()
            
            recent_response = self.supabase.table("customer_interactions").select("*").eq(
                "interaction_type", "email"
            ).gte("created_at", one_hour_ago).execute()
            
            recent_emails = len(recent_response.data) if recent_response.data else 0
            
            # Create stats panel
            stats_table = Table(show_header=False, box=None)
            stats_table.add_column("Metric", style="cyan")
            stats_table.add_column("Value", style="green")
            
            stats_table.add_row("📧 Total Emails Processed", str(total_emails))
            stats_table.add_row("⏰ Last Hour Activity", str(recent_emails))
            stats_table.add_row("🗄️ Database Status", "✅ Connected")
            stats_table.add_row("🤖 AI Processing", "✅ Active")
            
            console.print(Panel(stats_table, title="📊 System Statistics", border_style="blue"))
            
        except Exception as e:
            console.print(f"[red]Error fetching stats: {e}[/red]")

def main():
    """Main CLI interface."""
    viewer = EmailViewer()
    
    while True:
        console.clear()
        console.print(Panel.fit("📧 Email Processing Viewer", style="bold blue"))
        console.print()
        
        # Show menu
        menu_table = Table(show_header=False, box=None)
        menu_table.add_column("Option", style="cyan")
        menu_table.add_column("Description", style="white")
        
        menu_table.add_row("1", "📧 View Recent Emails")
        menu_table.add_row("2", "🔍 View Email Details")
        menu_table.add_row("3", "📊 System Statistics")
        menu_table.add_row("4", "🔄 Refresh")
        menu_table.add_row("q", "❌ Quit")
        
        console.print(menu_table)
        console.print()
        
        choice = Prompt.ask("Select option", choices=["1", "2", "3", "4", "q"], default="1")
        
        if choice == "1":
            console.clear()
            viewer.show_recent_emails()
            input("\nPress Enter to continue...")
            
        elif choice == "2":
            console.clear()
            viewer.show_recent_emails(5)  # Show 5 recent emails first
            console.print()
            try:
                index = int(Prompt.ask("Enter email index (0-4)", default="0"))
                console.clear()
                viewer.show_email_details(index)
            except ValueError:
                console.print("[red]Invalid index[/red]")
                input("Press Enter to continue...")
                
        elif choice == "3":
            console.clear()
            viewer.show_system_stats()
            input("\nPress Enter to continue...")
            
        elif choice == "4":
            continue
            
        elif choice == "q":
            console.print("[yellow]Goodbye![/yellow]")
            break

if __name__ == "__main__":
    main()
