#!/usr/bin/env python3
"""
Test script to verify that the MistralModel initialization fix works correctly.
This script tests the three agent classes that use MistralModel to ensure they
can be initialized without the "unexpected keyword argument 'api_key'" error.
"""

import os
import sys
from unittest.mock import patch

def test_customer_support_agent():
    """Test that CustomerSupportAgent can be initialized without errors."""
    print("🧪 Testing CustomerSupportAgent initialization...")
    
    try:
        # Mock the settings to avoid needing real API keys
        with patch('agents.customer_support.get_settings') as mock_settings:
            mock_settings.return_value.mistral_api_key = "test-api-key"
            mock_settings.return_value.mistral_model = "mistral-large-latest"
            mock_settings.return_value.mistral_temperature = 0.7
            mock_settings.return_value.mistral_max_tokens = 4096
            
            # Mock database operations
            with patch('agents.customer_support.get_database_operations'):
                from agents.customer_support import CustomerSupportAgent
                
                # This should not raise the "unexpected keyword argument 'api_key'" error
                agent = CustomerSupportAgent()
                print("✅ CustomerSupportAgent initialized successfully")
                return True
                
    except Exception as e:
        print(f"❌ CustomerSupportAgent initialization failed: {e}")
        return False

def test_purchasing_agent():
    """Test that PurchasingAgent can be initialized without errors."""
    print("🧪 Testing PurchasingAgent initialization...")
    
    try:
        # Mock the settings to avoid needing real API keys
        with patch('agents.purchasing.get_settings') as mock_settings:
            mock_settings.return_value.mistral_api_key = "test-api-key"
            mock_settings.return_value.mistral_model = "mistral-large-latest"
            mock_settings.return_value.mistral_temperature = 0.7
            mock_settings.return_value.mistral_max_tokens = 4096
            
            # Mock database operations
            with patch('agents.purchasing.get_database_operations'):
                from agents.purchasing import PurchasingAgent
                
                # This should not raise the "unexpected keyword argument 'api_key'" error
                agent = PurchasingAgent()
                print("✅ PurchasingAgent initialized successfully")
                return True
                
    except Exception as e:
        print(f"❌ PurchasingAgent initialization failed: {e}")
        return False

def test_order_processing_agent():
    """Test that OrderProcessingAgent can be initialized without errors."""
    print("🧪 Testing OrderProcessingAgent initialization...")
    
    try:
        # Mock the settings to avoid needing real API keys
        with patch('agents.order_processing.get_settings') as mock_settings:
            mock_settings.return_value.mistral_api_key = "test-api-key"
            mock_settings.return_value.mistral_model = "mistral-large-latest"
            mock_settings.return_value.mistral_temperature = 0.7
            mock_settings.return_value.mistral_max_tokens = 4096
            
            # Mock database operations
            with patch('agents.order_processing.get_database_operations'):
                from agents.order_processing import OrderProcessingAgent
                
                # This should not raise the "unexpected keyword argument 'api_key'" error
                agent = OrderProcessingAgent()
                print("✅ OrderProcessingAgent initialized successfully")
                return True
                
    except Exception as e:
        print(f"❌ OrderProcessingAgent initialization failed: {e}")
        return False

def main():
    """Run all tests and report results."""
    print("🚀 Testing MistralModel initialization fix...")
    print("=" * 60)
    
    results = []
    
    # Test each agent
    results.append(test_customer_support_agent())
    print()
    results.append(test_purchasing_agent())
    print()
    results.append(test_order_processing_agent())
    
    print()
    print("=" * 60)
    print("📊 Test Results:")
    
    if all(results):
        print("✅ All tests passed! MistralModel initialization fix is working correctly.")
        print("🎉 The 'unexpected keyword argument api_key' error has been resolved.")
        return 0
    else:
        print("❌ Some tests failed. The fix may not be complete.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
