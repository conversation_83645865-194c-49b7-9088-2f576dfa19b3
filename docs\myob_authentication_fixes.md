# MYOB EXO API Authentication Fixes

## Overview
This document outlines the authentication fixes implemented for the MYOB EXO API integration based on the official MYOB documentation.

## Issues Fixed

### 1. ❌ Incorrect Authentication Headers (FIXED ✅)

**Before (Incorrect):**
```python
headers = {
    "Authorization": f"Bearer {settings.myob_exo_api_key}",  # WRONG
    "Content-Type": "application/json",
    "Accept": "application/json"
}
```

**After (Correct):**
```python
import base64

auth_string = f"{settings.myob_exo_username}:{settings.myob_exo_password}"
encoded_auth = base64.b64encode(auth_string.encode()).decode()

headers = {
    "Authorization": f"Basic {encoded_auth}",           # ✅ CORRECT
    "x-myobapi-key": settings.myob_exo_api_key,        # ✅ CORRECT  
    "x-myobapi-exotoken": settings.myob_exo_token,     # ✅ CORRECT
    "Content-Type": "application/json",
    "Accept": "application/json"
}
```

### 2. ❌ Missing Environment Variables (FIXED ✅)

**Added to `config/settings.py`:**
```python
# MYOB EXO ERP
myob_exo_api_url: str = Field(..., env="MYOB_EXO_API_URL")
myob_exo_api_key: str = Field(..., env="MYOB_EXO_API_KEY")
myob_exo_username: str = Field(..., env="MYOB_EXO_USERNAME")      # ✅ ADDED
myob_exo_password: str = Field(..., env="MYOB_EXO_PASSWORD")      # ✅ ADDED
myob_exo_token: str = Field(..., env="MYOB_EXO_TOKEN")           # ✅ ADDED
myob_exo_database: Optional[str] = Field(default=None, env="MYOB_EXO_DATABASE")
myob_exo_timeout: int = Field(default=30, env="MYOB_EXO_TIMEOUT")
```

### 3. ✅ Token Refresh Functionality (ADDED)

**New Features Added:**
- Automatic token refresh on 401 authentication errors
- Token validation functionality
- Retry mechanism for API calls

**Files Modified:**
- `tools/erp_tools.py` - Added `refresh_token()` and `validate_token()` methods
- `mcp_server/main.py` - Added `refresh_myob_token()` and retry logic

## Files Modified

### 1. `config/settings.py`
- Added required MYOB EXO token field
- Made username and password required fields

### 2. `tools/erp_tools.py`
- Added `base64` import
- Fixed authentication headers in `_make_request()`
- Added `refresh_token()` method
- Added `validate_token()` method
- Added `_make_request_with_retry()` method
- Updated key methods to use retry mechanism

### 3. `mcp_server/main.py`
- Added `base64` import
- Fixed authentication headers in `call_myob_exo_api()`
- Added `refresh_myob_token()` function
- Added retry logic for authentication errors

## Authentication Flow

### Required Headers for MYOB EXO API:
1. **`Authorization: Basic [Base64Encode(username:password)]`**
   - Basic authentication with username and password
   
2. **`x-myobapi-key: [dev_key]`**
   - Developer key from my.myob.com
   
3. **`x-myobapi-exotoken: [access_token]`**
   - EXO Token generated from EXO Config application
   - Expires annually and needs refresh

### Token Refresh Process:
1. API call returns 401 Unauthorized
2. System automatically calls `/token` endpoint
3. New token is returned
4. Original API call is retried with new token
5. If refresh fails, error is propagated

## Environment Variables Required

Your `.env` file should contain:
```env
# MYOB EXO API Configuration
MYOB_EXO_API_URL=http://***********:8888
MYOB_EXO_USERNAME=MITCH
MYOB_EXO_PASSWORD=Coates25
MYOB_EXO_API_KEY=d40e1665-6cf1-4cfa-b036-17a622d43f9f
MYOB_EXO_TOKEN=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## Testing

A test script has been created: `test_myob_auth.py`

**To test authentication:**
```bash
python test_myob_auth.py
```

**The test will:**
1. Validate all required environment variables are set
2. Test authentication with the discovery endpoint
3. Test token refresh functionality
4. Provide troubleshooting guidance for common errors

## Production Considerations

### 1. Token Persistence
- Currently, refreshed tokens are logged but not persisted
- In production, you should update your configuration store with new tokens

### 2. Error Handling
- 401 errors trigger automatic token refresh
- Other errors (403, 404, 500) are handled appropriately
- Network errors are caught and logged

### 3. Security
- Sensitive values are masked in logs
- Authentication credentials are handled securely
- Base64 encoding is performed correctly

## Next Steps

1. **Test the authentication** using the provided test script
2. **Verify API connectivity** to your MYOB EXO server
3. **Implement token persistence** for production use
4. **Monitor token expiration** and refresh cycles
5. **Test with actual API operations** (sales orders, stock, etc.)

## Troubleshooting

### Common Issues:

**401 Unauthorized:**
- Check username and password
- Verify EXO token is valid and not expired
- Ensure API key is correct

**404 Not Found:**
- Check API URL is correct
- Verify MYOB EXO API server is running

**403 Forbidden:**
- Check user has API access permissions
- Verify EXO API module is licensed

**Connection Errors:**
- Check network connectivity to MYOB EXO server
- Verify firewall settings
- Check server is running on specified port
