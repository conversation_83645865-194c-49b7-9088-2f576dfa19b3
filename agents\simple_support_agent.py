"""
Simplified Customer Support Agent using Mistral AI
Handles customer queries and support interactions
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

import httpx

logger = logging.getLogger(__name__)


class SupportAgent:
    """Simplified Customer Support Agent using Mistral AI."""
    
    def __init__(self, mistral_api_key: str, supabase, graphiti=None):
        """Initialize the support agent."""
        self.mistral_api_key = mistral_api_key
        self.supabase = supabase
        self.graphiti = graphiti
        
        logger.info("🤖 Support Agent initialized")
    
    async def process_query(self, customer_email: str, query: str) -> Dict[str, Any]:
        """Process a customer support query."""
        try:
            # Get customer context
            context = await self._get_customer_context(customer_email)
            
            # Get temporal knowledge
            temporal_context = await self._get_temporal_context(customer_email)
            
            # Process with Mistral AI
            response = await self._generate_response_with_mistral(
                customer_email, query, context, temporal_context
            )
            
            # Store interaction
            await self._store_support_interaction(customer_email, query, response)
            
            # Update temporal knowledge
            if self.graphiti:
                await self._update_support_knowledge(customer_email, query, response)
            
            return response
            
        except Exception as e:
            logger.error(f"❌ Error processing support query: {e}")
            return {
                "response": "I apologize, but I'm experiencing technical difficulties. Please try again later.",
                "confidence": 0.0,
                "error": str(e)
            }
    
    async def _get_customer_context(self, customer_email: str) -> Dict[str, Any]:
        """Get customer context from Supabase."""
        try:
            # Get recent interactions
            interactions_response = self.supabase.table("customer_interactions").select("*").eq(
                "customer_email", customer_email
            ).order("created_at", desc=True).limit(10).execute()
            
            interactions = interactions_response.data if interactions_response.data else []
            
            # Get customer orders (if available)
            orders_response = self.supabase.table("orders").select("*").eq(
                "customer_email", customer_email
            ).order("created_at", desc=True).limit(5).execute()
            
            orders = orders_response.data if orders_response.data else []
            
            # Get SKU mappings
            sku_response = self.supabase.table("sku_mappings").select("*").eq(
                "customer_email", customer_email
            ).eq("active", True).execute()
            
            sku_mappings = sku_response.data if sku_response.data else []
            
            return {
                "recent_interactions": interactions,
                "recent_orders": orders,
                "sku_mappings": sku_mappings,
                "interaction_count": len(interactions)
            }
            
        except Exception as e:
            logger.error(f"❌ Error getting customer context: {e}")
            return {"error": str(e)}
    
    async def _get_temporal_context(self, customer_email: str) -> Dict[str, Any]:
        """Get temporal context from Graphiti."""
        try:
            if not self.graphiti:
                return {}
            
            # Search for relevant memories
            memories = await self.graphiti.search(
                query=f"customer {customer_email}",
                limit=5
            )
            
            return {
                "relevant_memories": memories,
                "memory_count": len(memories)
            }
            
        except Exception as e:
            logger.error(f"❌ Error getting temporal context: {e}")
            return {"error": str(e)}
    
    async def _generate_response_with_mistral(
        self,
        customer_email: str,
        query: str,
        context: Dict[str, Any],
        temporal_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate response using Mistral AI."""
        try:
            # Prepare context for Mistral
            context_summary = self._prepare_context_summary(context, temporal_context)
            
            # Create prompt
            prompt = f"""
            You are a helpful customer support agent. A customer has contacted us with a query.
            
            Customer: {customer_email}
            Query: {query}
            
            Customer Context:
            {context_summary}
            
            Please provide a helpful, professional response. Consider:
            1. The customer's history and previous interactions
            2. Any relevant orders or SKU mappings
            3. Be empathetic and solution-focused
            4. If you need more information, ask specific questions
            5. Suggest next steps when appropriate
            
            Respond in JSON format with:
            - "response": Your helpful response to the customer
            - "confidence": Confidence level (0.0 to 1.0)
            - "requires_escalation": Boolean if human intervention needed
            - "suggested_actions": List of suggested follow-up actions
            - "referenced_orders": List of any order IDs mentioned
            - "referenced_skus": List of any SKUs mentioned
            """
            
            # Call Mistral API
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://api.mistral.ai/v1/chat/completions",
                    headers={
                        "Authorization": f"Bearer {self.mistral_api_key}",
                        "Content-Type": "application/json"
                    },
                    json={
                        "model": "mistral-large-latest",
                        "messages": [
                            {"role": "user", "content": prompt}
                        ],
                        "temperature": 0.7,
                        "max_tokens": 1500
                    }
                )
                
                if response.status_code == 200:
                    result = response.json()
                    content = result['choices'][0]['message']['content']
                    
                    # Try to parse JSON response
                    try:
                        parsed_response = json.loads(content)
                        
                        # Ensure required fields
                        parsed_response.setdefault("confidence", 0.8)
                        parsed_response.setdefault("requires_escalation", False)
                        parsed_response.setdefault("suggested_actions", [])
                        parsed_response.setdefault("referenced_orders", [])
                        parsed_response.setdefault("referenced_skus", [])
                        
                        return parsed_response
                        
                    except json.JSONDecodeError:
                        # Fallback if not valid JSON
                        return {
                            "response": content,
                            "confidence": 0.7,
                            "requires_escalation": False,
                            "suggested_actions": [],
                            "referenced_orders": [],
                            "referenced_skus": [],
                            "raw_response": content
                        }
                else:
                    logger.error(f"Mistral API error: {response.status_code}")
                    return {
                        "response": "I apologize, but I'm having trouble processing your request right now. Please try again in a moment.",
                        "confidence": 0.0,
                        "error": f"API error: {response.status_code}"
                    }
                    
        except Exception as e:
            logger.error(f"❌ Error generating response with Mistral: {e}")
            return {
                "response": "I apologize, but I'm experiencing technical difficulties. Please try again later.",
                "confidence": 0.0,
                "error": str(e)
            }
    
    def _prepare_context_summary(self, context: Dict[str, Any], temporal_context: Dict[str, Any]) -> str:
        """Prepare context summary for Mistral."""
        summary_parts = []
        
        # Recent interactions
        if context.get("recent_interactions"):
            summary_parts.append(f"Recent interactions: {len(context['recent_interactions'])} interactions")
            for interaction in context["recent_interactions"][:3]:  # Last 3
                summary_parts.append(f"- {interaction.get('interaction_type', 'unknown')}: {interaction.get('content', '')[:100]}...")
        
        # Recent orders
        if context.get("recent_orders"):
            summary_parts.append(f"Recent orders: {len(context['recent_orders'])} orders")
            for order in context["recent_orders"][:2]:  # Last 2
                summary_parts.append(f"- Order {order.get('id', 'unknown')}: {order.get('status', 'unknown')}")
        
        # SKU mappings
        if context.get("sku_mappings"):
            summary_parts.append(f"SKU mappings: {len(context['sku_mappings'])} mappings available")
        
        # Temporal context
        if temporal_context.get("relevant_memories"):
            summary_parts.append(f"Relevant memories: {len(temporal_context['relevant_memories'])} memories")
        
        return "\n".join(summary_parts) if summary_parts else "No previous context available"
    
    async def _store_support_interaction(self, customer_email: str, query: str, response: Dict[str, Any]):
        """Store support interaction in Supabase."""
        try:
            interaction_data = {
                "customer_email": customer_email,
                "interaction_type": "support_query",
                "content": query,
                "response": response.get("response", ""),
                "metadata": {
                    "confidence": response.get("confidence", 0.0),
                    "requires_escalation": response.get("requires_escalation", False),
                    "suggested_actions": response.get("suggested_actions", []),
                    "referenced_orders": response.get("referenced_orders", []),
                    "referenced_skus": response.get("referenced_skus", [])
                },
                "created_at": datetime.utcnow().isoformat()
            }
            
            self.supabase.table("customer_interactions").insert(interaction_data).execute()
            logger.debug(f"Stored support interaction for {customer_email}")
            
        except Exception as e:
            logger.error(f"❌ Failed to store support interaction: {e}")
    
    async def _update_support_knowledge(self, customer_email: str, query: str, response: Dict[str, Any]):
        """Update temporal knowledge in Graphiti."""
        try:
            if self.graphiti:
                memories = [
                    f"Customer {customer_email} asked: {query}",
                    f"Support response provided with confidence {response.get('confidence', 0.0)}",
                    f"Response: {response.get('response', '')[:200]}..."
                ]
                
                # Add escalation info if needed
                if response.get("requires_escalation"):
                    memories.append(f"Query from {customer_email} requires human escalation")
                
                await self.graphiti.add_episodic_memories(memories)
                logger.debug(f"Updated support knowledge for {customer_email}")
                
        except Exception as e:
            logger.error(f"❌ Failed to update support knowledge: {e}")
    
    async def get_agent_stats(self) -> Dict[str, Any]:
        """Get agent statistics."""
        try:
            # Get recent interactions
            recent_interactions = self.supabase.table("customer_interactions").select("*").eq(
                "interaction_type", "support_query"
            ).gte("created_at", (datetime.utcnow() - timedelta(days=7)).isoformat()).execute()
            
            interactions = recent_interactions.data if recent_interactions.data else []
            
            # Calculate stats
            total_queries = len(interactions)
            escalated_queries = len([i for i in interactions if i.get('metadata', {}).get('requires_escalation', False)])
            
            avg_confidence = 0.0
            if total_queries > 0:
                confidences = [i.get('metadata', {}).get('confidence', 0.0) for i in interactions]
                avg_confidence = sum(confidences) / len(confidences)
            
            return {
                "total_queries_last_7_days": total_queries,
                "escalated_queries": escalated_queries,
                "escalation_rate": (escalated_queries / total_queries * 100) if total_queries > 0 else 0,
                "average_confidence": avg_confidence,
                "agent_type": "mistral_support_agent"
            }
            
        except Exception as e:
            logger.error(f"❌ Error getting agent stats: {e}")
            return {"error": str(e)}
