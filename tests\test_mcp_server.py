"""
Unit tests for the MCP server.
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from fastapi.testclient import TestClient

from mcp_server.main import app
from mcp_server.endpoints import get_endpoint_config, MYOB_EXO_ENDPOINTS
from mcp_server.sku_mapping import SKUMappingManager


@pytest.mark.unit
class TestMCPServerEndpoints:
    """Test MCP server endpoint configurations."""
    
    def test_get_endpoint_config_valid(self):
        """Test getting valid endpoint configuration."""
        # Act
        config = get_endpoint_config("debtor")
        
        # Assert
        assert config is not None
        assert config.endpoint == "/debtor"
        assert config.category.value == "debtors"
        assert "GET" in [method.value for method in config.methods]
    
    def test_get_endpoint_config_invalid(self):
        """Test getting invalid endpoint configuration."""
        # Act
        config = get_endpoint_config("nonexistent")
        
        # Assert
        assert config is None
    
    def test_all_endpoints_have_required_fields(self):
        """Test that all endpoints have required configuration fields."""
        for name, config in MYOB_EXO_ENDPOINTS.items():
            # Assert
            assert config.endpoint is not None
            assert config.methods is not None
            assert len(config.methods) > 0
            assert config.category is not None
            assert config.description is not None
            assert config.response_key is not None


@pytest.mark.unit
class TestSKUMappingManager:
    """Test SKU mapping manager."""
    
    @pytest.fixture
    async def sku_manager(self, mock_database_operations, mock_settings):
        """Create SKU mapping manager with mocked dependencies."""
        with patch('mcp_server.sku_mapping.get_database_operations', return_value=mock_database_operations):
            manager = SKUMappingManager()
            return manager
    
    async def test_get_internal_sku_found(self, sku_manager, mock_database_operations):
        """Test getting internal SKU when mapping exists."""
        # Arrange
        mock_database_operations.get_sku_mapping.return_value = {
            "internal_sku": "PROD001",
            "customer_sku": "CUST-001"
        }
        
        # Act
        result = await sku_manager.get_internal_sku("<EMAIL>", "CUST-001")
        
        # Assert
        assert result == "PROD001"
        mock_database_operations.get_sku_mapping.assert_called_once_with("<EMAIL>", "CUST-001")
    
    async def test_get_internal_sku_not_found(self, sku_manager, mock_database_operations):
        """Test getting internal SKU when mapping doesn't exist."""
        # Arrange
        mock_database_operations.get_sku_mapping.return_value = None
        
        # Act
        result = await sku_manager.get_internal_sku("<EMAIL>", "CUST-001")
        
        # Assert
        assert result is None
    
    async def test_create_mapping_success(self, sku_manager, mock_database_operations):
        """Test creating a new SKU mapping."""
        # Arrange
        mock_database_operations.get_sku_mapping.return_value = None  # No existing mapping
        mock_database_operations.create_sku_mapping.return_value = {"id": 1}
        
        # Act
        result = await sku_manager.create_mapping(
            "<EMAIL>", "CUST-001", "PROD001", "Test Product"
        )
        
        # Assert
        assert result["id"] == 1
        mock_database_operations.create_sku_mapping.assert_called_once()
    
    async def test_create_mapping_already_exists(self, sku_manager, mock_database_operations):
        """Test creating a mapping that already exists."""
        # Arrange
        existing_mapping = {
            "internal_sku": "PROD001",
            "customer_sku": "CUST-001"
        }
        mock_database_operations.get_sku_mapping.return_value = existing_mapping
        
        # Act
        result = await sku_manager.create_mapping(
            "<EMAIL>", "CUST-001", "PROD001"
        )
        
        # Assert
        assert result == existing_mapping
    
    async def test_get_customer_mappings(self, sku_manager, mock_database_operations):
        """Test getting all mappings for a customer."""
        # Arrange
        expected_mappings = [
            {"customer_sku": "CUST-001", "internal_sku": "PROD001"},
            {"customer_sku": "CUST-002", "internal_sku": "PROD002"}
        ]
        mock_database_operations.get_customer_sku_mappings.return_value = expected_mappings
        
        # Act
        result = await sku_manager.get_customer_mappings("<EMAIL>")
        
        # Assert
        assert result == expected_mappings
        mock_database_operations.get_customer_sku_mappings.assert_called_once_with("<EMAIL>")
    
    async def test_cache_functionality(self, sku_manager, mock_database_operations):
        """Test SKU mapping caching."""
        # Arrange
        mock_database_operations.get_sku_mapping.return_value = {
            "internal_sku": "PROD001"
        }
        
        # Act - First call should hit database
        result1 = await sku_manager.get_internal_sku("<EMAIL>", "CUST-001")
        
        # Act - Second call should hit cache
        result2 = await sku_manager.get_internal_sku("<EMAIL>", "CUST-001")
        
        # Assert
        assert result1 == "PROD001"
        assert result2 == "PROD001"
        # Database should only be called once due to caching
        assert mock_database_operations.get_sku_mapping.call_count == 1
    
    async def test_clear_cache(self, sku_manager):
        """Test clearing the SKU mapping cache."""
        # Arrange - Add something to cache
        await sku_manager._cache_mapping("<EMAIL>", "CUST-001", "PROD001")
        
        # Act
        await sku_manager.clear_cache()
        
        # Assert
        stats = await sku_manager.get_cache_stats()
        assert stats["total_mappings"] == 0
    
    async def test_bulk_create_mappings(self, sku_manager, mock_database_operations):
        """Test bulk creation of SKU mappings."""
        # Arrange
        mappings = [
            {
                "customer_email": "<EMAIL>",
                "customer_sku": "CUST-001",
                "internal_sku": "PROD001"
            },
            {
                "customer_email": "<EMAIL>",
                "customer_sku": "CUST-002",
                "internal_sku": "PROD002"
            }
        ]
        mock_database_operations.get_sku_mapping.return_value = None
        mock_database_operations.create_sku_mapping.return_value = {"id": 1}
        
        # Act
        result = await sku_manager.bulk_create_mappings(mappings)
        
        # Assert
        assert result["created"] == 2
        assert result["errors"] == 0
        assert mock_database_operations.create_sku_mapping.call_count == 2


@pytest.mark.integration
class TestMCPServerAPI:
    """Test MCP server API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    @patch('mcp_server.main.setup_environment')
    @patch('mcp_server.main.initialize_supabase')
    def test_health_endpoint(self, mock_init_supabase, mock_setup_env, client):
        """Test health check endpoint."""
        # Arrange
        mock_init_supabase.return_value = None
        
        with patch('mcp_server.main.db_health_check') as mock_health:
            mock_health.return_value = {"supabase_connected": True}
            
            with patch('mcp_server.main.call_myob_exo_api') as mock_myob:
                mock_myob.return_value = {"success": True}
                
                # Act
                response = client.get("/health")
                
                # Assert
                assert response.status_code == 200
                data = response.json()
                assert "status" in data
                assert "timestamp" in data
    
    def test_endpoints_list(self, client):
        """Test listing all endpoints."""
        # Act
        response = client.get("/endpoints")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "endpoints" in data
        assert "summary" in data
        assert len(data["endpoints"]) > 0
    
    def test_endpoint_info_valid(self, client):
        """Test getting info for a valid endpoint."""
        # Act
        response = client.get("/endpoints/debtor")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "debtor"
        assert data["endpoint"] == "/debtor"
        assert "methods" in data
        assert "category" in data
    
    def test_endpoint_info_invalid(self, client):
        """Test getting info for an invalid endpoint."""
        # Act
        response = client.get("/endpoints/nonexistent")
        
        # Assert
        assert response.status_code == 404
    
    @patch('mcp_server.main.get_sku_mapping_manager')
    def test_create_sku_mapping(self, mock_get_manager, client):
        """Test creating a SKU mapping."""
        # Arrange
        mock_manager = AsyncMock()
        mock_manager.create_mapping.return_value = {"id": 1}
        mock_get_manager.return_value = mock_manager
        
        mapping_data = {
            "customer_email": "<EMAIL>",
            "customer_sku": "CUST-001",
            "internal_sku": "PROD001",
            "description": "Test Product"
        }
        
        # Act
        response = client.post("/sku-mapping", json=mapping_data)
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    @patch('mcp_server.main.get_sku_mapping_manager')
    def test_get_customer_sku_mappings(self, mock_get_manager, client):
        """Test getting customer SKU mappings."""
        # Arrange
        mock_manager = AsyncMock()
        mock_manager.get_customer_mappings.return_value = [
            {"customer_sku": "CUST-001", "internal_sku": "PROD001"}
        ]
        mock_get_manager.return_value = mock_manager
        
        # Act
        response = client.get("/sku-mapping/<EMAIL>")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert len(data["data"]) == 1
    
    @patch('mcp_server.main.map_customer_sku')
    def test_get_specific_sku_mapping(self, mock_map_sku, client):
        """Test getting a specific SKU mapping."""
        # Arrange
        mock_map_sku.return_value = "PROD001"
        
        # Act
        response = client.get("/sku-mapping/<EMAIL>/CUST-001")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["internal_sku"] == "PROD001"
    
    @patch('mcp_server.main.map_customer_sku')
    def test_get_sku_mapping_not_found(self, mock_map_sku, client):
        """Test getting a SKU mapping that doesn't exist."""
        # Arrange
        mock_map_sku.return_value = None
        
        # Act
        response = client.get("/sku-mapping/<EMAIL>/NONEXISTENT")
        
        # Assert
        assert response.status_code == 404
