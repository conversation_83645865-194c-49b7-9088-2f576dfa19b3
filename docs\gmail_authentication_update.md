# Gmail API Authentication Update

## Overview
This document outlines the changes made to Gmail API authentication to use the official Google-recommended `credentials.json` → `token.json` → `token.pickle` flow.

## Changes Made

### ✅ Updated Authentication Flow

**Before (Environment Variables):**
```env
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
GOOGLE_REDIRECT_URI=http://localhost:8080/callback
```

**After (Credentials File):**
```
credentials.json (downloaded from Google Cloud Console)
↓
token.json (generated automatically)
↓
token.pickle (preferred for persistence)
```

### ✅ File Structure Changes

#### **New Required Files:**
- `credentials.json` - OAuth 2.0 client configuration from Google Cloud Console
- `token.json` - Generated automatically during first authentication
- `token.pickle` - Preferred token storage format (more reliable)

#### **Files Modified:**
1. `tools/gmail_tools.py` - Updated authentication logic
2. `config/settings.py` - Removed Google client ID/secret requirements
3. `config/environment.py` - Updated validation to check for credentials.json
4. `.env.example` - Updated Gmail configuration section

### ✅ Authentication Logic Improvements

#### **New Features:**
1. **Multiple Token Formats**: Supports both `token.pickle` (preferred) and `token.json`
2. **Automatic Token Refresh**: Handles expired tokens automatically
3. **Fallback Loading**: Tries pickle first, then JSON
4. **Better Error Handling**: Clear error messages for missing files
5. **Dual Token Saving**: Saves both formats for compatibility

#### **Authentication Flow:**
```python
1. Check for token.pickle (fastest, most reliable)
2. Fallback to token.json if pickle doesn't exist
3. If no valid tokens, check for credentials.json
4. Run OAuth flow if needed (opens browser)
5. Save new tokens in both formats
6. Return valid credentials
```

### ✅ Enhanced Scopes

**Updated Gmail Scopes:**
```python
gmail_scopes: List[str] = [
    "https://www.googleapis.com/auth/gmail.readonly",    # Read emails
    "https://www.googleapis.com/auth/gmail.send",        # Send emails  
    "https://www.googleapis.com/auth/gmail.modify"       # Modify emails (mark read, etc.)
]
```

## Setup Instructions

### 1. **Download credentials.json**

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project (or create one)
3. Enable the Gmail API
4. Go to **APIs & Services** → **Credentials**
5. Click **Create Credentials** → **OAuth 2.0 Client ID**
6. Choose **Desktop application**
7. Download the JSON file
8. Rename it to `credentials.json`
9. Place it in your project root directory

### 2. **Configure OAuth Consent Screen**

1. Go to **APIs & Services** → **OAuth consent screen**
2. Choose **External** user type
3. Fill in required fields:
   - App name
   - User support email
   - Developer contact information
4. Add your Gmail scopes:
   - `https://www.googleapis.com/auth/gmail.readonly`
   - `https://www.googleapis.com/auth/gmail.send`
   - `https://www.googleapis.com/auth/gmail.modify`
5. Add test users (your Gmail address)

### 3. **Update Environment Variables**

Remove these from your `.env` file:
```env
# Remove these - no longer needed
GOOGLE_CLIENT_ID=...
GOOGLE_CLIENT_SECRET=...
GOOGLE_REDIRECT_URI=...
```

Keep this (optional):
```env
# Optional - uses defaults if not specified
GMAIL_SCOPES=https://www.googleapis.com/auth/gmail.readonly,https://www.googleapis.com/auth/gmail.send,https://www.googleapis.com/auth/gmail.modify
```

### 4. **Test Authentication**

Run the test script:
```bash
python test_gmail_auth.py
```

This will:
- Validate your `credentials.json` file
- Test the OAuth flow
- Create token files
- Verify Gmail API access

## File Locations

### **Project Root Files:**
```
project_root/
├── credentials.json     # OAuth client config (required)
├── token.json          # Generated token (JSON format)
├── token.pickle        # Generated token (pickle format, preferred)
└── test_gmail_auth.py  # Authentication test script
```

### **No Longer Used:**
- `credentials/gmail_credentials.json`
- `credentials/gmail_token.pickle`

## Authentication Process

### **First Time Setup:**
1. User runs application
2. System checks for `credentials.json`
3. Opens browser for OAuth consent
4. User grants permissions
5. System saves tokens in both formats
6. Future runs use saved tokens

### **Subsequent Runs:**
1. System loads `token.pickle` (fastest)
2. If pickle fails, tries `token.json`
3. If tokens expired, refreshes automatically
4. If refresh fails, re-runs OAuth flow

## Error Handling

### **Common Errors and Solutions:**

**❌ "credentials.json not found"**
- Download credentials.json from Google Cloud Console
- Place in project root directory

**❌ "Invalid credentials.json format"**
- Ensure you downloaded OAuth 2.0 client credentials
- Check file contains "web" or "installed" configuration

**❌ "OAuth flow failed"**
- Check OAuth consent screen is configured
- Verify Gmail API is enabled
- Ensure test users are added

**❌ "Token refresh failed"**
- Delete existing token files
- Re-run OAuth flow
- Check internet connectivity

## Security Considerations

### **Token Security:**
- `token.pickle` and `token.json` contain sensitive access tokens
- Add to `.gitignore` to prevent committing to version control
- Store securely in production environments

### **Credentials Security:**
- `credentials.json` contains client configuration (not secret)
- Can be committed to version control for team projects
- Contains redirect URIs and OAuth settings

## Testing

### **Test Script Features:**
- Validates `credentials.json` format
- Tests OAuth flow
- Verifies Gmail API access
- Checks token file creation
- Provides troubleshooting guidance

### **Run Tests:**
```bash
# Test Gmail authentication
python test_gmail_auth.py

# Test in your application
python -c "
import asyncio
from tools.gmail_tools import get_gmail_manager

async def test():
    manager = await get_gmail_manager()
    messages = await manager.get_messages('is:unread', 5)
    print(f'Found {len(messages)} unread messages')

asyncio.run(test())
"
```

## Migration Notes

### **For Existing Users:**
1. Old token files in `credentials/` directory are no longer used
2. Remove old environment variables from `.env`
3. Download new `credentials.json` file
4. Run test script to verify setup
5. Delete old credential files if desired

### **For New Users:**
1. Follow setup instructions above
2. Run test script
3. Start using Gmail functionality

## Benefits of New Approach

1. **Official Google Pattern**: Follows Google's recommended authentication flow
2. **Better Reliability**: Pickle format is more robust than JSON for credentials
3. **Simplified Configuration**: No need for individual client ID/secret variables
4. **Automatic Token Management**: Handles refresh and expiration automatically
5. **Better Error Messages**: Clear guidance for troubleshooting
6. **Team Friendly**: credentials.json can be shared across team members

## Next Steps

1. **Test the authentication** using the provided test script
2. **Verify Gmail functionality** in your application
3. **Update deployment scripts** to include credentials.json
4. **Add token files to .gitignore** for security
5. **Document team setup process** for new developers
