"""
Main entry point for the Multi-Agent Sales Support System.
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path
from typing import Optional

import structlog
from config.environment import setup_environment, get_environment_info, is_development
from config.settings import get_settings, create_directories

# Configure Python logging to show console output
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/sales_support.log', mode='a')
    ]
)

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer() if not is_development() else structlog.dev.ConsoleRenderer(),
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


class SalesupportSystem:
    """Main application class for the sales support system."""
    
    def __init__(self):
        """Initialize the sales support system."""
        self.settings = get_settings()
        self.running = False
        self.tasks = []

        # Initialize agent references
        self.email_agent = None
        self.support_agent = None
        
    async def startup(self) -> None:
        """Start up the sales support system."""
        try:
            print("🔄 Starting Multi-Agent Sales Support System...")
            logger.info("Starting Multi-Agent Sales Support System", version=self.settings.app_version)

            # Create necessary directories
            print("📁 Creating directories...")
            create_directories()
            logger.info("Created necessary directories")

            # Initialize database connections
            print("🗄️ Initializing database...")
            await self._initialize_database()

            # Initialize knowledge graph
            print("🕸️ Initializing knowledge graph...")
            await self._initialize_knowledge_graph()

            # Initialize agents
            print("🤖 Initializing agents...")
            await self._initialize_agents()

            # Start background tasks
            print("⚙️ Starting background tasks...")
            await self._start_background_tasks()

            self.running = True
            print("✅ Sales Support System started successfully!")
            logger.info("Sales Support System started successfully")
            
        except Exception as e:
            logger.error("Failed to start Sales Support System", error=str(e))
            raise
    
    async def shutdown(self) -> None:
        """Shut down the sales support system."""
        logger.info("Shutting down Sales Support System")
        self.running = False
        
        # Cancel all background tasks
        for task in self.tasks:
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        # Close database connections
        await self._cleanup_database()
        
        # Close knowledge graph connections
        await self._cleanup_knowledge_graph()
        
        logger.info("Sales Support System shut down complete")
    
    async def _initialize_database(self) -> None:
        """Initialize database connections and setup."""
        try:
            from database.supabase_setup import initialize_supabase
            from database.operations import setup_database_schema
            
            # Initialize Supabase connection
            await initialize_supabase()
            logger.info("Supabase connection initialized")
            
            # Setup database schema
            await setup_database_schema()
            logger.info("Database schema setup complete")
            
        except ImportError as e:
            logger.warning("Database modules not yet implemented", error=str(e))
        except Exception as e:
            logger.error("Failed to initialize database", error=str(e))
            raise
    
    async def _initialize_knowledge_graph(self) -> None:
        """Initialize knowledge graph connections."""
        try:
            if self.settings.enable_knowledge_graph:
                from knowledge_graph.graphiti_setup import initialize_graphiti
                
                await initialize_graphiti()
                logger.info("Knowledge graph initialized")
            else:
                logger.info("Knowledge graph disabled in configuration")
                
        except ImportError as e:
            logger.warning("Knowledge graph modules not yet implemented", error=str(e))
        except Exception as e:
            logger.error("Failed to initialize knowledge graph", error=str(e))
            raise
    
    async def _initialize_agents(self) -> None:
        """Initialize all agents."""
        try:
            logger.info("Initializing agents...")

            # Email monitoring agent
            if self.settings.enable_email_monitoring:
                from agents.email_monitor import EmailMonitoringAgent
                self.email_agent = EmailMonitoringAgent()
                await self.email_agent.initialize()
                logger.info("Email monitoring agent initialized")
            else:
                self.email_agent = None
                logger.info("Email monitoring disabled")

            # Customer support agent
            from agents.customer_support import get_customer_support_agent
            self.support_agent = await get_customer_support_agent()
            await self.support_agent.initialize()  # Initialize async components
            logger.info("Customer support agent initialized")

            # Order processing agent
            logger.info("Order processing agent will be initialized")

            # Purchasing agent
            logger.info("Purchasing agent will be initialized")

            logger.info("All agents initialized")

        except Exception as e:
            logger.error("Failed to initialize agents", error=str(e))
            raise
    
    async def _start_background_tasks(self) -> None:
        """Start background tasks."""
        try:
            # Email monitoring task
            if self.settings.enable_email_monitoring:
                task = asyncio.create_task(self._email_monitoring_loop())
                self.tasks.append(task)
                logger.info("Email monitoring task started")
            
            # Inventory monitoring task
            task = asyncio.create_task(self._inventory_monitoring_loop())
            self.tasks.append(task)
            logger.info("Inventory monitoring task started")
            
            # Health check task
            task = asyncio.create_task(self._health_check_loop())
            self.tasks.append(task)
            logger.info("Health check task started")
            
        except Exception as e:
            logger.error("Failed to start background tasks", error=str(e))
            raise
    
    async def _email_monitoring_loop(self) -> None:
        """Background task for email monitoring."""
        if not self.email_agent:
            logger.info("Email monitoring disabled, skipping email monitoring loop")
            return

        while self.running:
            try:
                # Use the actual email monitoring agent
                await self.email_agent._monitoring_cycle()
                logger.debug("Email monitoring cycle completed")
                await asyncio.sleep(self.settings.email_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Error in email monitoring loop", error=str(e))
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _inventory_monitoring_loop(self) -> None:
        """Background task for inventory monitoring."""
        while self.running:
            try:
                # Inventory monitoring logic will be implemented
                logger.debug("Inventory monitoring check")
                await asyncio.sleep(self.settings.inventory_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Error in inventory monitoring loop", error=str(e))
                await asyncio.sleep(300)  # Wait before retrying
    
    async def _health_check_loop(self) -> None:
        """Background task for health checks."""
        while self.running:
            try:
                # Health check logic will be implemented
                logger.debug("Health check")
                await asyncio.sleep(self.settings.health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Error in health check loop", error=str(e))
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _cleanup_database(self) -> None:
        """Clean up database connections."""
        try:
            # Database cleanup logic will be implemented
            logger.info("Database cleanup complete")
        except Exception as e:
            logger.error("Error during database cleanup", error=str(e))
    
    async def _cleanup_knowledge_graph(self) -> None:
        """Clean up knowledge graph connections."""
        try:
            # Knowledge graph cleanup logic will be implemented
            logger.info("Knowledge graph cleanup complete")
        except Exception as e:
            logger.error("Error during knowledge graph cleanup", error=str(e))
    
    async def run(self) -> None:
        """Run the sales support system."""
        await self.startup()
        
        try:
            # Keep the system running
            while self.running:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        finally:
            await self.shutdown()


async def main() -> None:
    """Main function."""
    try:
        # Setup environment
        print("🚀 Starting Sales Support System...")
        print("📋 Setting up environment...")
        logger.info("Setting up environment")
        validation_result = setup_environment()
        
        # Log environment info
        print("📊 Getting environment information...")
        env_info = get_environment_info()
        logger.info("Environment information", **env_info)
        print(f"🔧 Environment: {env_info.get('environment', 'unknown')}")
        print(f"🐛 Debug mode: {env_info.get('debug', False)}")
        print(f"📝 Log level: {env_info.get('log_level', 'INFO')}")

        # Create and run the system
        print("🏗️ Creating Sales Support System...")
        system = SalesupportSystem()
        
        # Setup signal handlers
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}")
            system.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Run the system
        print("🚀 Starting system...")
        await system.run()
        
    except Exception as e:
        logger.error("Fatal error in main", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    # Run the main function
    asyncio.run(main())
