# 🔍 Enhanced Search Functionality Guide

## Overview
The Human Review Dashboard now includes comprehensive search capabilities to help you quickly find and analyze emails based on various criteria.

## How to Access
1. Run the dashboard: `python human_review_dashboard.py`
2. Select option **4: 🔍 Search Emails** from the main menu
3. Choose your search type and enter your query

## Search Types Available

### 1. 📝 Content Search
- **What it does**: Searches through email body text
- **Use case**: Find emails containing specific words or phrases
- **Example**: Search for "refund" to find all refund-related emails

### 2. 👤 Sender Search  
- **What it does**: Searches by email address
- **Use case**: Find all emails from a specific customer or domain
- **Example**: Search for "@teamsystems.net.au" to find internal emails

### 3. 📋 Subject Search
- **What it does**: Searches email subject lines
- **Use case**: Find emails with specific subjects
- **Example**: Search for "invoice" to find billing-related emails

### 4. 🎯 Intent Search
- **What it does**: Searches by AI-detected intent
- **Use case**: Find emails categorized with specific intents
- **Example**: Search for "complaint" to find complaint emails

### 5. ⚡ Urgency Search
- **What it does**: Filters by urgency level
- **Use case**: Find emails of specific priority
- **Options**: low, medium, high, urgent

### 6. 🧠 Semantic Search (Advanced)
- **What it does**: AI-powered similarity search using vector embeddings
- **Use case**: Find emails with similar meaning, not just exact words
- **Example**: Search "shipping delay" might find emails about "delivery problems"
- **Note**: Requires vector embeddings to be set up

### 7. 🔄 Multi-field Search
- **What it does**: Searches across content, sender, and subject simultaneously
- **Use case**: Comprehensive search when you're not sure which field contains your query
- **Example**: Search for "payment" across all fields

## Time Range Options
- **24h**: Last 24 hours
- **7d**: Last 7 days (default)
- **30d**: Last 30 days  
- **all**: All available emails

## Search Results Features

### Results Display
- Shows up to 50 results in a formatted table
- Displays: timestamp, sender, subject, intent, urgency, match type
- Color-coded urgency levels for quick identification

### Review from Results
- After viewing results, you can select any email for detailed review
- Full analysis interface available directly from search results
- All review actions available (approve, reject, escalate, etc.)

## Advanced Features

### Quick Search Function
- Available programmatically via `dashboard.quick_search(query)`
- Returns top 10 most relevant results
- Automatically tries semantic search first, falls back to content search

### Context-Aware Re-analysis
- When re-analyzing emails, system searches for similar emails for context
- Helps improve analysis accuracy with historical data

### Learning Integration
- Search results can be used for learning and feedback
- Approved analyses are stored with vector embeddings for future similarity search

## Technical Implementation

### Vector Embeddings
- Uses OpenAI embeddings for semantic search
- Stores embeddings in Supabase with pgvector
- Calculates cosine similarity for relevance scoring

### Database Queries
- Efficient Supabase queries with proper indexing
- Time-based filtering for performance
- Fallback mechanisms for robustness

### Error Handling
- Graceful degradation when vector search unavailable
- Fallback to text-based search methods
- Clear error messages and recovery options

## Usage Tips

1. **Start Broad**: Use content or multi-field search for general queries
2. **Get Specific**: Use sender/subject search when you know the source
3. **Find Patterns**: Use intent/urgency search to identify trends
4. **Leverage AI**: Use semantic search for concept-based queries
5. **Time Scope**: Adjust time range based on your needs

## Troubleshooting

### No Results Found
- Try broader search terms
- Expand time range
- Use multi-field search
- Check spelling and case sensitivity

### Semantic Search Not Working
- Vector embeddings may not be set up
- Falls back to content search automatically
- Check vector_embeddings.py module availability

### Performance Issues
- Limit time range for faster results
- Use specific search types instead of multi-field
- Consider database indexing for large datasets

## Integration with Existing Features

### Review Workflow
- Search results integrate seamlessly with existing review process
- All review actions available from search results
- Learning feedback captured for search improvements

### Statistics and Analytics
- Search patterns can inform system statistics
- Popular search terms indicate common issues
- Search success rates help optimize functionality

## Future Enhancements

- Saved search queries
- Search history and favorites
- Advanced filtering combinations
- Export search results
- Search analytics dashboard
