"""
Pytest configuration and fixtures for the multi-agent sales support system tests.
"""

import asyncio
import os
import pytest
import tempfile
from typing import Dict, Any, AsyncGenerator
from unittest.mock import AsyncMock, MagicMock, patch
from pathlib import Path

# Set test environment
os.environ["ENVIRONMENT"] = "testing"
os.environ["DEBUG"] = "true"
os.environ["LOG_LEVEL"] = "DEBUG"

# Mock API keys for testing
os.environ["MISTRAL_API_KEY"] = "test_mistral_key"
os.environ["OPENAI_API_KEY"] = "test_openai_key"
os.environ["GOOGLE_CLIENT_ID"] = "test_google_client_id"
os.environ["GOOGLE_CLIENT_SECRET"] = "test_google_client_secret"
os.environ["MYOB_EXO_API_URL"] = "https://test-myob-api.com"
os.environ["MYOB_EXO_API_KEY"] = "test_myob_key"
os.environ["SUPABASE_URL"] = "https://test-project.supabase.co"
os.environ["SUPABASE_SERVICE_KEY"] = "test_supabase_key"
os.environ["JWT_SECRET_KEY"] = "test_jwt_secret"
os.environ["FALKORDB_HOST"] = "localhost"
os.environ["FALKORDB_PORT"] = "6379"


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def mock_supabase_client():
    """Mock Supabase client for testing."""
    with patch('database.supabase_setup.create_client') as mock_create:
        mock_client = AsyncMock()
        mock_client.table.return_value.select.return_value.execute.return_value.data = []
        mock_client.table.return_value.insert.return_value.execute.return_value.data = [{"id": 1}]
        mock_client.table.return_value.update.return_value.execute.return_value.data = [{"id": 1}]
        mock_client.table.return_value.delete.return_value.execute.return_value.data = [{"id": 1}]
        mock_create.return_value = mock_client
        yield mock_client


@pytest.fixture
async def mock_mistral_model():
    """Mock Mistral AI model for testing."""
    with patch('pydantic_ai.models.mistral.MistralModel') as mock_model:
        mock_instance = AsyncMock()
        mock_model.return_value = mock_instance
        yield mock_instance


@pytest.fixture
async def mock_gmail_service():
    """Mock Gmail API service for testing."""
    with patch('googleapiclient.discovery.build') as mock_build:
        mock_service = MagicMock()
        
        # Mock messages list
        mock_service.users().messages().list().execute.return_value = {
            "messages": [{"id": "test_message_1"}, {"id": "test_message_2"}]
        }
        
        # Mock message get
        mock_service.users().messages().get().execute.return_value = {
            "id": "test_message_1",
            "threadId": "test_thread_1",
            "payload": {
                "headers": [
                    {"name": "From", "value": "<EMAIL>"},
                    {"name": "Subject", "value": "Test Subject"},
                    {"name": "Date", "value": "2024-01-01T00:00:00Z"}
                ],
                "body": {"data": "VGVzdCBtZXNzYWdlIGJvZHk="}  # Base64 encoded "Test message body"
            }
        }
        
        # Mock send
        mock_service.users().messages().send().execute.return_value = {"id": "sent_message_1"}
        
        mock_build.return_value = mock_service
        yield mock_service


@pytest.fixture
async def mock_myob_api():
    """Mock MYOB EXO API for testing."""
    with patch('httpx.AsyncClient') as mock_client:
        mock_response = AsyncMock()
        mock_response.is_success = True
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True, "data": []}
        
        mock_instance = AsyncMock()
        mock_instance.get.return_value = mock_response
        mock_instance.post.return_value = mock_response
        mock_instance.put.return_value = mock_response
        mock_instance.patch.return_value = mock_response
        
        mock_client.return_value = mock_instance
        yield mock_instance


@pytest.fixture
async def mock_falkordb():
    """Mock FalkorDB for testing."""
    with patch('falkordb.FalkorDB') as mock_falkor:
        mock_graph = MagicMock()
        mock_graph.query.return_value.result_set = []
        
        mock_client = MagicMock()
        mock_client.select_graph.return_value = mock_graph
        
        mock_falkor.return_value = mock_client
        yield mock_client


@pytest.fixture
def sample_customer_data():
    """Sample customer data for testing."""
    return {
        "customer_email": "<EMAIL>",
        "name": "Test Customer",
        "customer_id": "CUST001"
    }


@pytest.fixture
def sample_order_data():
    """Sample order data for testing."""
    return {
        "order_id": "ORD001",
        "customer_email": "<EMAIL>",
        "items": [
            {
                "sku": "PROD001",
                "quantity": 2,
                "unit_price": 10.00
            }
        ],
        "total_amount": 20.00,
        "status": "pending"
    }


@pytest.fixture
def sample_email_data():
    """Sample email data for testing."""
    return {
        "message_id": "test_message_1",
        "from": "<EMAIL>",
        "subject": "Order inquiry",
        "body": "I would like to check the status of my order ORD001",
        "attachments": []
    }


@pytest.fixture
def sample_pdf_data():
    """Sample PDF data for testing."""
    # This is a minimal PDF content for testing
    pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Test PDF Content) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000204 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
297
%%EOF"""
    return pdf_content


@pytest.fixture
def sample_sku_mapping():
    """Sample SKU mapping for testing."""
    return {
        "customer_email": "<EMAIL>",
        "customer_sku": "CUST-PROD-001",
        "internal_sku": "PROD001",
        "description": "Test Product"
    }


@pytest.fixture
def sample_interaction_data():
    """Sample interaction data for testing."""
    return {
        "customer_email": "<EMAIL>",
        "interaction_type": "email",
        "content": "Customer inquiry about order status",
        "metadata": {
            "agent": "email_monitor",
            "extracted_query": "order status inquiry"
        }
    }


@pytest.fixture
async def temp_directory():
    """Create a temporary directory for testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def mock_settings():
    """Mock settings for testing."""
    with patch('config.settings.get_settings') as mock_get_settings:
        mock_settings_obj = MagicMock()
        
        # Set common test settings
        mock_settings_obj.debug = True
        mock_settings_obj.log_level = "DEBUG"
        mock_settings_obj.environment = "testing"
        mock_settings_obj.mistral_api_key = "test_mistral_key"
        mock_settings_obj.openai_api_key = "test_openai_key"
        mock_settings_obj.supabase_url = "https://test-project.supabase.co"
        mock_settings_obj.supabase_service_key = "test_supabase_key"
        mock_settings_obj.myob_exo_api_url = "https://test-myob-api.com"
        mock_settings_obj.myob_exo_api_key = "test_myob_key"
        mock_settings_obj.email_check_interval = 60
        mock_settings_obj.inventory_check_interval = 300
        mock_settings_obj.support_context_limit = 5
        mock_settings_obj.order_processing_timeout = 30
        mock_settings_obj.enable_email_monitoring = True
        mock_settings_obj.enable_auto_purchasing = False
        mock_settings_obj.enable_knowledge_graph = True
        
        mock_get_settings.return_value = mock_settings_obj
        yield mock_settings_obj


@pytest.fixture
async def mock_database_operations():
    """Mock database operations for testing."""
    with patch('database.operations.get_database_operations') as mock_get_db:
        mock_db = AsyncMock()
        
        # Mock common database operations
        mock_db.create_interaction.return_value = {"id": 1}
        mock_db.get_interactions.return_value = []
        mock_db.create_sku_mapping.return_value = {"id": 1}
        mock_db.get_sku_mapping.return_value = None
        mock_db.create_order_record.return_value = {"id": 1}
        mock_db.get_customer_orders.return_value = []
        mock_db.log_email_processing.return_value = {"id": 1}
        mock_db.create_inventory_alert.return_value = {"id": 1}
        mock_db.get_active_inventory_alerts.return_value = []
        mock_db.store_agent_memory.return_value = {"id": 1}
        mock_db.get_agent_memory.return_value = []
        mock_db.log_api_usage.return_value = {"id": 1}
        
        mock_get_db.return_value = mock_db
        yield mock_db


@pytest.fixture
def mock_langchain_agent():
    """Mock LangChain agent for testing."""
    with patch('langchain.agents.AgentExecutor') as mock_executor:
        mock_agent = AsyncMock()
        mock_agent.ainvoke.return_value = {
            "output": "Test agent response",
            "intermediate_steps": []
        }
        mock_executor.return_value = mock_agent
        yield mock_agent


# Async test helpers
class AsyncContextManager:
    """Helper for async context managers in tests."""
    
    def __init__(self, async_func):
        self.async_func = async_func
    
    async def __aenter__(self):
        return await self.async_func()
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass


def async_mock_context(return_value=None):
    """Create an async mock context manager."""
    async def async_func():
        return return_value
    return AsyncContextManager(async_func)


# Test data generators
def generate_test_orders(count: int = 5) -> list:
    """Generate test order data."""
    orders = []
    for i in range(count):
        orders.append({
            "order_id": f"ORD{i:03d}",
            "customer_email": "<EMAIL>",
            "internal_sku": f"PROD{i:03d}",
            "customer_sku": f"CUST-PROD-{i:03d}",
            "quantity": i + 1,
            "unit_price": 10.00 * (i + 1),
            "total_amount": 10.00 * (i + 1) * (i + 1),
            "status": "completed",
            "created_at": f"2024-01-{i+1:02d}T00:00:00Z"
        })
    return orders


def generate_test_interactions(count: int = 5) -> list:
    """Generate test interaction data."""
    interactions = []
    for i in range(count):
        interactions.append({
            "id": i + 1,
            "customer_email": "<EMAIL>",
            "interaction_type": "email" if i % 2 == 0 else "support",
            "content": f"Test interaction {i + 1}",
            "metadata": {
                "agent": "test_agent",
                "confidence": 0.8 + (i * 0.05)
            },
            "created_at": f"2024-01-{i+1:02d}T00:00:00Z"
        })
    return interactions


# Pytest markers
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.slow = pytest.mark.slow
pytest.mark.requires_api = pytest.mark.requires_api
