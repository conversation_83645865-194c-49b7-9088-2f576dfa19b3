# Simplified AI Sales Support System Requirements
# Core dependencies for clean AI framework
# Updated: January 2025 with latest versions

# Environment and Configuration
python-dotenv==1.1.1  # Latest version

# HTTP Client for API calls
httpx==0.28.0  # Latest version

# Web API Framework
fastapi==0.116.1  # Latest version
uvicorn==0.35.0  # Latest version

# Database and Memory
supabase==2.17.0  # Latest version
psycopg2-binary==2.9.10

# Temporal Knowledge (Graphiti by Zep)
graphiti-core==0.18.0  # Latest version

# Gmail API
google-auth==2.40.3  # Latest version
google-auth-oauthlib==1.2.2
google-auth-httplib2==0.2.0
google-api-python-client==2.177.0  # Latest version

# AI Frameworks
pydantic-ai-slim==0.4.8  # Latest version
anthropic==0.60.0  # Latest version
openai==1.97.1  # Latest version
mistralai==1.9.3  # Latest version

# Logging and Utilities
structlog==25.4.0  # Latest version

# JSON handling
orjson==3.11.1  # Latest version

# Date/Time utilities
python-dateutil==2.9.0

# Async utilities
asyncio-mqtt==0.16.2

# PDF processing for email attachments
PyMuPDF==1.26.3  # Latest version
pypdf==5.9.0  # Latest version

# Rich CLI formatting
rich==14.1.0  # Latest version

# Redis for caching
redis==6.2.0  # Latest version

# Development and Testing (optional)
pytest==8.4.1  # Latest version
pytest-asyncio==1.1.0  # Latest version
