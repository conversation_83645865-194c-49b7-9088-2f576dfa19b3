#!/usr/bin/env python3
"""
Test script for MYOB EXO API integration.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from config.environment import setup_environment
from tools.myob_tools import get_myob_client, get_sku_mapping_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def test_myob_connection():
    """Test MYOB EXO API connection."""
    try:
        print("🔧 Setting up environment...")
        setup_environment()
        
        print("🔗 Testing MYOB client initialization...")
        myob_client = await get_myob_client()
        
        print("📋 Testing SKU mapping manager...")
        sku_manager = await get_sku_mapping_manager()
        
        print("✅ MYOB tools initialized successfully!")
        
        # Test basic API call (discovery endpoint)
        print("🔍 Testing MYOB API discovery endpoint...")
        try:
            discovery = await myob_client.get_discovery()
            print(f"✅ Discovery endpoint successful: {len(discovery.get('endpoints', []))} endpoints found")
        except Exception as e:
            print(f"⚠️ Discovery endpoint failed (expected if MYOB not configured): {e}")
        
        # Test SKU mapping functionality
        print("🏷️ Testing SKU mapping functionality...")
        test_mapping = await sku_manager.create_mapping(
            customer_email="<EMAIL>",
            customer_sku="TEST-001",
            internal_sku="INTERNAL-001",
            description="Test mapping"
        )
        print(f"✅ SKU mapping test: {test_mapping}")
        
        # Clean up
        await myob_client.close()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ MYOB integration test failed: {e}")
        return False


async def test_email_agent():
    """Test email monitoring agent."""
    try:
        print("📧 Testing email monitoring agent...")
        from agents.email_monitor import EmailMonitoringAgent
        
        email_agent = EmailMonitoringAgent()
        print("✅ Email monitoring agent created successfully!")
        
        # Test initialization (will fail without Gmail credentials, but should not crash)
        try:
            await email_agent.initialize()
            print("✅ Email agent initialized successfully!")
        except Exception as e:
            print(f"⚠️ Email agent initialization failed (expected without Gmail setup): {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Email agent test failed: {e}")
        return False


async def test_customer_support_agent():
    """Test customer support agent."""
    try:
        print("🤖 Testing customer support agent...")
        from agents.customer_support import get_customer_support_agent
        
        support_agent = await get_customer_support_agent()
        await support_agent.initialize()
        print("✅ Customer support agent initialized successfully!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Customer support agent test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Starting MYOB Integration Tests...")
    print("=" * 50)
    
    tests = [
        ("MYOB Connection", test_myob_connection),
        ("Email Agent", test_email_agent),
        ("Customer Support Agent", test_customer_support_agent),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            result = await test_func()
            results.append((test_name, result))
            print(f"✅ {test_name} test completed: {'PASSED' if result else 'FAILED'}")
        except Exception as e:
            logger.error(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! The system is ready for email and MYOB integration.")
    else:
        print("⚠️ Some tests failed. Check the configuration and dependencies.")


if __name__ == "__main__":
    asyncio.run(main())
