#!/usr/bin/env python3
"""
Test script to verify that temp files are being created during email processing.
"""

import os
import time
import json
from datetime import datetime

def check_temp_directory():
    """Check if temp directory exists and list any files."""
    temp_dir = "temp"
    
    print("🔍 Checking temp directory...")
    
    if not os.path.exists(temp_dir):
        print(f"❌ Temp directory '{temp_dir}' does not exist")
        return False
    
    files = os.listdir(temp_dir)
    
    if not files:
        print(f"📁 Temp directory '{temp_dir}' exists but is empty")
        return True
    
    print(f"📁 Temp directory '{temp_dir}' contains {len(files)} files:")
    
    for file in files:
        filepath = os.path.join(temp_dir, file)
        if os.path.isfile(filepath):
            file_size = os.path.getsize(filepath)
            mod_time = datetime.fromtimestamp(os.path.getmtime(filepath))
            print(f"  📄 {file} ({file_size} bytes, modified: {mod_time})")
            
            # If it's a JSON file, show a preview
            if file.endswith('.json'):
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    print(f"     📧 From: {data.get('from', 'unknown')}")
                    print(f"     📧 Subject: {data.get('subject', 'unknown')}")
                    print(f"     📧 Processing Status: {data.get('processing_status', 'unknown')}")
                except Exception as e:
                    print(f"     ❌ Error reading JSON: {e}")
    
    return True

def monitor_temp_files(duration_seconds=30):
    """Monitor temp directory for new files."""
    temp_dir = "temp"
    
    print(f"👀 Monitoring temp directory for {duration_seconds} seconds...")
    
    # Get initial file list
    initial_files = set()
    if os.path.exists(temp_dir):
        initial_files = set(os.listdir(temp_dir))
    
    print(f"📊 Initial files: {len(initial_files)}")
    
    start_time = time.time()
    last_check_files = initial_files.copy()
    
    while time.time() - start_time < duration_seconds:
        time.sleep(2)  # Check every 2 seconds
        
        if os.path.exists(temp_dir):
            current_files = set(os.listdir(temp_dir))
            new_files = current_files - last_check_files
            
            if new_files:
                print(f"🆕 New files detected: {list(new_files)}")
                for new_file in new_files:
                    filepath = os.path.join(temp_dir, new_file)
                    if new_file.endswith('.json'):
                        try:
                            with open(filepath, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                            print(f"   📧 Email from: {data.get('from', 'unknown')}")
                            print(f"   📧 Subject: {data.get('subject', 'unknown')}")
                        except Exception as e:
                            print(f"   ❌ Error reading {new_file}: {e}")
                
                last_check_files = current_files
    
    # Final count
    final_files = set()
    if os.path.exists(temp_dir):
        final_files = set(os.listdir(temp_dir))
    
    total_new_files = len(final_files) - len(initial_files)
    print(f"📊 Monitoring complete. New files created: {total_new_files}")
    
    return total_new_files > 0

def main():
    """Main function to test temp file creation."""
    print("🚀 Testing Gmail temp file creation...")
    print("=" * 60)
    
    # Check current state
    check_temp_directory()
    
    print("\n" + "=" * 60)
    print("💡 To test temp file creation:")
    print("1. Run this script")
    print("2. In another terminal, run: python main.py")
    print("3. Watch for new temp files being created as emails are processed")
    print("=" * 60)
    
    # Ask user if they want to monitor
    try:
        response = input("\n🤔 Do you want to monitor for new temp files? (y/n): ").lower().strip()
        
        if response in ['y', 'yes']:
            duration = 60  # Monitor for 1 minute
            try:
                duration_input = input(f"⏱️  How many seconds to monitor? (default: {duration}): ").strip()
                if duration_input:
                    duration = int(duration_input)
            except ValueError:
                print(f"Using default duration: {duration} seconds")
            
            files_created = monitor_temp_files(duration)
            
            if files_created:
                print("✅ Temp files were created during monitoring!")
                check_temp_directory()  # Show final state
            else:
                print("ℹ️  No new temp files were created during monitoring.")
                print("   Make sure the main system is running and processing emails.")
        
        else:
            print("👍 Monitoring skipped. Run the main system to see temp files created.")
    
    except KeyboardInterrupt:
        print("\n⏹️  Monitoring interrupted by user.")
    
    print("\n🎯 Temp file testing complete!")

if __name__ == "__main__":
    main()
