#!/usr/bin/env python3
"""
Test script to verify that datetime serialization in database operations works correctly.
This script tests that Pydantic models with datetime fields can be properly serialized to JSON.
"""

import json
from datetime import datetime
from database.schemas import CustomerInteraction, EmailProcessingLog, SystemMetrics, APIUsageLog

def test_customer_interaction_serialization():
    """Test CustomerInteraction model serialization."""
    print("🧪 Testing CustomerInteraction serialization...")
    
    try:
        interaction = CustomerInteraction(
            customer_email="<EMAIL>",
            interaction_type="email",
            content="Test interaction content",
            metadata={"test": "data"},
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        # Test model_dump with mode='json'
        data = interaction.model_dump(exclude={"id"}, exclude_none=True, mode='json')
        
        # Verify it can be JSON serialized
        json_str = json.dumps(data)
        
        # Verify datetime fields are strings
        assert isinstance(data['created_at'], str), "created_at should be a string"
        assert isinstance(data['updated_at'], str), "updated_at should be a string"
        
        print("✅ CustomerInteraction serialization successful")
        return True
        
    except Exception as e:
        print(f"❌ CustomerInteraction serialization failed: {e}")
        return False

def test_email_processing_log_serialization():
    """Test EmailProcessingLog model serialization."""
    print("🧪 Testing EmailProcessingLog serialization...")
    
    try:
        log_entry = EmailProcessingLog(
            message_id="test_msg_123",
            customer_email="<EMAIL>",
            subject="Test Subject",
            processed=True,
            processing_status="completed",
            processed_at=datetime.utcnow(),
            created_at=datetime.utcnow()
        )
        
        # Test model_dump with mode='json'
        data = log_entry.model_dump(exclude={"id"}, exclude_none=True, mode='json')
        
        # Verify it can be JSON serialized
        json_str = json.dumps(data)
        
        # Verify datetime fields are strings
        assert isinstance(data['created_at'], str), "created_at should be a string"
        assert isinstance(data['processed_at'], str), "processed_at should be a string"
        
        print("✅ EmailProcessingLog serialization successful")
        return True
        
    except Exception as e:
        print(f"❌ EmailProcessingLog serialization failed: {e}")
        return False

def test_system_metrics_serialization():
    """Test SystemMetrics model serialization."""
    print("🧪 Testing SystemMetrics serialization...")
    
    try:
        metric = SystemMetrics(
            metric_name="test_metric",
            metric_value=42.5,
            metric_unit="units",
            tags={"environment": "test"},
            timestamp=datetime.utcnow()
        )
        
        # Test model_dump with mode='json'
        data = metric.model_dump(exclude={"id"}, exclude_none=True, mode='json')
        
        # Verify it can be JSON serialized
        json_str = json.dumps(data)
        
        # Verify datetime fields are strings
        assert isinstance(data['timestamp'], str), "timestamp should be a string"
        
        print("✅ SystemMetrics serialization successful")
        return True
        
    except Exception as e:
        print(f"❌ SystemMetrics serialization failed: {e}")
        return False

def test_api_usage_log_serialization():
    """Test APIUsageLog model serialization."""
    print("🧪 Testing APIUsageLog serialization...")
    
    try:
        usage_log = APIUsageLog(
            api_name="mistral",
            endpoint="/v1/chat/completions",
            method="POST",
            status_code=200,
            response_time_ms=150.5,
            created_at=datetime.utcnow()
        )
        
        # Test model_dump with mode='json'
        data = usage_log.model_dump(exclude={"id"}, exclude_none=True, mode='json')
        
        # Verify it can be JSON serialized
        json_str = json.dumps(data)
        
        # Verify datetime fields are strings
        assert isinstance(data['created_at'], str), "created_at should be a string"
        
        print("✅ APIUsageLog serialization successful")
        return True
        
    except Exception as e:
        print(f"❌ APIUsageLog serialization failed: {e}")
        return False

def test_old_serialization_method():
    """Test that the old method (without mode='json') fails."""
    print("🧪 Testing old serialization method (should fail)...")
    
    try:
        interaction = CustomerInteraction(
            customer_email="<EMAIL>",
            interaction_type="email",
            content="Test interaction content",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        # Test model_dump without mode='json' (old method)
        data = interaction.model_dump(exclude={"id"}, exclude_none=True)
        
        # Try to JSON serialize - this should fail
        json_str = json.dumps(data)
        
        print("❌ Old method unexpectedly succeeded - this should have failed!")
        return False
        
    except TypeError as e:
        if "Object of type datetime is not JSON serializable" in str(e):
            print("✅ Old method correctly failed with datetime serialization error")
            return True
        else:
            print(f"❌ Old method failed with unexpected error: {e}")
            return False
    except Exception as e:
        print(f"❌ Old method failed with unexpected error: {e}")
        return False

def main():
    """Run all tests and report results."""
    print("🚀 Testing datetime serialization fix...")
    print("=" * 60)
    
    results = []
    
    # Test each model
    results.append(test_customer_interaction_serialization())
    print()
    results.append(test_email_processing_log_serialization())
    print()
    results.append(test_system_metrics_serialization())
    print()
    results.append(test_api_usage_log_serialization())
    print()
    results.append(test_old_serialization_method())
    
    print()
    print("=" * 60)
    print("📊 Test Results:")
    
    if all(results):
        print("✅ All tests passed! Datetime serialization fix is working correctly.")
        print("🎉 The 'Object of type datetime is not JSON serializable' error has been resolved.")
        return 0
    else:
        print("❌ Some tests failed. The fix may not be complete.")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
