"""
Email Monitoring Agent for the multi-agent sales support system.
Monitors Gmail for customer emails and processes PDF attachments.
"""

import asyncio
import logging
import os
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Set
from pathlib import Path

from config.settings import get_settings
from tools.gmail_tools import get_gmail_manager, GmailError
from tools.pdf_tools import get_pdf_processor, process_pdf_attachment, is_order_pdf
from database.operations import get_database_operations
from database.schemas import CustomerInteraction, EmailProcessingLog

logger = logging.getLogger(__name__)


class EmailMonitoringError(Exception):
    """Custom exception for email monitoring errors."""
    pass


class EmailMonitoringAgent:
    """Agent responsible for monitoring and processing customer emails."""
    
    def __init__(self):
        """Initialize the email monitoring agent."""
        self.settings = get_settings()
        self.gmail_manager = None
        self.pdf_processor = get_pdf_processor()
        self.db_ops = get_database_operations()
        self.processed_messages: Set[str] = set()
        self.running = False
        
        # Create directories
        self.temp_dir = Path(self.settings.temp_dir)
        self.temp_dir.mkdir(parents=True, exist_ok=True)
    
    async def initialize(self) -> None:
        """Initialize the email monitoring agent."""
        try:
            self.gmail_manager = await get_gmail_manager()
            logger.info("Email monitoring agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize email monitoring agent: {e}")
            raise EmailMonitoringError(f"Initialization failed: {e}")
    
    async def start_monitoring(self) -> None:
        """Start the email monitoring loop."""
        if not self.gmail_manager:
            await self.initialize()
        
        self.running = True
        logger.info("Starting email monitoring")
        
        while self.running:
            try:
                await self._monitoring_cycle()
                await asyncio.sleep(self.settings.email_check_interval)
                
            except asyncio.CancelledError:
                logger.info("Email monitoring cancelled")
                break
            except Exception as e:
                logger.error(f"Error in email monitoring cycle: {e}")
                await asyncio.sleep(60)  # Wait before retrying
    
    async def stop_monitoring(self) -> None:
        """Stop the email monitoring loop."""
        self.running = False
        logger.info("Email monitoring stopped")
    
    async def _monitoring_cycle(self) -> None:
        """Perform one cycle of email monitoring."""
        try:
            # Get unread emails
            unread_messages = await self.gmail_manager.get_messages(
                query="is:unread",
                max_results=self.settings.email_batch_size
            )
            
            if not unread_messages:
                logger.debug("No unread emails found")
                return
            
            logger.info(f"Found {len(unread_messages)} unread emails to process")
            
            # Process each message
            for message in unread_messages:
                message_id = message['id']
                
                # Skip if already processed
                if message_id in self.processed_messages:
                    continue
                
                try:
                    await self._process_email(message_id)
                    self.processed_messages.add(message_id)
                    
                except Exception as e:
                    logger.error(f"Error processing email {message_id}: {e}")
                    # Log the error but continue with other emails
                    await self._log_processing_error(message_id, str(e))
            
            # Cleanup old processed message IDs
            if len(self.processed_messages) > 1000:
                # Keep only the most recent 500
                self.processed_messages = set(list(self.processed_messages)[-500:])
            
        except Exception as e:
            logger.error(f"Error in monitoring cycle: {e}")
            raise
    
    async def _process_email(self, message_id: str) -> None:
        """Process a single email message."""
        try:
            # Get email details
            email_details = await self.gmail_manager.get_message_details(message_id)
            
            customer_email = email_details['from']
            subject = email_details['subject']
            body = email_details['body']
            attachments = email_details['attachments']
            
            logger.info(f"Processing email from {customer_email}: {subject}")
            
            # Initialize processing log
            processing_log = EmailProcessingLog(
                message_id=message_id,
                customer_email=customer_email,
                subject=subject,
                attachments_count=len(attachments),
                processing_status="processing"
            )
            
            # Extract query from email content
            extracted_query = await self._extract_customer_query(subject, body)
            processing_log.extracted_query = extracted_query
            
            # Process attachments
            order_info = None
            extracted_skus = []
            
            if attachments:
                order_info, extracted_skus = await self._process_attachments(
                    message_id, attachments
                )
                
                if extracted_skus:
                    processing_log.extracted_sku = ", ".join(extracted_skus)
            
            # Store customer interaction
            await self._store_customer_interaction(
                customer_email=customer_email,
                content=f"Subject: {subject}\n\nBody: {body}",
                extracted_query=extracted_query,
                order_info=order_info,
                extracted_skus=extracted_skus
            )
            
            # Mark email as processed
            processing_log.processed = True
            processing_log.processing_status = "completed"
            processing_log.processed_at = datetime.utcnow()
            
            # Log processing
            await self.db_ops.log_email_processing(processing_log)
            
            # Mark email as read and add label
            await self.gmail_manager.mark_as_read(message_id)
            await self.gmail_manager.add_label(message_id, "Processed")
            
            logger.info(f"Successfully processed email {message_id}")
            
        except Exception as e:
            logger.error(f"Error processing email {message_id}: {e}")
            await self._log_processing_error(message_id, str(e))
            raise
    
    async def _extract_customer_query(self, subject: str, body: str) -> str:
        """Extract customer query from email content."""
        try:
            # Combine subject and body
            full_content = f"{subject}\n\n{body}"
            
            # Clean up the content
            lines = full_content.split('\n')
            cleaned_lines = []
            
            for line in lines:
                line = line.strip()
                # Skip empty lines and common email signatures
                if line and not any(skip in line.lower() for skip in [
                    'sent from', 'regards', 'best regards', 'thank you',
                    'thanks', 'sincerely', '---', 'confidential'
                ]):
                    cleaned_lines.append(line)
            
            # Take the first few meaningful lines as the query
            query_lines = cleaned_lines[:5]  # First 5 lines
            query = ' '.join(query_lines)
            
            # Limit query length
            if len(query) > 500:
                query = query[:500] + "..."
            
            logger.debug(f"Extracted query: {query}")
            return query
            
        except Exception as e:
            logger.error(f"Error extracting customer query: {e}")
            return f"{subject} {body}"[:500]
    
    async def _process_attachments(
        self,
        message_id: str,
        attachments: List[Dict[str, Any]]
    ) -> tuple[Optional[Dict[str, Any]], List[str]]:
        """Process email attachments."""
        order_info = None
        extracted_skus = []
        
        try:
            # Limit number of attachments to process
            attachments_to_process = attachments[:self.settings.email_max_attachments]
            
            for attachment in attachments_to_process:
                filename = attachment['filename']
                attachment_id = attachment['attachment_id']
                mime_type = attachment['mime_type']
                size = attachment['size']
                
                # Skip if attachment is too large
                if size > self.settings.email_attachment_max_size:
                    logger.warning(f"Skipping large attachment {filename}: {size} bytes")
                    continue
                
                # Process PDF attachments
                if mime_type == 'application/pdf' or filename.lower().endswith('.pdf'):
                    try:
                        pdf_data = await self.gmail_manager.download_attachment(
                            message_id, attachment_id, filename
                        )
                        
                        # Process PDF
                        pdf_result = await process_pdf_attachment(pdf_data, filename)
                        
                        if pdf_result['processing_status'] == 'success':
                            # Check if it's an order document
                            if await is_order_pdf(pdf_result['plain_text']):
                                order_info = pdf_result['structured_data']
                                
                                # Extract SKUs from the order
                                if 'extracted_info' in order_info:
                                    skus = order_info['extracted_info'].get('skus', [])
                                    extracted_skus.extend(skus)
                                
                                logger.info(f"Processed order PDF {filename}")
                            else:
                                logger.debug(f"PDF {filename} does not appear to be an order document")
                        
                    except Exception as e:
                        logger.error(f"Error processing PDF attachment {filename}: {e}")
                
                else:
                    logger.debug(f"Skipping non-PDF attachment {filename}")
            
            return order_info, list(set(extracted_skus))  # Remove duplicates
            
        except Exception as e:
            logger.error(f"Error processing attachments: {e}")
            return None, []
    
    async def _store_customer_interaction(
        self,
        customer_email: str,
        content: str,
        extracted_query: str,
        order_info: Optional[Dict[str, Any]] = None,
        extracted_skus: Optional[List[str]] = None
    ) -> None:
        """Store customer interaction in database."""
        try:
            # Prepare metadata
            metadata = {
                "extracted_query": extracted_query,
                "processing_timestamp": datetime.utcnow().isoformat(),
                "agent": "email_monitor"
            }
            
            if order_info:
                metadata["order_info"] = order_info
            
            if extracted_skus:
                metadata["extracted_skus"] = extracted_skus
            
            # Create interaction record
            interaction = CustomerInteraction(
                customer_email=customer_email,
                interaction_type="email",
                content=content,
                metadata=metadata
            )
            
            # Store in database
            await self.db_ops.create_interaction(interaction)
            
            logger.debug(f"Stored customer interaction for {customer_email}")
            
        except Exception as e:
            logger.error(f"Error storing customer interaction: {e}")
    
    async def _log_processing_error(self, message_id: str, error_message: str) -> None:
        """Log email processing error."""
        try:
            # Try to get basic email info for logging
            try:
                email_details = await self.gmail_manager.get_message_details(message_id)
                customer_email = email_details['from']
                subject = email_details['subject']
            except:
                customer_email = "unknown"
                subject = "unknown"
            
            processing_log = EmailProcessingLog(
                message_id=message_id,
                customer_email=customer_email,
                subject=subject,
                processed=False,
                processing_status="error",
                error_message=error_message
            )
            
            await self.db_ops.log_email_processing(processing_log)
            
        except Exception as e:
            logger.error(f"Error logging processing error: {e}")
    
    async def get_processing_stats(self) -> Dict[str, Any]:
        """Get email processing statistics."""
        try:
            # Get recent processing logs
            logs = await self.db_ops.get_unprocessed_emails(limit=100)
            
            total_processed = len([log for log in logs if log.get('processed', False)])
            total_errors = len([log for log in logs if log.get('processing_status') == 'error'])
            
            return {
                "total_emails_processed": total_processed,
                "total_errors": total_errors,
                "success_rate": (total_processed / len(logs)) * 100 if logs else 0,
                "currently_running": self.running,
                "processed_messages_cache_size": len(self.processed_messages)
            }
            
        except Exception as e:
            logger.error(f"Error getting processing stats: {e}")
            return {
                "error": str(e),
                "currently_running": self.running
            }
    
    async def process_specific_email(self, message_id: str) -> Dict[str, Any]:
        """Process a specific email by message ID."""
        try:
            if not self.gmail_manager:
                await self.initialize()
            
            await self._process_email(message_id)
            return {"success": True, "message": f"Email {message_id} processed successfully"}
            
        except Exception as e:
            logger.error(f"Error processing specific email {message_id}: {e}")
            return {"success": False, "error": str(e)}


# Global email monitoring agent instance
_email_agent: Optional[EmailMonitoringAgent] = None


async def get_email_monitoring_agent() -> EmailMonitoringAgent:
    """Get the global email monitoring agent instance."""
    global _email_agent
    
    if _email_agent is None:
        _email_agent = EmailMonitoringAgent()
        await _email_agent.initialize()
    
    return _email_agent


async def start_email_monitoring() -> None:
    """Start email monitoring."""
    agent = await get_email_monitoring_agent()
    await agent.start_monitoring()


async def stop_email_monitoring() -> None:
    """Stop email monitoring."""
    global _email_agent
    
    if _email_agent:
        await _email_agent.stop_monitoring()


async def process_email(message_id: str) -> Dict[str, Any]:
    """Process a specific email."""
    agent = await get_email_monitoring_agent()
    return await agent.process_specific_email(message_id)


async def get_email_stats() -> Dict[str, Any]:
    """Get email processing statistics."""
    agent = await get_email_monitoring_agent()
    return await agent.get_processing_stats()
