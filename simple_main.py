#!/usr/bin/env python3
"""
Simplified AI Sales Support System
Pure AI framework using:
- Mistral AI Agents API
- Gmail API for email processing
- MYOB EXO API for ERP integration
- Supabase with pgvector for memory
- Graphiti for temporal knowledge
"""

import asyncio
import logging
import os
import sys
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

# Add FastAPI for web interface
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
import uvicorn

# Configure logging with UTF-8 encoding and colors
import io
from rich.console import Console
from rich.logging import RichHandler
from rich.theme import Theme
from rich.highlighter import RegexHighlighter

# Custom theme for module-based colors
custom_theme = Theme({
    "email": "bright_blue",
    "support": "bright_green",
    "myob": "bright_yellow",
    "main": "bright_magenta",
    "supabase": "bright_cyan",
    "mistral": "bright_red",
    "gmail": "blue",
    "graphiti": "green",
    "error": "bold red",
    "warning": "bold yellow",
    "success": "bold green",
    "info": "white"
})

console = Console(theme=custom_theme)

class ModuleColorHighlighter(RegexHighlighter):
    """Custom highlighter for module-based coloring."""

    base_style = ""
    highlights = [
        r"(?P<email>agents\.simple_email_agent|EMAIL|📧)",
        r"(?P<support>agents\.simple_support_agent|SUPPORT|🤖)",
        r"(?P<myob>agents\.simple_myob_agent|MYOB|🏢)",
        r"(?P<main>__main__|MAIN|INIT|START|🚀)",
        r"(?P<supabase>supabase|SUPABASE|💾)",
        r"(?P<mistral>MISTRAL|🧠)",
        r"(?P<gmail>gmail|GMAIL|📬)",
        r"(?P<graphiti>graphiti|GRAPHITI|🕸️)",
        r"(?P<error>ERROR|FAILED|❌|Exception)",
        r"(?P<warning>WARNING|WARN|⚠️)",
        r"(?P<success>SUCCESS|✅)",
        r"(?P<info>INFO|🔍)"
    ]

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        RichHandler(
            console=console,
            rich_tracebacks=True,
            highlighter=ModuleColorHighlighter(),
            show_path=False,
            markup=True
        ),
        logging.FileHandler('logs/sales_support.log', mode='a', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)


class SimplifiedSalesSystem:
    """Simplified AI Sales Support System."""

    def __init__(self):
        """Initialize the system configuration only."""
        self.running = False
        self.shutdown_called = False
        self.agents = {}
        self.supabase = None
        self.graphiti = None

        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()

        # Store configuration
        self.mistral_api_key = os.getenv("MISTRAL_API_KEY")
        self.supabase_url = os.getenv("SUPABASE_URL")
        self.supabase_key = os.getenv("SUPABASE_SERVICE_KEY")
        self.myob_api_url = os.getenv("MYOB_EXO_API_URL")
        self.myob_api_key = os.getenv("MYOB_EXO_API_KEY")

        # Email parameters (will be configured on startup)
        self.email_params = self._get_default_email_params()

        # Initialize FastAPI app
        self.app = FastAPI(
            title="Simplified AI Sales Support System",
            description="AI-powered sales support with Gmail, MYOB EXO, and temporal knowledge",
            version="1.0.0"
        )
        self._setup_api_routes()

        logger.info("[INIT] System configuration loaded")

    def _get_default_email_params(self):
        """Get default email parameters."""
        return {
            'query': 'is:unread',
            'max_results': 10,
            'check_interval': 30,
            'include_spam_trash': False,
            'label_ids': None,
            'date_filter': None,
            'sender_filter': None,
            'subject_filter': None,
            'auto_mark_read': True,
            'process_attachments': True
        }

    def configure_email_params(self):
        """Interactive configuration of email parameters."""
        from rich.console import Console
        from rich.prompt import Prompt, Confirm
        from rich.panel import Panel
        from rich.table import Table
        from rich.columns import Columns

        console = Console()

        # Show current configuration
        console.print("\n🔧 [bold blue]EMAIL PARAMETER CONFIGURATION[/bold blue]\n")

        current_table = Table(title="📋 Current Email Parameters", show_header=True)
        current_table.add_column("Parameter", style="cyan", width=20)
        current_table.add_column("Current Value", style="green", width=30)
        current_table.add_column("Description", style="white", width=50)

        current_table.add_row("Gmail Query", self.email_params['query'], "Gmail search query filter")
        current_table.add_row("Max Results", str(self.email_params['max_results']), "Maximum emails per check")
        current_table.add_row("Check Interval", f"{self.email_params['check_interval']}s", "Seconds between email checks")
        current_table.add_row("Include Spam/Trash", str(self.email_params['include_spam_trash']), "Include spam and trash folders")
        current_table.add_row("Auto Mark Read", str(self.email_params['auto_mark_read']), "Automatically mark emails as read")
        current_table.add_row("Process Attachments", str(self.email_params['process_attachments']), "Process PDF attachments")

        console.print(current_table)

        # Ask if user wants to configure
        if not Confirm.ask("\n🤔 Would you like to customize email parameters?", default=False):
            console.print("✅ [green]Using default email parameters[/green]")
            return

        console.print("\n📝 [yellow]Configure Email Parameters:[/yellow]\n")

        # Gmail Query Configuration
        query_options = [
            ("is:unread", "Only unread emails (recommended)"),
            ("is:unread newer_than:1d", "Unread emails from last 24 hours"),
            ("is:unread newer_than:1h", "Unread emails from last hour"),
            ("in:inbox", "All inbox emails (read and unread)"),
            ("custom", "Enter custom Gmail query")
        ]

        console.print("📬 [cyan]Gmail Query Options:[/cyan]")
        for i, (query, desc) in enumerate(query_options, 1):
            console.print(f"  {i}. {query} - {desc}")

        query_choice = Prompt.ask("Select query option", choices=[str(i) for i in range(1, len(query_options)+1)], default="1")

        if query_choice == str(len(query_options)):  # Custom option
            self.email_params['query'] = Prompt.ask("Enter custom Gmail query", default=self.email_params['query'])
        else:
            self.email_params['query'] = query_options[int(query_choice)-1][0]

        # Max Results
        self.email_params['max_results'] = int(Prompt.ask(
            "📊 Maximum emails per check",
            default=str(self.email_params['max_results'])
        ))

        # Check Interval
        self.email_params['check_interval'] = int(Prompt.ask(
            "⏰ Check interval (seconds)",
            default=str(self.email_params['check_interval'])
        ))

        # Additional options
        self.email_params['include_spam_trash'] = Confirm.ask(
            "🗑️ Include spam and trash folders?",
            default=self.email_params['include_spam_trash']
        )

        self.email_params['auto_mark_read'] = Confirm.ask(
            "✅ Automatically mark emails as read after processing?",
            default=self.email_params['auto_mark_read']
        )

        self.email_params['process_attachments'] = Confirm.ask(
            "📎 Process PDF attachments?",
            default=self.email_params['process_attachments']
        )

        # Optional filters
        if Confirm.ask("🔍 Add sender filter?", default=False):
            self.email_params['sender_filter'] = Prompt.ask("Enter sender email or domain")

        if Confirm.ask("📝 Add subject filter?", default=False):
            self.email_params['subject_filter'] = Prompt.ask("Enter subject keywords")

        # Show final configuration
        console.print("\n✅ [bold green]Email Parameters Configured![/bold green]\n")

        final_table = Table(title="🎯 Final Email Configuration", show_header=True)
        final_table.add_column("Parameter", style="cyan", width=20)
        final_table.add_column("Value", style="green", width=50)

        final_table.add_row("Gmail Query", self.email_params['query'])
        final_table.add_row("Max Results", str(self.email_params['max_results']))
        final_table.add_row("Check Interval", f"{self.email_params['check_interval']} seconds")
        final_table.add_row("Include Spam/Trash", str(self.email_params['include_spam_trash']))
        final_table.add_row("Auto Mark Read", str(self.email_params['auto_mark_read']))
        final_table.add_row("Process Attachments", str(self.email_params['process_attachments']))

        if self.email_params['sender_filter']:
            final_table.add_row("Sender Filter", self.email_params['sender_filter'])
        if self.email_params['subject_filter']:
            final_table.add_row("Subject Filter", self.email_params['subject_filter'])

        console.print(final_table)

    def _setup_api_routes(self):
        """Setup FastAPI routes."""

        @self.app.get("/")
        async def root():
            return {"message": "Simplified AI Sales Support System", "status": "running"}

        @self.app.get("/health")
        async def health_check():
            health = await self._check_health()
            return JSONResponse(content=health)

        @self.app.post("/query")
        async def process_query(request: dict):
            customer_email = request.get("customer_email")
            query = request.get("query")

            if not customer_email or not query:
                raise HTTPException(status_code=400, detail="customer_email and query are required")

            response = await self.process_customer_query(customer_email, query)
            return JSONResponse(content=response)

        @self.app.get("/customers/{customer_email}/history")
        async def get_customer_history(customer_email: str):
            try:
                interactions = self.supabase.table("customer_interactions").select("*").eq(
                    "customer_email", customer_email
                ).order("created_at", desc=True).limit(20).execute()

                return JSONResponse(content={"interactions": interactions.data or []})
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/stats")
        async def get_system_stats():
            try:
                stats = {}
                for agent_name, agent in self.agents.items():
                    if hasattr(agent, 'get_agent_stats'):
                        stats[agent_name] = await agent.get_agent_stats()

                return JSONResponse(content={"agent_stats": stats})
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/emails/recent")
        async def get_recent_emails():
            """Get recent processed emails with analysis."""
            try:
                interactions = self.supabase.table("customer_interactions").select("*").eq(
                    "interaction_type", "email"
                ).order("created_at", desc=True).limit(10).execute()

                return JSONResponse(content={"recent_emails": interactions.data or []})
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/emails/analysis/{email_id}")
        async def get_email_analysis(email_id: str):
            """Get detailed analysis for a specific email."""
            try:
                interaction = self.supabase.table("customer_interactions").select("*").eq(
                    "metadata->>email_id", email_id
                ).execute()

                if not interaction.data:
                    raise HTTPException(status_code=404, detail="Email not found")

                return JSONResponse(content={"email_analysis": interaction.data[0]})
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

    async def initialize_components(self):
        """Initialize all system components."""
        try:
            logger.info("[START] Initializing system components...")

            # Initialize core components
            await self._initialize_supabase()
            await self._initialize_graphiti()
            await self._initialize_agents()

            logger.info("[SUCCESS] All components initialized successfully!")

        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize components: {e}")
            raise

    async def start_monitoring(self):
        """Start the background monitoring loops."""
        try:
            # Set running flag and start monitoring (individual loops will log their startup)
            self.running = True
            await self._start_monitoring()

        except Exception as e:
            logger.error(f"[ERROR] Failed to start monitoring: {e}")
            raise
    
    async def stop(self):
        """Stop the system."""
        if self.shutdown_called:
            return
        self.shutdown_called = True

        logger.info("[STOP] Stopping system...")
        self.running = False
    
    async def _initialize_supabase(self):
        """Initialize Supabase connection."""
        try:
            from supabase import create_client
            
            self.supabase = create_client(self.supabase_url, self.supabase_key)
            
            # Test connection
            response = self.supabase.table("customer_interactions").select("count").execute()
            logger.info("✅ Supabase connected successfully")
            
        except Exception as e:
            logger.error(f"❌ Supabase initialization failed: {e}")
            raise
    
    async def _initialize_graphiti(self):
        """Initialize Graphiti for temporal knowledge (disabled for now)."""
        # Graphiti is optional and not currently needed
        # Uncomment and install 'graphiti' package if temporal knowledge is required
        logger.info("[INFO] Graphiti initialization skipped (optional feature)")
        self.graphiti = None
    
    async def _initialize_agents(self):
        """Initialize AI agents."""
        try:
            # Email Processing Agent
            self.agents['email'] = EmailAgent(
                mistral_api_key=self.mistral_api_key,
                supabase=self.supabase,
                graphiti=self.graphiti
            )
            
            # Customer Support Agent
            self.agents['support'] = SupportAgent(
                mistral_api_key=self.mistral_api_key,
                supabase=self.supabase,
                graphiti=self.graphiti
            )
            
            # MYOB Integration Agent
            self.agents['myob'] = MyobAgent(
                mistral_api_key=self.mistral_api_key,
                myob_api_url=self.myob_api_url,
                myob_api_key=self.myob_api_key,
                supabase=self.supabase
            )
            
            logger.info("✅ All agents initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Agent initialization failed: {e}")
            raise
    
    async def _start_monitoring(self):
        """Start monitoring loops."""
        try:
            # Start email monitoring
            email_task = asyncio.create_task(self._email_monitoring_loop())
            
            # Start health monitoring
            health_task = asyncio.create_task(self._health_monitoring_loop())
            
            # Wait for tasks
            await asyncio.gather(email_task, health_task)
            
        except Exception as e:
            logger.error(f"❌ Monitoring failed: {e}")
    
    async def _email_monitoring_loop(self):
        """Monitor emails and process them."""
        logger.info(f"[EMAIL] Starting email monitoring (checking every {self.email_params['check_interval']}s)...")

        while self.running:
            try:
                # Check for new emails with configured parameters
                await self.agents['email'].check_emails(
                    query=self.email_params['query'],
                    max_results=self.email_params['max_results'],
                    include_spam_trash=self.email_params['include_spam_trash'],
                    sender_filter=self.email_params.get('sender_filter'),
                    subject_filter=self.email_params.get('subject_filter'),
                    auto_mark_read=self.email_params['auto_mark_read'],
                    process_attachments=self.email_params['process_attachments']
                )

                # Wait before next check using configured interval
                await asyncio.sleep(self.email_params['check_interval'])

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[ERROR] Email monitoring error: {e}")
                await asyncio.sleep(60)  # Wait before retry
    
    async def _health_monitoring_loop(self):
        """Monitor system health."""
        logger.info("[HEALTH] Starting health monitoring...")

        while self.running:
            try:
                # Check system health
                health_status = await self._check_health()
                logger.debug(f"Health status: {health_status}")

                # Wait before next check
                await asyncio.sleep(60)  # 1 minute

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[ERROR] Health monitoring error: {e}")
                await asyncio.sleep(60)
    
    async def _check_health(self) -> Dict[str, Any]:
        """Check system health."""
        health = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "status": "healthy",
            "components": {}
        }
        
        # Check Supabase
        try:
            self.supabase.table("customer_interactions").select("count").limit(1).execute()
            health["components"]["supabase"] = "healthy"
        except Exception as e:
            health["components"]["supabase"] = f"unhealthy: {e}"
            health["status"] = "degraded"
        
        # Check Graphiti
        if self.graphiti:
            try:
                # Simple health check for Graphiti
                health["components"]["graphiti"] = "healthy"
            except Exception as e:
                health["components"]["graphiti"] = f"unhealthy: {e}"
        else:
            health["components"]["graphiti"] = "disabled"
        
        return health
    
    async def process_customer_query(self, customer_email: str, query: str) -> Dict[str, Any]:
        """Process a customer query."""
        try:
            # Use support agent to process the query
            response = await self.agents['support'].process_query(customer_email, query)
            
            # Store interaction in Supabase
            await self._store_interaction(customer_email, query, response)
            
            # Update temporal knowledge in Graphiti
            if self.graphiti:
                await self._update_temporal_knowledge(customer_email, query, response)
            
            return response
            
        except Exception as e:
            logger.error(f"❌ Error processing customer query: {e}")
            return {
                "error": str(e),
                "response": "I apologize, but I'm experiencing technical difficulties. Please try again later."
            }
    
    async def _store_interaction(self, customer_email: str, query: str, response: Dict[str, Any]):
        """Store interaction in Supabase."""
        try:
            interaction_data = {
                "customer_email": customer_email,
                "interaction_type": "support_query",
                "content": query,
                "response": response.get("response", ""),
                "metadata": response,
                "created_at": datetime.now(timezone.utc).isoformat()
            }
            
            self.supabase.table("customer_interactions").insert(interaction_data).execute()
            logger.debug(f"Stored interaction for {customer_email}")
            
        except Exception as e:
            logger.error(f"❌ Failed to store interaction: {e}")
    
    async def _update_temporal_knowledge(self, customer_email: str, query: str, response: Dict[str, Any]):
        """Update temporal knowledge in Graphiti."""
        try:
            if self.graphiti:
                # Add episodic memory
                await self.graphiti.add_episodic_memories([
                    f"Customer {customer_email} asked: {query}",
                    f"System responded: {response.get('response', '')}"
                ])
                
                logger.debug(f"Updated temporal knowledge for {customer_email}")
                
        except Exception as e:
            logger.error(f"❌ Failed to update temporal knowledge: {e}")


# Import agent classes (will be created next)
from agents.simple_email_agent import EmailAgent
from agents.simple_support_agent import SupportAgent
from agents.simple_myob_agent import MyobAgent


def initialize_system():
    """Initialize the system components."""
    logger.info("[INIT] Initializing Simplified Sales Support System...")

    # Create system instance
    system = SimplifiedSalesSystem()

    # Run async initialization
    async def _async_init():
        await system.initialize_components()
        return system

    return asyncio.run(_async_init())


def start_background_monitoring(system):
    """Start background monitoring in a separate thread."""
    def _run_monitoring():
        asyncio.run(system.start_monitoring())

    import threading
    monitor_thread = threading.Thread(target=_run_monitoring)
    monitor_thread.daemon = True
    monitor_thread.start()
    logger.info("[EMAIL] Background email monitoring started")


def start_web_api(system):
    """Start the web API on port 8080."""
    logger.info("[WEB] Starting web API on port 8080...")
    uvicorn.run(
        system.app,
        host="0.0.0.0",
        port=8080,
        log_level="info",
        access_log=True
    )


def show_startup_banner(system):
    """Show startup banner with email parameters."""
    from rich.console import Console
    from rich.panel import Panel
    from rich.table import Table

    console = Console()

    # Create email parameters table
    params_table = Table(title="📧 Active Email Parameters", show_header=True)
    params_table.add_column("Parameter", style="cyan", width=20)
    params_table.add_column("Value", style="green", width=40)

    params_table.add_row("Gmail Query", system.email_params['query'])
    params_table.add_row("Max Results", str(system.email_params['max_results']))
    params_table.add_row("Check Interval", f"{system.email_params['check_interval']} seconds")
    params_table.add_row("Include Spam/Trash", str(system.email_params['include_spam_trash']))
    params_table.add_row("Auto Mark Read", str(system.email_params['auto_mark_read']))
    params_table.add_row("Process Attachments", str(system.email_params['process_attachments']))

    if system.email_params.get('sender_filter'):
        params_table.add_row("Sender Filter", system.email_params['sender_filter'])
    if system.email_params.get('subject_filter'):
        params_table.add_row("Subject Filter", system.email_params['subject_filter'])

    console.print(params_table)

    # Show startup panel
    startup_panel = Panel(
        f"""[bold blue]🚀 SIMPLIFIED AI SALES SUPPORT SYSTEM 🚀[/bold blue]

[green]✅ System Initialized Successfully[/green]
[cyan]📧 Email monitoring configured and ready[/cyan]
[yellow]🧠 AI analysis with Mistral AI[/yellow]
[magenta]💾 Database: Supabase connected[/magenta]
[red]🕸️ Knowledge Graph: Graphiti ready[/red]

[bold white]🌐 Web API will start on: http://localhost:8080[/bold white]

[dim]Press Ctrl+C to stop the system[/dim]""",
        title="🎯 System Ready",
        border_style="green"
    )

    console.print(startup_panel)

def main():
    """Main function - entry point for the application."""
    try:
        # 1. Initialize the system
        system = initialize_system()

        # 2. Configure email parameters interactively
        system.configure_email_params()

        # 3. Show startup banner with configuration
        show_startup_banner(system)

        # 4. Start background monitoring with configured parameters
        start_background_monitoring(system)

        # 5. Start web API (this blocks)
        start_web_api(system)

    except KeyboardInterrupt:
        logger.info("[STOP] Received interrupt signal")
    except Exception as e:
        logger.error(f"[ERROR] System error: {e}")
        raise


if __name__ == "__main__":
    # Create logs directory
    os.makedirs("logs", exist_ok=True)

    # Run the system
    main()
