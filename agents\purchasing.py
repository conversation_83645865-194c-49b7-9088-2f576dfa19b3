"""
Purchasing Agent for the multi-agent sales support system.
Monitors inventory and handles automated restocking through MYOB EXO ERP integration.
"""

import asyncio
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Set
from enum import Enum

from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext
from pydantic_ai.models.mistral import MistralModel
from pydantic_ai.providers.mistral import MistralProvider
from pydantic_ai.settings import ModelSettings

from config.settings import get_settings
from database.operations import get_database_operations
from database.schemas import InventoryAlert, CustomerInteraction, AgentMemory
from tools.erp_tools import get_erp_client, ERPError

logger = logging.getLogger(__name__)


class PurchasingError(Exception):
    """Custom exception for purchasing operations."""
    pass


class AlertType(str, Enum):
    """Types of inventory alerts."""
    LOW_STOCK = "low_stock"
    OUT_OF_STOCK = "out_of_stock"
    REORDER_POINT = "reorder_point"
    CRITICAL_STOCK = "critical_stock"


class PurchaseRecommendation(BaseModel):
    """Model for purchase recommendations."""
    sku: str = Field(..., description="SKU to purchase")
    current_quantity: int = Field(..., description="Current stock quantity")
    recommended_quantity: int = Field(..., description="Recommended purchase quantity")
    supplier: Optional[str] = Field(default=None, description="Recommended supplier")
    estimated_cost: Optional[float] = Field(default=None, description="Estimated purchase cost")
    priority: str = Field(default="medium", description="Purchase priority")
    reason: str = Field(..., description="Reason for purchase recommendation")
    lead_time_days: Optional[int] = Field(default=None, description="Expected lead time")


class PurchaseOrderRequest(BaseModel):
    """Model for purchase order requests."""
    items: List[Dict[str, Any]] = Field(..., description="Items to purchase")
    supplier: Optional[str] = Field(default=None, description="Supplier for the order")
    priority: str = Field(default="normal", description="Order priority")
    notes: Optional[str] = Field(default=None, description="Additional notes")
    auto_approve: bool = Field(default=False, description="Whether to auto-approve the order")


class PurchaseOrderResponse(BaseModel):
    """Model for purchase order responses."""
    success: bool = Field(..., description="Whether the operation was successful")
    purchase_order_id: Optional[str] = Field(default=None, description="Purchase order ID")
    message: str = Field(..., description="Response message")
    total_amount: Optional[float] = Field(default=None, description="Total order amount")
    items_count: int = Field(default=0, description="Number of items in the order")
    estimated_delivery: Optional[str] = Field(default=None, description="Estimated delivery date")
    warnings: List[str] = Field(default_factory=list, description="Any warnings or issues")


class PurchasingAgent:
    """Agent responsible for inventory monitoring and automated purchasing."""
    
    def __init__(self):
        """Initialize the purchasing agent."""
        self.settings = get_settings()
        self.db_ops = get_database_operations()
        self.erp_client = None
        self.running = False
        self.monitored_skus: Set[str] = set()
        
        # Initialize Mistral model for intelligent purchasing decisions
        mistral_provider = MistralProvider(api_key=self.settings.mistral_api_key)
        model_settings = ModelSettings(
            temperature=0.2,  # Low temperature for consistent purchasing decisions
            max_tokens=self.settings.mistral_max_tokens
        )
        self.model = MistralModel(
            model_name=self.settings.mistral_model,
            provider=mistral_provider,
            settings=model_settings
        )
        
        # Initialize PydanticAI agent
        self.agent = Agent(
            model=self.model,
            result_type=PurchaseRecommendation,
            system_prompt=self._get_system_prompt()
        )
        
        # Setup agent tools
        self._setup_agent_tools()
    
    async def initialize(self) -> None:
        """Initialize the purchasing agent."""
        try:
            self.erp_client = await get_erp_client()
            logger.info("Purchasing agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize purchasing agent: {e}")
            raise PurchasingError(f"Initialization failed: {e}")
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for the purchasing agent."""
        return """
You are an expert purchasing agent for a sales support system. Your role is to:

1. Monitor inventory levels and identify restocking needs
2. Generate intelligent purchase recommendations based on:
   - Current stock levels
   - Historical demand patterns
   - Lead times and supplier information
   - Seasonal trends and forecasts
   - Business rules and constraints

3. Create purchase orders when appropriate
4. Optimize purchasing decisions for cost and availability
5. Handle urgent restocking situations

Guidelines for purchase recommendations:
- Consider minimum order quantities and economic order quantities
- Factor in lead times and delivery schedules
- Prioritize critical items and fast-moving inventory
- Balance carrying costs with stockout risks
- Apply business rules for approval thresholds
- Consider supplier relationships and pricing

Priority levels:
- Critical: Items that are out of stock or will be within 1-2 days
- High: Items below reorder point with short lead times
- Medium: Items approaching reorder point
- Low: Routine restocking for optimization

When making recommendations:
- Provide clear reasoning for purchase quantities
- Include cost estimates and supplier suggestions
- Highlight any urgent or critical situations
- Consider seasonal patterns and demand forecasts
- Apply appropriate safety stock levels

Remember: Balance cost efficiency with service level requirements.
"""
    
    def _setup_agent_tools(self) -> None:
        """Set up tools for the PydanticAI agent."""
        
        @self.agent.tool
        async def get_stock_history(ctx: RunContext[Dict[str, Any]], sku: str, days: int = 30) -> str:
            """Get stock movement history for a SKU."""
            try:
                # This would integrate with ERP to get historical data
                # For now, return a placeholder
                return f"Stock history for {sku} over {days} days: Average daily usage: 5 units, Peak usage: 15 units"
            except Exception as e:
                return f"Error getting stock history for {sku}: {e}"
        
        @self.agent.tool
        async def get_supplier_info(ctx: RunContext[Dict[str, Any]], sku: str) -> str:
            """Get supplier information for a SKU."""
            try:
                # This would integrate with ERP supplier data
                return f"Primary supplier for {sku}: ABC Supplies (Lead time: 7 days, MOQ: 100 units)"
            except Exception as e:
                return f"Error getting supplier info for {sku}: {e}"
        
        @self.agent.tool
        async def calculate_economic_order_quantity(ctx: RunContext[Dict[str, Any]], sku: str) -> str:
            """Calculate economic order quantity for a SKU."""
            try:
                # Simplified EOQ calculation
                # In reality, this would use actual demand, ordering costs, and holding costs
                return f"Economic Order Quantity for {sku}: 250 units (based on demand patterns and costs)"
            except Exception as e:
                return f"Error calculating EOQ for {sku}: {e}"
    
    async def start_monitoring(self) -> None:
        """Start the inventory monitoring loop."""
        if not self.erp_client:
            await self.initialize()
        
        self.running = True
        logger.info("Starting inventory monitoring")
        
        while self.running:
            try:
                await self._monitoring_cycle()
                await asyncio.sleep(self.settings.inventory_check_interval)
                
            except asyncio.CancelledError:
                logger.info("Inventory monitoring cancelled")
                break
            except Exception as e:
                logger.error(f"Error in inventory monitoring cycle: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes before retrying
    
    async def stop_monitoring(self) -> None:
        """Stop the inventory monitoring loop."""
        self.running = False
        logger.info("Inventory monitoring stopped")
    
    async def _monitoring_cycle(self) -> None:
        """Perform one cycle of inventory monitoring."""
        try:
            # Get all stock items that need monitoring
            stock_items = await self.erp_client.get_stock_items(limit=100)
            
            if not stock_items:
                logger.debug("No stock items found for monitoring")
                return
            
            logger.info(f"Monitoring {len(stock_items)} stock items")
            
            alerts_created = 0
            recommendations_generated = 0
            
            # Check each item
            for item in stock_items:
                sku = item.get('sku') or item.get('item_code')
                if not sku:
                    continue
                
                try:
                    # Get current stock levels
                    stock_info = await self.erp_client.get_stock_levels(sku)
                    current_quantity = stock_info.get('quantity_available', 0)
                    
                    # Check if alert is needed
                    alert_type = self._determine_alert_type(current_quantity, item)
                    
                    if alert_type:
                        # Create inventory alert
                        await self._create_inventory_alert(sku, current_quantity, alert_type)
                        alerts_created += 1
                        
                        # Generate purchase recommendation if enabled
                        if self.settings.enable_auto_purchasing:
                            recommendation = await self._generate_purchase_recommendation(sku, current_quantity, item)
                            
                            if recommendation:
                                await self._process_purchase_recommendation(recommendation)
                                recommendations_generated += 1
                
                except Exception as e:
                    logger.error(f"Error monitoring SKU {sku}: {e}")
            
            if alerts_created > 0 or recommendations_generated > 0:
                logger.info(f"Monitoring cycle complete: {alerts_created} alerts, {recommendations_generated} recommendations")
            
        except Exception as e:
            logger.error(f"Error in monitoring cycle: {e}")
            raise
    
    def _determine_alert_type(self, current_quantity: int, item: Dict[str, Any]) -> Optional[AlertType]:
        """Determine if an alert is needed based on stock levels."""
        # Get thresholds from item data or use defaults
        reorder_point = item.get('reorder_point', self.settings.inventory_low_threshold)
        critical_threshold = reorder_point * 0.5  # 50% of reorder point
        
        if current_quantity <= 0:
            return AlertType.OUT_OF_STOCK
        elif current_quantity <= critical_threshold:
            return AlertType.CRITICAL_STOCK
        elif current_quantity <= reorder_point:
            return AlertType.LOW_STOCK
        
        return None
    
    async def _create_inventory_alert(self, sku: str, current_quantity: int, alert_type: AlertType) -> None:
        """Create an inventory alert."""
        try:
            # Check if alert already exists
            existing_alerts = await self.db_ops.get_active_inventory_alerts()
            
            for alert in existing_alerts:
                if alert.get('internal_sku') == sku and alert.get('status') == 'active':
                    logger.debug(f"Alert already exists for SKU {sku}")
                    return
            
            # Create new alert
            alert = InventoryAlert(
                internal_sku=sku,
                current_quantity=current_quantity,
                threshold_quantity=self.settings.inventory_low_threshold,
                alert_type=alert_type.value,
                status="active"
            )
            
            await self.db_ops.create_inventory_alert(alert)
            logger.info(f"Created {alert_type.value} alert for SKU {sku} (quantity: {current_quantity})")
            
        except Exception as e:
            logger.error(f"Error creating inventory alert for {sku}: {e}")
    
    async def _generate_purchase_recommendation(
        self,
        sku: str,
        current_quantity: int,
        item: Dict[str, Any]
    ) -> Optional[PurchaseRecommendation]:
        """Generate a purchase recommendation for a SKU."""
        try:
            # Prepare context for AI agent
            context = {
                "sku": sku,
                "current_quantity": current_quantity,
                "item_info": item,
                "threshold": self.settings.inventory_low_threshold
            }
            
            # Create prompt for the AI agent
            prompt = f"""
            Analyze the following inventory situation and provide a purchase recommendation:
            
            SKU: {sku}
            Current Quantity: {current_quantity}
            Reorder Threshold: {self.settings.inventory_low_threshold}
            Item Information: {item}
            
            Please recommend:
            1. How many units to purchase
            2. Priority level (critical/high/medium/low)
            3. Reasoning for the recommendation
            4. Any special considerations
            """
            
            # Run the AI agent
            result = await self.agent.run(prompt, deps=context)
            
            logger.debug(f"Generated purchase recommendation for {sku}: {result.data.recommended_quantity} units")
            return result.data
            
        except Exception as e:
            logger.error(f"Error generating purchase recommendation for {sku}: {e}")
            return None
    
    async def _process_purchase_recommendation(self, recommendation: PurchaseRecommendation) -> None:
        """Process a purchase recommendation."""
        try:
            # Store the recommendation as agent memory
            await self._store_purchase_memory(recommendation)
            
            # If critical priority and auto-approval enabled, create purchase order
            if (recommendation.priority == "critical" and 
                self.settings.enable_auto_purchasing and
                recommendation.estimated_cost and
                recommendation.estimated_cost <= self.settings.order_auto_approval_limit):
                
                await self._create_auto_purchase_order(recommendation)
            
        except Exception as e:
            logger.error(f"Error processing purchase recommendation: {e}")
    
    async def _store_purchase_memory(self, recommendation: PurchaseRecommendation) -> None:
        """Store purchase recommendation as agent memory."""
        try:
            memory_data = {
                "recommendation": recommendation.model_dump(),
                "timestamp": datetime.utcnow().isoformat(),
                "agent": "purchasing"
            }
            
            memory = AgentMemory(
                agent_name="purchasing",
                context_type="purchase_recommendation",
                context_data=memory_data,
                expires_at=datetime.utcnow() + timedelta(days=30)
            )
            
            await self.db_ops.store_agent_memory(memory)
            
        except Exception as e:
            logger.error(f"Error storing purchase memory: {e}")
    
    async def _create_auto_purchase_order(self, recommendation: PurchaseRecommendation) -> None:
        """Create an automatic purchase order."""
        try:
            order_data = {
                "items": [{
                    "sku": recommendation.sku,
                    "quantity": recommendation.recommended_quantity,
                    "estimated_cost": recommendation.estimated_cost
                }],
                "supplier": recommendation.supplier,
                "priority": recommendation.priority,
                "notes": f"Auto-generated order: {recommendation.reason}",
                "created_by": "purchasing_agent"
            }
            
            # Create purchase order in ERP
            po_response = await self.erp_client.create_purchase_order(order_data)
            
            # Update inventory alert with PO information
            alerts = await self.db_ops.get_active_inventory_alerts()
            for alert in alerts:
                if alert.get('internal_sku') == recommendation.sku:
                    # Update alert with PO info (this would need to be implemented in db_ops)
                    logger.info(f"Updated alert for {recommendation.sku} with PO {po_response.get('order_id')}")
                    break
            
            logger.info(f"Created auto purchase order for {recommendation.sku}: {po_response.get('order_id')}")
            
        except Exception as e:
            logger.error(f"Error creating auto purchase order: {e}")
    
    async def create_purchase_order(self, request: PurchaseOrderRequest) -> PurchaseOrderResponse:
        """Create a purchase order manually."""
        try:
            if not self.erp_client:
                await self.initialize()
            
            # Prepare order data
            order_data = {
                "items": request.items,
                "supplier": request.supplier,
                "priority": request.priority,
                "notes": request.notes,
                "auto_approve": request.auto_approve,
                "created_by": "purchasing_agent"
            }
            
            # Calculate total amount
            total_amount = sum(
                item.get('quantity', 0) * item.get('unit_cost', 0)
                for item in request.items
            )
            
            # Create purchase order in ERP
            po_response = await self.erp_client.create_purchase_order(order_data)
            
            # Store interaction
            await self._store_purchase_interaction(request, po_response)
            
            return PurchaseOrderResponse(
                success=True,
                purchase_order_id=po_response.get('order_id'),
                message=f"Purchase order {po_response.get('order_id')} created successfully",
                total_amount=total_amount,
                items_count=len(request.items),
                estimated_delivery=self._calculate_delivery_estimate()
            )
            
        except Exception as e:
            logger.error(f"Error creating purchase order: {e}")
            return PurchaseOrderResponse(
                success=False,
                message=f"Failed to create purchase order: {e}",
                warnings=[str(e)]
            )
    
    async def _store_purchase_interaction(
        self,
        request: PurchaseOrderRequest,
        response: Dict[str, Any]
    ) -> None:
        """Store purchase interaction in database."""
        try:
            interaction_content = f"Purchase Order Created\nItems: {len(request.items)}\nSupplier: {request.supplier}\nPO ID: {response.get('order_id')}"
            
            metadata = {
                "agent": "purchasing",
                "action": "create_purchase_order",
                "purchase_order_id": response.get('order_id'),
                "items_count": len(request.items),
                "supplier": request.supplier,
                "priority": request.priority
            }
            
            interaction = CustomerInteraction(
                customer_email="system@purchasing",  # System-generated
                interaction_type="purchasing",
                content=interaction_content,
                metadata=metadata
            )
            
            await self.db_ops.create_interaction(interaction)
            
        except Exception as e:
            logger.error(f"Error storing purchase interaction: {e}")
    
    def _calculate_delivery_estimate(self) -> str:
        """Calculate estimated delivery date."""
        # Simple estimation - in reality this would consider supplier lead times
        estimated_date = datetime.utcnow() + timedelta(days=7)
        return estimated_date.strftime("%Y-%m-%d")
    
    async def get_purchase_recommendations(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent purchase recommendations."""
        try:
            memories = await self.db_ops.get_agent_memory(
                agent_name="purchasing",
                limit=limit
            )
            
            recommendations = []
            for memory in memories:
                if memory.get('context_type') == 'purchase_recommendation':
                    recommendations.append(memory.get('context_data', {}))
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error getting purchase recommendations: {e}")
            return []
    
    async def get_agent_stats(self) -> Dict[str, Any]:
        """Get purchasing agent statistics."""
        try:
            # Get recent alerts
            alerts = await self.db_ops.get_active_inventory_alerts()
            
            # Get recent purchase interactions
            recent_interactions = await self.db_ops.get_interactions(
                interaction_type="purchasing",
                limit=100
            )
            
            return {
                "active_alerts": len(alerts),
                "total_purchase_orders": len(recent_interactions),
                "monitoring_enabled": self.running,
                "auto_purchasing_enabled": self.settings.enable_auto_purchasing,
                "inventory_threshold": self.settings.inventory_low_threshold,
                "check_interval_seconds": self.settings.inventory_check_interval
            }
            
        except Exception as e:
            logger.error(f"Error getting agent stats: {e}")
            return {"error": str(e)}


# Global purchasing agent instance
_purchasing_agent: Optional[PurchasingAgent] = None


async def get_purchasing_agent() -> PurchasingAgent:
    """Get the global purchasing agent instance."""
    global _purchasing_agent
    
    if _purchasing_agent is None:
        _purchasing_agent = PurchasingAgent()
        await _purchasing_agent.initialize()
    
    return _purchasing_agent


async def start_inventory_monitoring() -> None:
    """Start inventory monitoring."""
    agent = await get_purchasing_agent()
    await agent.start_monitoring()


async def stop_inventory_monitoring() -> None:
    """Stop inventory monitoring."""
    global _purchasing_agent
    
    if _purchasing_agent:
        await _purchasing_agent.stop_monitoring()


async def create_purchase_order(
    items: List[Dict[str, Any]],
    supplier: Optional[str] = None,
    priority: str = "normal",
    notes: Optional[str] = None,
    auto_approve: bool = False
) -> PurchaseOrderResponse:
    """Create a purchase order."""
    agent = await get_purchasing_agent()
    
    request = PurchaseOrderRequest(
        items=items,
        supplier=supplier,
        priority=priority,
        notes=notes,
        auto_approve=auto_approve
    )
    
    return await agent.create_purchase_order(request)


async def get_purchasing_stats() -> Dict[str, Any]:
    """Get purchasing agent statistics."""
    agent = await get_purchasing_agent()
    return await agent.get_agent_stats()
