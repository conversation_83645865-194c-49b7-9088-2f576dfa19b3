"""
MYOB EXO API tools for the multi-agent sales support system.
Direct API integration without MCP server overhead.
"""

import asyncio
import base64
import logging
import time
from typing import Dict, Any, List, Optional
import httpx

from config.settings import get_settings
from database.operations import get_database_operations
from database.schemas import APIUsageLog

logger = logging.getLogger(__name__)


class MYOBError(Exception):
    """Custom exception for MYOB EXO API operations."""
    pass


class SKUMappingManager:
    """Manages customer SKU to internal SKU mappings."""
    
    def __init__(self):
        """Initialize SKU mapping manager."""
        self.db_ops = get_database_operations()
        self.cache = {}  # Simple in-memory cache
    
    async def get_internal_sku(self, customer_email: str, customer_sku: str) -> Optional[str]:
        """Get internal SKU for a customer's SKU."""
        try:
            cache_key = f"{customer_email}:{customer_sku}"
            
            # Check cache first
            if cache_key in self.cache:
                return self.cache[cache_key]
            
            # Query database
            mappings = await self.db_ops.get_sku_mappings(customer_email)
            
            for mapping in mappings:
                if mapping.get('customer_sku') == customer_sku and mapping.get('active', True):
                    internal_sku = mapping.get('internal_sku')
                    self.cache[cache_key] = internal_sku
                    return internal_sku
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting internal SKU: {e}")
            return None
    
    async def create_mapping(
        self,
        customer_email: str,
        customer_sku: str,
        internal_sku: str,
        description: Optional[str] = None
    ) -> bool:
        """Create a new SKU mapping."""
        try:
            from database.schemas import SKUMapping
            
            mapping = SKUMapping(
                customer_email=customer_email,
                customer_sku=customer_sku,
                internal_sku=internal_sku,
                description=description,
                active=True
            )
            
            await self.db_ops.create_sku_mapping(mapping)
            
            # Update cache
            cache_key = f"{customer_email}:{customer_sku}"
            self.cache[cache_key] = internal_sku
            
            logger.info(f"Created SKU mapping: {customer_sku} -> {internal_sku} for {customer_email}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating SKU mapping: {e}")
            return False
    
    async def get_customer_mappings(self, customer_email: str) -> List[Dict[str, Any]]:
        """Get all SKU mappings for a customer."""
        try:
            return await self.db_ops.get_sku_mappings(customer_email)
        except Exception as e:
            logger.error(f"Error getting customer mappings: {e}")
            return []


class MYOBClient:
    """Client for MYOB EXO API interactions."""
    
    def __init__(self):
        """Initialize MYOB client."""
        self.settings = get_settings()
        self.db_ops = get_database_operations()
        self.base_url = self.settings.myob_exo_api_url.rstrip('/')
        self.client: Optional[httpx.AsyncClient] = None
        self.sku_manager = SKUMappingManager()
    
    async def _get_client(self) -> httpx.AsyncClient:
        """Get or create HTTP client."""
        if self.client is None:
            self.client = httpx.AsyncClient(
                timeout=httpx.Timeout(30.0),
                limits=httpx.Limits(max_connections=20)
            )
        return self.client
    
    async def close(self) -> None:
        """Close HTTP client."""
        if self.client:
            await self.client.aclose()
            self.client = None
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        customer_email: Optional[str] = None
    ) -> Dict[str, Any]:
        """Make authenticated request to MYOB EXO API."""
        start_time = time.time()
        client = await self._get_client()
        
        # Prepare MYOB EXO API authentication headers
        auth_string = f"{self.settings.myob_exo_username}:{self.settings.myob_exo_password}"
        encoded_auth = base64.b64encode(auth_string.encode()).decode()

        headers = {
            "Authorization": f"Basic {encoded_auth}",
            "x-myobapi-key": self.settings.myob_exo_api_key,
            "x-myobapi-exotoken": self.settings.myob_exo_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == "GET":
                response = await client.get(url, headers=headers, params=params)
            elif method.upper() == "POST":
                response = await client.post(url, headers=headers, params=params, json=data)
            elif method.upper() == "PUT":
                response = await client.put(url, headers=headers, params=params, json=data)
            elif method.upper() == "PATCH":
                response = await client.patch(url, headers=headers, params=params, json=data)
            else:
                raise MYOBError(f"Unsupported HTTP method: {method}")
            
            response_time = (time.time() - start_time) * 1000
            
            # Log API usage
            await self._log_api_usage(
                endpoint=endpoint,
                method=method,
                status_code=response.status_code,
                response_time_ms=response_time,
                customer_email=customer_email
            )
            
            if response.status_code >= 400:
                error_text = response.text
                raise MYOBError(f"MYOB API request failed: {response.status_code} - {error_text}")
            
            return response.json()
            
        except httpx.RequestError as e:
            raise MYOBError(f"MYOB API request failed: {e}")
        except Exception as e:
            logger.error(f"Error making MYOB API request: {e}")
            raise MYOBError(f"MYOB API error: {e}")
    
    async def _log_api_usage(
        self,
        endpoint: str,
        method: str,
        status_code: int,
        response_time_ms: float,
        customer_email: Optional[str] = None
    ) -> None:
        """Log API usage for monitoring."""
        try:
            log_entry = APIUsageLog(
                api_name="MYOB_EXO",
                endpoint=endpoint,
                method=method,
                status_code=status_code,
                response_time_ms=response_time_ms,
                customer_email=customer_email,
                agent_name="myob_client"
            )
            
            await self.db_ops.log_api_usage(log_entry)
            
        except Exception as e:
            logger.warning(f"Failed to log API usage: {e}")
    
    # Core MYOB EXO API methods
    async def get_discovery(self) -> Dict[str, Any]:
        """Get available API endpoints."""
        return await self._make_request("GET", "/discovery")
    
    async def search_customers(self, query: str) -> List[Dict[str, Any]]:
        """Search for customers."""
        response = await self._make_request("GET", "/debtor/search", params={"q": query})
        return response.get("data", [])
    
    async def get_customer(self, customer_id: str) -> Dict[str, Any]:
        """Get customer details."""
        return await self._make_request("GET", f"/debtor/{customer_id}")
    
    async def search_products(self, query: str) -> List[Dict[str, Any]]:
        """Search for products."""
        response = await self._make_request("GET", "/stock/search", params={"q": query})
        return response.get("data", [])
    
    async def get_stock_levels(self, sku: str) -> Dict[str, Any]:
        """Get stock levels for a SKU."""
        return await self._make_request("GET", f"/stock/{sku}")
    
    async def create_sales_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a sales order."""
        return await self._make_request("POST", "/salesorder", data=order_data)
    
    async def get_sales_orders(self, customer_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get sales orders."""
        params = {"customer_id": customer_id} if customer_id else None
        response = await self._make_request("GET", "/salesorder", params=params)
        return response.get("data", [])
    
    async def create_purchase_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a purchase order."""
        return await self._make_request("POST", "/purchaseorder", data=order_data)


# Global instances
_myob_client: Optional[MYOBClient] = None
_sku_manager: Optional[SKUMappingManager] = None


async def get_myob_client() -> MYOBClient:
    """Get the global MYOB client instance."""
    global _myob_client
    
    if _myob_client is None:
        _myob_client = MYOBClient()
    
    return _myob_client


async def get_sku_mapping_manager() -> SKUMappingManager:
    """Get the global SKU mapping manager instance."""
    global _sku_manager
    
    if _sku_manager is None:
        _sku_manager = SKUMappingManager()
    
    return _sku_manager


async def map_customer_sku(customer_email: str, customer_sku: str) -> Optional[str]:
    """Map a customer SKU to internal SKU."""
    manager = await get_sku_mapping_manager()
    return await manager.get_internal_sku(customer_email, customer_sku)
