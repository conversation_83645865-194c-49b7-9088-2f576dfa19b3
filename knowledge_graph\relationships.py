"""
Knowledge graph relationship management for customer-order-product relationships.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum

from knowledge_graph.graphiti_setup import get_graphiti_manager
from database.operations import get_database_operations
from config.settings import get_settings

logger = logging.getLogger(__name__)


class RelationshipType(str, Enum):
    """Types of relationships in the knowledge graph."""
    PLACED_ORDER = "PLACED_ORDER"
    CONTAINS_PRODUCT = "CONTAINS_PRODUCT"
    HAS_SKU = "HAS_SKU"
    SUPPLIED_BY = "SUPPLIED_BY"
    INTERACTED_WITH = "INTERACTED_WITH"
    PROCESSED_BY = "PROCESSED_BY"
    RELATED_TO = "RELATED_TO"
    FOLLOWED_BY = "FOLLOWED_BY"
    SIMILAR_TO = "SIMILAR_TO"
    RECOMMENDED_FOR = "RECOMMENDED_FOR"


class RelationshipManager:
    """Manages knowledge graph relationships and temporal patterns."""
    
    def __init__(self):
        """Initialize relationship manager."""
        self.settings = get_settings()
        self.db_ops = get_database_operations()
        self.graphiti_manager = None
    
    async def initialize(self) -> None:
        """Initialize the relationship manager."""
        try:
            self.graphiti_manager = await get_graphiti_manager()
            if not self.graphiti_manager:
                logger.warning("Graphiti manager not available")
            else:
                logger.info("Relationship manager initialized successfully")
                
        except Exception as e:
            logger.error(f"Failed to initialize relationship manager: {e}")
    
    async def build_customer_order_relationships(self, customer_email: str) -> Dict[str, Any]:
        """Build comprehensive customer-order relationships."""
        try:
            if not self.graphiti_manager:
                await self.initialize()
            
            # Get customer order history from database
            orders = await self.db_ops.get_customer_orders(customer_email, limit=50)
            
            relationship_data = {
                "customer_email": customer_email,
                "total_orders": len(orders),
                "relationships_created": 0,
                "temporal_patterns": {},
                "product_affinities": {}
            }
            
            # Process each order
            for order in orders:
                try:
                    await self._process_order_relationships(order, relationship_data)
                except Exception as e:
                    logger.error(f"Error processing order {order.get('order_id')}: {e}")
            
            # Analyze temporal patterns
            relationship_data["temporal_patterns"] = await self._analyze_temporal_patterns(orders)
            
            # Calculate product affinities
            relationship_data["product_affinities"] = await self._calculate_product_affinities(orders)
            
            logger.info(f"Built relationships for {customer_email}: {relationship_data['relationships_created']} relationships")
            return relationship_data
            
        except Exception as e:
            logger.error(f"Error building customer relationships: {e}")
            return {"error": str(e)}
    
    async def _process_order_relationships(self, order: Dict[str, Any], relationship_data: Dict[str, Any]) -> None:
        """Process relationships for a single order."""
        try:
            order_id = order.get("order_id")
            customer_email = relationship_data["customer_email"]
            
            if not order_id or not self.graphiti_manager:
                return
            
            # Record order in knowledge graph
            order_data = {
                "order_id": order_id,
                "status": order.get("status"),
                "total_amount": order.get("total_amount"),
                "currency": order.get("currency", "USD"),
                "order_date": order.get("created_at", datetime.utcnow().isoformat())
            }
            
            # Get order products (simplified - in reality would get from order items)
            products = []
            if order.get("internal_sku"):
                products.append({
                    "sku": order.get("internal_sku"),
                    "customer_sku": order.get("customer_sku"),
                    "quantity": order.get("quantity", 1),
                    "unit_price": order.get("unit_price")
                })
            
            # Record in knowledge graph
            await self.graphiti_manager.record_customer_order(
                customer_email, order_id, order_data, products
            )
            
            relationship_data["relationships_created"] += 1 + len(products)
            
        except Exception as e:
            logger.error(f"Error processing order relationships: {e}")
    
    async def _analyze_temporal_patterns(self, orders: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze temporal patterns in customer orders."""
        try:
            patterns = {
                "order_frequency": {},
                "seasonal_trends": {},
                "time_between_orders": [],
                "peak_ordering_times": {}
            }
            
            if len(orders) < 2:
                return patterns
            
            # Sort orders by date
            sorted_orders = sorted(orders, key=lambda x: x.get("created_at", ""))
            
            # Calculate time between orders
            for i in range(1, len(sorted_orders)):
                try:
                    prev_date = datetime.fromisoformat(sorted_orders[i-1].get("created_at", "").replace('Z', '+00:00'))
                    curr_date = datetime.fromisoformat(sorted_orders[i].get("created_at", "").replace('Z', '+00:00'))
                    
                    days_between = (curr_date - prev_date).days
                    patterns["time_between_orders"].append(days_between)
                    
                except Exception as e:
                    logger.debug(f"Error parsing dates for temporal analysis: {e}")
            
            # Calculate average order frequency
            if patterns["time_between_orders"]:
                avg_days = sum(patterns["time_between_orders"]) / len(patterns["time_between_orders"])
                patterns["order_frequency"]["average_days_between_orders"] = avg_days
                patterns["order_frequency"]["estimated_annual_orders"] = 365 / avg_days if avg_days > 0 else 0
            
            # Analyze seasonal trends (by month)
            monthly_counts = {}
            for order in sorted_orders:
                try:
                    order_date = datetime.fromisoformat(order.get("created_at", "").replace('Z', '+00:00'))
                    month = order_date.strftime("%B")
                    monthly_counts[month] = monthly_counts.get(month, 0) + 1
                except Exception:
                    continue
            
            patterns["seasonal_trends"] = monthly_counts
            
            return patterns
            
        except Exception as e:
            logger.error(f"Error analyzing temporal patterns: {e}")
            return {}
    
    async def _calculate_product_affinities(self, orders: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate product affinity relationships."""
        try:
            affinities = {
                "frequently_ordered_together": {},
                "product_sequences": {},
                "sku_preferences": {}
            }
            
            # Track SKU usage
            sku_counts = {}
            for order in orders:
                internal_sku = order.get("internal_sku")
                customer_sku = order.get("customer_sku")
                
                if internal_sku:
                    sku_counts[internal_sku] = sku_counts.get(internal_sku, 0) + 1
                
                if customer_sku and internal_sku:
                    if customer_sku not in affinities["sku_preferences"]:
                        affinities["sku_preferences"][customer_sku] = {
                            "internal_sku": internal_sku,
                            "usage_count": 1
                        }
                    else:
                        affinities["sku_preferences"][customer_sku]["usage_count"] += 1
            
            # Find most frequently ordered products
            sorted_skus = sorted(sku_counts.items(), key=lambda x: x[1], reverse=True)
            affinities["top_products"] = sorted_skus[:10]
            
            return affinities
            
        except Exception as e:
            logger.error(f"Error calculating product affinities: {e}")
            return {}
    
    async def find_customer_clusters(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Find clusters of similar customers based on ordering patterns."""
        try:
            if not self.graphiti_manager:
                await self.initialize()
                
            if not self.graphiti_manager:
                return []
            
            # Get customer interactions from database
            interactions = await self.db_ops.get_interactions(limit=limit)
            
            # Group by customer
            customer_data = {}
            for interaction in interactions:
                email = interaction.get("customer_email")
                if email not in customer_data:
                    customer_data[email] = {
                        "email": email,
                        "interactions": [],
                        "orders": [],
                        "products": set()
                    }
                
                customer_data[email]["interactions"].append(interaction)
                
                # Extract product information
                metadata = interaction.get("metadata", {})
                if metadata.get("referenced_skus"):
                    customer_data[email]["products"].update(metadata["referenced_skus"])
            
            # Find similar customers based on product overlap
            clusters = []
            processed_customers = set()
            
            for email1, data1 in customer_data.items():
                if email1 in processed_customers:
                    continue
                
                cluster = {
                    "primary_customer": email1,
                    "similar_customers": [],
                    "common_products": list(data1["products"]),
                    "cluster_size": 1
                }
                
                for email2, data2 in customer_data.items():
                    if email1 != email2 and email2 not in processed_customers:
                        # Calculate product overlap
                        overlap = data1["products"].intersection(data2["products"])
                        overlap_ratio = len(overlap) / max(len(data1["products"]), len(data2["products"]), 1)
                        
                        if overlap_ratio > 0.3:  # 30% overlap threshold
                            cluster["similar_customers"].append({
                                "email": email2,
                                "overlap_ratio": overlap_ratio,
                                "common_products": list(overlap)
                            })
                            processed_customers.add(email2)
                            cluster["cluster_size"] += 1
                
                if cluster["cluster_size"] > 1:
                    clusters.append(cluster)
                
                processed_customers.add(email1)
            
            # Sort clusters by size
            clusters.sort(key=lambda x: x["cluster_size"], reverse=True)
            
            logger.info(f"Found {len(clusters)} customer clusters")
            return clusters
            
        except Exception as e:
            logger.error(f"Error finding customer clusters: {e}")
            return []
    
    async def generate_cross_sell_opportunities(self, customer_email: str) -> List[Dict[str, Any]]:
        """Generate cross-sell opportunities based on knowledge graph relationships."""
        try:
            if not self.graphiti_manager:
                await self.initialize()
                
            if not self.graphiti_manager:
                return []
            
            # Get customer insights from knowledge graph
            insights = await self.graphiti_manager.find_similar_customers(customer_email)
            recommendations = await self.graphiti_manager.get_product_recommendations(customer_email)
            
            # Get customer order history
            orders = await self.db_ops.get_customer_orders(customer_email, limit=20)
            customer_products = set()
            
            for order in orders:
                if order.get("internal_sku"):
                    customer_products.add(order["internal_sku"])
            
            # Generate opportunities
            opportunities = []
            
            # From similar customers
            for similar_customer in insights:
                customer_data = similar_customer.get("customer", {})
                common_products = similar_customer.get("common_products", 0)
                
                if common_products > 0:
                    opportunities.append({
                        "type": "similar_customer_pattern",
                        "description": f"Customers with {common_products} common products often order additional items",
                        "confidence": min(common_products / 10.0, 1.0),  # Normalize confidence
                        "similar_customer": customer_data.get("email", "unknown"),
                        "basis": f"{common_products} common products"
                    })
            
            # From product recommendations
            for recommendation in recommendations:
                product_data = recommendation.get("product", {})
                popularity = recommendation.get("popularity", 0)
                
                product_sku = product_data.get("sku")
                if product_sku and product_sku not in customer_products:
                    opportunities.append({
                        "type": "product_recommendation",
                        "description": f"Product {product_sku} is popular among similar customers",
                        "confidence": min(popularity / 20.0, 1.0),  # Normalize confidence
                        "recommended_product": product_data,
                        "basis": f"Ordered by {popularity} similar customers"
                    })
            
            # Sort by confidence
            opportunities.sort(key=lambda x: x["confidence"], reverse=True)
            
            logger.info(f"Generated {len(opportunities)} cross-sell opportunities for {customer_email}")
            return opportunities[:10]  # Return top 10
            
        except Exception as e:
            logger.error(f"Error generating cross-sell opportunities: {e}")
            return []
    
    async def update_relationship_weights(self) -> Dict[str, Any]:
        """Update relationship weights based on temporal patterns and frequency."""
        try:
            if not self.graphiti_manager:
                await self.initialize()
                
            if not self.graphiti_manager:
                return {"error": "Knowledge graph not available"}
            
            # This would implement sophisticated relationship weight updates
            # based on recency, frequency, and business value
            
            stats = await self.graphiti_manager.get_graph_stats()
            
            # Placeholder for weight update logic
            updates_made = 0
            
            return {
                "updates_made": updates_made,
                "graph_stats": stats,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error updating relationship weights: {e}")
            return {"error": str(e)}
    
    async def get_relationship_insights(self, customer_email: str) -> Dict[str, Any]:
        """Get comprehensive relationship insights for a customer."""
        try:
            # Build relationships
            relationship_data = await self.build_customer_order_relationships(customer_email)
            
            # Get cross-sell opportunities
            cross_sell = await self.generate_cross_sell_opportunities(customer_email)
            
            # Get knowledge graph insights
            if self.graphiti_manager:
                kg_insights = await self.graphiti_manager.query_customer_relationships(customer_email)
            else:
                kg_insights = []
            
            return {
                "customer_email": customer_email,
                "relationship_summary": relationship_data,
                "cross_sell_opportunities": cross_sell,
                "knowledge_graph_relationships": kg_insights,
                "generated_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting relationship insights: {e}")
            return {"error": str(e)}


# Global relationship manager instance
_relationship_manager: Optional[RelationshipManager] = None


async def get_relationship_manager() -> RelationshipManager:
    """Get the global relationship manager instance."""
    global _relationship_manager
    
    if _relationship_manager is None:
        _relationship_manager = RelationshipManager()
        await _relationship_manager.initialize()
    
    return _relationship_manager


async def build_customer_relationships(customer_email: str) -> Dict[str, Any]:
    """Build customer relationships in the knowledge graph."""
    manager = await get_relationship_manager()
    return await manager.build_customer_order_relationships(customer_email)


async def get_customer_insights(customer_email: str) -> Dict[str, Any]:
    """Get comprehensive customer insights."""
    manager = await get_relationship_manager()
    return await manager.get_relationship_insights(customer_email)


async def find_cross_sell_opportunities(customer_email: str) -> List[Dict[str, Any]]:
    """Find cross-sell opportunities for a customer."""
    manager = await get_relationship_manager()
    return await manager.generate_cross_sell_opportunities(customer_email)


async def discover_customer_clusters() -> List[Dict[str, Any]]:
    """Discover customer clusters based on ordering patterns."""
    manager = await get_relationship_manager()
    return await manager.find_customer_clusters()
