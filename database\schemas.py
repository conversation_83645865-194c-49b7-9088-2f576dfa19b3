"""
Database schemas for the multi-agent sales support system.
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, ConfigDict
from enum import Enum


class InteractionType(str, Enum):
    """Types of customer interactions."""
    EMAIL = "email"
    SUPPORT = "support"
    ORDER = "order"
    PURCHASING = "purchasing"
    SYSTEM = "system"


class OrderStatus(str, Enum):
    """Order status values."""
    PENDING = "pending"
    PROCESSING = "processing"
    SHIPPED = "shipped"
    DELIVERED = "delivered"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"


class EscalationLevel(str, Enum):
    """Escalation levels for support tickets."""
    NONE = "none"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class CustomerInteraction(BaseModel):
    """Schema for customer interactions table."""
    id: Optional[int] = None
    customer_email: str = Field(..., description="Customer email address")
    interaction_type: InteractionType = Field(..., description="Type of interaction")
    content: str = Field(..., description="Interaction content")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Additional metadata")
    embedding: Optional[List[float]] = Field(default=None, description="Vector embedding for semantic search")
    created_at: Optional[datetime] = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default_factory=datetime.utcnow)
    
    model_config = ConfigDict(from_attributes=True)


class SKUMapping(BaseModel):
    """Schema for customer SKU mappings table."""
    id: Optional[int] = None
    customer_email: str = Field(..., description="Customer email address")
    customer_sku: str = Field(..., description="Customer's SKU identifier")
    internal_sku: str = Field(..., description="Internal system SKU")
    description: Optional[str] = Field(default=None, description="SKU description")
    active: bool = Field(default=True, description="Whether mapping is active")
    created_at: Optional[datetime] = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default_factory=datetime.utcnow)
    
    model_config = ConfigDict(from_attributes=True)


class AgentMemory(BaseModel):
    """Schema for agent memory table."""
    id: Optional[int] = None
    agent_name: str = Field(..., description="Name of the agent")
    context_type: str = Field(..., description="Type of context (conversation, task, etc.)")
    context_data: Dict[str, Any] = Field(..., description="Context data")
    customer_email: Optional[str] = Field(default=None, description="Associated customer email")
    session_id: Optional[str] = Field(default=None, description="Session identifier")
    embedding: Optional[List[float]] = Field(default=None, description="Vector embedding")
    expires_at: Optional[datetime] = Field(default=None, description="Expiration timestamp")
    created_at: Optional[datetime] = Field(default_factory=datetime.utcnow)
    
    model_config = ConfigDict(from_attributes=True)


class OrderRecord(BaseModel):
    """Schema for order records table."""
    id: Optional[int] = None
    order_id: str = Field(..., description="External order ID")
    customer_email: str = Field(..., description="Customer email address")
    customer_sku: Optional[str] = Field(default=None, description="Customer SKU")
    internal_sku: Optional[str] = Field(default=None, description="Internal SKU")
    quantity: int = Field(..., description="Order quantity")
    unit_price: Optional[float] = Field(default=None, description="Unit price")
    total_amount: Optional[float] = Field(default=None, description="Total order amount")
    currency: str = Field(default="USD", description="Currency code")
    status: OrderStatus = Field(default=OrderStatus.PENDING, description="Order status")
    myob_order_id: Optional[str] = Field(default=None, description="MYOB EXO order ID")
    notes: Optional[str] = Field(default=None, description="Order notes")
    created_at: Optional[datetime] = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default_factory=datetime.utcnow)
    
    model_config = ConfigDict(from_attributes=True)


class SupportTicket(BaseModel):
    """Schema for support tickets table."""
    id: Optional[int] = None
    ticket_id: str = Field(..., description="Unique ticket identifier")
    customer_email: str = Field(..., description="Customer email address")
    subject: str = Field(..., description="Ticket subject")
    description: str = Field(..., description="Ticket description")
    category: Optional[str] = Field(default=None, description="Ticket category")
    priority: str = Field(default="medium", description="Ticket priority")
    status: str = Field(default="open", description="Ticket status")
    escalation_level: EscalationLevel = Field(default=EscalationLevel.NONE, description="Escalation level")
    assigned_agent: Optional[str] = Field(default=None, description="Assigned agent")
    resolution: Optional[str] = Field(default=None, description="Ticket resolution")
    resolved_at: Optional[datetime] = Field(default=None, description="Resolution timestamp")
    created_at: Optional[datetime] = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default_factory=datetime.utcnow)
    
    model_config = ConfigDict(from_attributes=True)


class InventoryAlert(BaseModel):
    """Schema for inventory alerts table."""
    id: Optional[int] = None
    internal_sku: str = Field(..., description="Internal SKU")
    current_quantity: int = Field(..., description="Current inventory quantity")
    threshold_quantity: int = Field(..., description="Alert threshold quantity")
    alert_type: str = Field(..., description="Type of alert (low_stock, out_of_stock)")
    status: str = Field(default="active", description="Alert status")
    purchase_order_created: bool = Field(default=False, description="Whether PO was created")
    purchase_order_id: Optional[str] = Field(default=None, description="Purchase order ID")
    resolved_at: Optional[datetime] = Field(default=None, description="Resolution timestamp")
    created_at: Optional[datetime] = Field(default_factory=datetime.utcnow)
    
    model_config = ConfigDict(from_attributes=True)


class EmailProcessingLog(BaseModel):
    """Schema for email processing log table."""
    id: Optional[int] = None
    message_id: str = Field(..., description="Gmail message ID")
    customer_email: str = Field(..., description="Sender email address")
    subject: str = Field(..., description="Email subject")
    processed: bool = Field(default=False, description="Whether email was processed")
    processing_status: str = Field(default="pending", description="Processing status")
    extracted_query: Optional[str] = Field(default=None, description="Extracted customer query")
    extracted_sku: Optional[str] = Field(default=None, description="Extracted SKU")
    attachments_count: int = Field(default=0, description="Number of attachments")
    error_message: Optional[str] = Field(default=None, description="Error message if processing failed")
    processed_at: Optional[datetime] = Field(default=None, description="Processing timestamp")
    created_at: Optional[datetime] = Field(default_factory=datetime.utcnow)
    
    model_config = ConfigDict(from_attributes=True)


class SystemMetrics(BaseModel):
    """Schema for system metrics table."""
    id: Optional[int] = None
    metric_name: str = Field(..., description="Name of the metric")
    metric_value: float = Field(..., description="Metric value")
    metric_unit: Optional[str] = Field(default=None, description="Unit of measurement")
    tags: Optional[Dict[str, str]] = Field(default=None, description="Metric tags")
    timestamp: Optional[datetime] = Field(default_factory=datetime.utcnow)
    
    model_config = ConfigDict(from_attributes=True)


class APIUsageLog(BaseModel):
    """Schema for API usage logging table."""
    id: Optional[int] = None
    api_name: str = Field(..., description="Name of the API (mistral, myob, gmail, etc.)")
    endpoint: str = Field(..., description="API endpoint called")
    method: str = Field(..., description="HTTP method")
    status_code: int = Field(..., description="Response status code")
    response_time_ms: float = Field(..., description="Response time in milliseconds")
    request_size: Optional[int] = Field(default=None, description="Request size in bytes")
    response_size: Optional[int] = Field(default=None, description="Response size in bytes")
    error_message: Optional[str] = Field(default=None, description="Error message if failed")
    customer_email: Optional[str] = Field(default=None, description="Associated customer email")
    agent_name: Optional[str] = Field(default=None, description="Agent that made the call")
    created_at: Optional[datetime] = Field(default_factory=datetime.utcnow)
    
    model_config = ConfigDict(from_attributes=True)


# SQL Schema Definitions for Supabase
SUPABASE_SCHEMA_SQL = """
-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Customer interactions table with vector embeddings
CREATE TABLE IF NOT EXISTS customer_interactions (
    id BIGSERIAL PRIMARY KEY,
    customer_email TEXT NOT NULL,
    interaction_type TEXT NOT NULL CHECK (interaction_type IN ('email', 'support', 'order', 'purchasing', 'system')),
    content TEXT NOT NULL,
    metadata JSONB,
    embedding vector(1536),  -- OpenAI embedding dimension
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- SKU mappings table
CREATE TABLE IF NOT EXISTS sku_mappings (
    id BIGSERIAL PRIMARY KEY,
    customer_email TEXT NOT NULL,
    customer_sku TEXT NOT NULL,
    internal_sku TEXT NOT NULL,
    description TEXT,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(customer_email, customer_sku)
);

-- Agent memory table
CREATE TABLE IF NOT EXISTS agent_memory (
    id BIGSERIAL PRIMARY KEY,
    agent_name TEXT NOT NULL,
    context_type TEXT NOT NULL,
    context_data JSONB NOT NULL,
    customer_email TEXT,
    session_id TEXT,
    embedding vector(1536),
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Order records table
CREATE TABLE IF NOT EXISTS order_records (
    id BIGSERIAL PRIMARY KEY,
    order_id TEXT NOT NULL UNIQUE,
    customer_email TEXT NOT NULL,
    customer_sku TEXT,
    internal_sku TEXT,
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2),
    total_amount DECIMAL(10,2),
    currency TEXT DEFAULT 'USD',
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded')),
    myob_order_id TEXT,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Support tickets table
CREATE TABLE IF NOT EXISTS support_tickets (
    id BIGSERIAL PRIMARY KEY,
    ticket_id TEXT NOT NULL UNIQUE,
    customer_email TEXT NOT NULL,
    subject TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT,
    priority TEXT DEFAULT 'medium',
    status TEXT DEFAULT 'open',
    escalation_level TEXT DEFAULT 'none' CHECK (escalation_level IN ('none', 'low', 'medium', 'high', 'critical')),
    assigned_agent TEXT,
    resolution TEXT,
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Inventory alerts table
CREATE TABLE IF NOT EXISTS inventory_alerts (
    id BIGSERIAL PRIMARY KEY,
    internal_sku TEXT NOT NULL,
    current_quantity INTEGER NOT NULL,
    threshold_quantity INTEGER NOT NULL,
    alert_type TEXT NOT NULL,
    status TEXT DEFAULT 'active',
    purchase_order_created BOOLEAN DEFAULT FALSE,
    purchase_order_id TEXT,
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Email processing log table
CREATE TABLE IF NOT EXISTS email_processing_log (
    id BIGSERIAL PRIMARY KEY,
    message_id TEXT NOT NULL UNIQUE,
    customer_email TEXT NOT NULL,
    subject TEXT NOT NULL,
    processed BOOLEAN DEFAULT FALSE,
    processing_status TEXT DEFAULT 'pending',
    extracted_query TEXT,
    extracted_sku TEXT,
    attachments_count INTEGER DEFAULT 0,
    error_message TEXT,
    processed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- System metrics table
CREATE TABLE IF NOT EXISTS system_metrics (
    id BIGSERIAL PRIMARY KEY,
    metric_name TEXT NOT NULL,
    metric_value DECIMAL NOT NULL,
    metric_unit TEXT,
    tags JSONB,
    timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- API usage log table
CREATE TABLE IF NOT EXISTS api_usage_log (
    id BIGSERIAL PRIMARY KEY,
    api_name TEXT NOT NULL,
    endpoint TEXT NOT NULL,
    method TEXT NOT NULL,
    status_code INTEGER NOT NULL,
    response_time_ms DECIMAL NOT NULL,
    request_size INTEGER,
    response_size INTEGER,
    error_message TEXT,
    customer_email TEXT,
    agent_name TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_customer_interactions_email ON customer_interactions(customer_email);
CREATE INDEX IF NOT EXISTS idx_customer_interactions_type ON customer_interactions(interaction_type);
CREATE INDEX IF NOT EXISTS idx_customer_interactions_created ON customer_interactions(created_at);
CREATE INDEX IF NOT EXISTS idx_customer_interactions_embedding ON customer_interactions USING ivfflat (embedding vector_cosine_ops);

CREATE INDEX IF NOT EXISTS idx_sku_mappings_customer ON sku_mappings(customer_email);
CREATE INDEX IF NOT EXISTS idx_sku_mappings_customer_sku ON sku_mappings(customer_sku);
CREATE INDEX IF NOT EXISTS idx_sku_mappings_internal_sku ON sku_mappings(internal_sku);

CREATE INDEX IF NOT EXISTS idx_agent_memory_agent ON agent_memory(agent_name);
CREATE INDEX IF NOT EXISTS idx_agent_memory_customer ON agent_memory(customer_email);
CREATE INDEX IF NOT EXISTS idx_agent_memory_session ON agent_memory(session_id);
CREATE INDEX IF NOT EXISTS idx_agent_memory_embedding ON agent_memory USING ivfflat (embedding vector_cosine_ops);

CREATE INDEX IF NOT EXISTS idx_order_records_customer ON order_records(customer_email);
CREATE INDEX IF NOT EXISTS idx_order_records_status ON order_records(status);
CREATE INDEX IF NOT EXISTS idx_order_records_created ON order_records(created_at);

CREATE INDEX IF NOT EXISTS idx_support_tickets_customer ON support_tickets(customer_email);
CREATE INDEX IF NOT EXISTS idx_support_tickets_status ON support_tickets(status);
CREATE INDEX IF NOT EXISTS idx_support_tickets_escalation ON support_tickets(escalation_level);

CREATE INDEX IF NOT EXISTS idx_inventory_alerts_sku ON inventory_alerts(internal_sku);
CREATE INDEX IF NOT EXISTS idx_inventory_alerts_status ON inventory_alerts(status);

CREATE INDEX IF NOT EXISTS idx_email_log_message ON email_processing_log(message_id);
CREATE INDEX IF NOT EXISTS idx_email_log_customer ON email_processing_log(customer_email);
CREATE INDEX IF NOT EXISTS idx_email_log_status ON email_processing_log(processing_status);

CREATE INDEX IF NOT EXISTS idx_system_metrics_name ON system_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_system_metrics_timestamp ON system_metrics(timestamp);

CREATE INDEX IF NOT EXISTS idx_api_usage_api ON api_usage_log(api_name);
CREATE INDEX IF NOT EXISTS idx_api_usage_customer ON api_usage_log(customer_email);
CREATE INDEX IF NOT EXISTS idx_api_usage_created ON api_usage_log(created_at);

-- Create function for similarity search
CREATE OR REPLACE FUNCTION match_interactions(
    query_embedding vector(1536),
    match_threshold float,
    match_count int,
    filter_customer_email text DEFAULT NULL
)
RETURNS TABLE (
    id bigint,
    customer_email text,
    interaction_type text,
    content text,
    metadata jsonb,
    similarity float
)
LANGUAGE sql STABLE
AS $$
    SELECT
        customer_interactions.id,
        customer_interactions.customer_email,
        customer_interactions.interaction_type,
        customer_interactions.content,
        customer_interactions.metadata,
        1 - (customer_interactions.embedding <=> query_embedding) AS similarity
    FROM customer_interactions
    WHERE 
        customer_interactions.embedding IS NOT NULL
        AND (filter_customer_email IS NULL OR customer_interactions.customer_email = filter_customer_email)
        AND 1 - (customer_interactions.embedding <=> query_embedding) > match_threshold
    ORDER BY customer_interactions.embedding <=> query_embedding
    LIMIT match_count;
$$;

-- Create function for agent memory search
CREATE OR REPLACE FUNCTION match_agent_memory(
    query_embedding vector(1536),
    match_threshold float,
    match_count int,
    filter_agent_name text DEFAULT NULL,
    filter_customer_email text DEFAULT NULL
)
RETURNS TABLE (
    id bigint,
    agent_name text,
    context_type text,
    context_data jsonb,
    customer_email text,
    similarity float
)
LANGUAGE sql STABLE
AS $$
    SELECT
        agent_memory.id,
        agent_memory.agent_name,
        agent_memory.context_type,
        agent_memory.context_data,
        agent_memory.customer_email,
        1 - (agent_memory.embedding <=> query_embedding) AS similarity
    FROM agent_memory
    WHERE 
        agent_memory.embedding IS NOT NULL
        AND (agent_memory.expires_at IS NULL OR agent_memory.expires_at > NOW())
        AND (filter_agent_name IS NULL OR agent_memory.agent_name = filter_agent_name)
        AND (filter_customer_email IS NULL OR agent_memory.customer_email = filter_customer_email)
        AND 1 - (agent_memory.embedding <=> query_embedding) > match_threshold
    ORDER BY agent_memory.embedding <=> query_embedding
    LIMIT match_count;
$$;

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_customer_interactions_updated_at BEFORE UPDATE ON customer_interactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sku_mappings_updated_at BEFORE UPDATE ON sku_mappings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_order_records_updated_at BEFORE UPDATE ON order_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_support_tickets_updated_at BEFORE UPDATE ON support_tickets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
"""
