---
url: "https://developer.myob.com/api/exo/exo-api-overview/"
title: "EXO API Overview"
---

[![](https://developer.myob.com/media/cms_page_media/23/myob_logo_small.png)](http://www.myob.com/)

# API Overview

What is the EXO API?

**Date Updated:** November 25th 2013

The EXO API (Application Programming Interface) is an interface which makes it easy for any app running on any device or operating system to connect and interact with EXO Business databases.

The end user must be using EXO Business version 2015.1 or later.

The EXO API module must be licensed annually by the end user (for which there is a cost) in order to be used for third party developments. Purchase and installation can be arranged by your EXO Business Partner.

### What can I build?

### The world is your oyster

We challenge you to create leading-edge apps that help **make business life easier**. That’s our mission at MYOB, and we encourage you to also keep that in mind when building your apps.

To get you thinking, here are some features we’ve heard EXO Business clients ask for in recent years:

- More customisable reports, and analysis tools
- Integration with online services
- Web stores, and online selling
- Tighter integration with third party apps
- Mobile apps that make it easier to work on the road

The API is out in the marketplace at present; however not all EXO Business services are currently accessible via the API, which means the scope of what you can build is limited at the moment. But that shouldn't stop you exploring what is possible with the EXO API. We are going to be releasing more and more services in stages, and lots of API services are currently being developed, including sales and purchases - register to be kept informed.

There really is no limit to what you can do with the EXO API. So, if you have an idea that will help make business life easier for EXO Business users, let's get started: [register](https://developer.myob.com/contact/) now.

**Disclaimer:** The MYOB EXO API provides an open interface for add-ons to extend and integrate with an EXO Business solution. However, please be aware that the API provides a subset of the functionality available within EXO Business. The API documentation outlines the functionality that is available but **does not outline the functionality that is not available**. If the API documentation does not explicitly state that it provides particular functionality you should assume that it is not supported.

Feel free to refer to the FAQs and feedback from other developers and our internal development team through [the API forums](http://community.myob.com/t5/EXO-API-and-Partner-Add-ons/bd-p/EXOAPI).