#!/usr/bin/env python3
"""
Test the Human Review Dashboard functionality
"""

import asyncio
from human_review_dashboard import HumanR<PERSON>iewDashboard
from rich.console import <PERSON>sole
from rich.table import Table

console = Console()

async def test_dashboard():
    """Test dashboard functionality."""
    
    console.print("[bold green]🧪 Testing Human Review Dashboard[/bold green]")
    console.print()
    
    # Initialize dashboard
    dashboard = HumanReviewDashboard()
    
    # Test 1: Fetch recent emails
    console.print("[cyan]📧 Testing: Fetch Recent Emails[/cyan]")
    emails = await dashboard.fetch_recent_emails(24)
    console.print(f"✅ Found {len(emails)} recent emails")
    
    if emails:
        # Show sample email structure
        sample = emails[0]
        console.print(f"   Sample email from: {sample.get('customer_email', 'unknown')}")
        metadata = sample.get('metadata', {})
        analysis = metadata.get('analysis', {})
        console.print(f"   Analysis keys: {list(analysis.keys())}")
    
    console.print()
    
    # Test 2: Get pending reviews
    console.print("[cyan]📋 Testing: Get Pending Reviews[/cyan]")
    pending = await dashboard.get_pending_reviews()
    console.print(f"✅ Found {len(pending)} emails pending review")
    
    # Show internal emails detected
    internal_count = 0
    for email in pending:
        metadata = email.get('metadata', {})
        analysis = metadata.get('analysis', {})
        if analysis.get('is_internal', False):
            internal_count += 1
    
    console.print(f"   Internal emails detected: {internal_count}")
    console.print()
    
    # Test 3: Show recent emails table
    console.print("[cyan]📊 Testing: Display Recent Emails[/cyan]")
    await dashboard.display_recent_emails()

    console.print()

    # Test 4: Show pending reviews
    console.print("[cyan]🔍 Testing: Display Pending Reviews[/cyan]")
    await dashboard.display_pending_reviews()

    console.print()

    # Test 5: Show statistics
    console.print("[cyan]📈 Testing: System Statistics[/cyan]")
    await dashboard.show_system_statistics()
    
    console.print()
    console.print("[bold green]✅ All dashboard tests completed successfully![/bold green]")
    console.print()
    console.print("[yellow]💡 To run the interactive dashboard:[/yellow]")
    console.print("[white]python human_review_dashboard.py[/white]")

if __name__ == "__main__":
    asyncio.run(test_dashboard())
