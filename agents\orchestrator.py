"""
Agent Orchestration and Workflow system using LangChain.
Coordinates all agents in a cohesive workflow with proper task sequencing and inter-agent communication.
"""

import asyncio
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union, Callable
from enum import Enum

from langchain.agents import Agent<PERSON>xecutor, create_openai_tools_agent
from langchain.tools import BaseTool, tool
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field

from config.settings import get_settings
from database.operations import get_database_operations
from database.schemas import CustomerInteraction, AgentMemory
from agents.email_monitor import get_email_monitoring_agent
from agents.customer_support import get_customer_support_agent, process_support_query
from agents.order_processing import get_order_processing_agent, process_order
from agents.purchasing import get_purchasing_agent
from knowledge_graph.relationships import get_customer_insights

logger = logging.getLogger(__name__)


class WorkflowType(str, Enum):
    """Types of workflows."""
    EMAIL_TO_SUPPORT = "email_to_support"
    ORDER_PROCESSING = "order_processing"
    INVENTORY_MANAGEMENT = "inventory_management"
    CUSTOMER_INQUIRY = "customer_inquiry"
    CROSS_SELL = "cross_sell"
    ESCALATION = "escalation"


class WorkflowStatus(str, Enum):
    """Workflow execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class WorkflowRequest(BaseModel):
    """Model for workflow requests."""
    workflow_type: WorkflowType = Field(..., description="Type of workflow to execute")
    customer_email: str = Field(..., description="Customer email address")
    input_data: Dict[str, Any] = Field(..., description="Input data for the workflow")
    priority: str = Field(default="medium", description="Workflow priority")
    session_id: Optional[str] = Field(default=None, description="Session identifier")
    context: Optional[Dict[str, Any]] = Field(default=None, description="Additional context")


class WorkflowResponse(BaseModel):
    """Model for workflow responses."""
    workflow_id: str = Field(..., description="Unique workflow identifier")
    status: WorkflowStatus = Field(..., description="Workflow status")
    result: Optional[Dict[str, Any]] = Field(default=None, description="Workflow result")
    steps_completed: List[str] = Field(default_factory=list, description="Completed workflow steps")
    error_message: Optional[str] = Field(default=None, description="Error message if failed")
    execution_time_ms: float = Field(..., description="Total execution time")
    agent_interactions: List[Dict[str, Any]] = Field(default_factory=list, description="Agent interaction log")


class WorkflowStep(BaseModel):
    """Model for individual workflow steps."""
    step_id: str = Field(..., description="Step identifier")
    agent_name: str = Field(..., description="Agent responsible for this step")
    action: str = Field(..., description="Action to perform")
    input_data: Dict[str, Any] = Field(..., description="Input data for the step")
    dependencies: List[str] = Field(default_factory=list, description="Step dependencies")
    timeout_seconds: int = Field(default=60, description="Step timeout")


class AgentOrchestrator:
    """Orchestrates multiple agents in coordinated workflows."""
    
    def __init__(self):
        """Initialize the agent orchestrator."""
        self.settings = get_settings()
        self.db_ops = get_database_operations()
        
        # Initialize LangChain components
        self.llm = ChatOpenAI(
            api_key=self.settings.openai_api_key,
            model="gpt-4",
            temperature=0.3
        ) if self.settings.openai_api_key else None
        
        # Agent references
        self.agents = {}
        self.active_workflows: Dict[str, Dict[str, Any]] = {}
        
        # Setup LangChain tools
        self.tools = self._setup_langchain_tools()
        
        # Setup orchestrator agent
        if self.llm:
            self.orchestrator_agent = self._setup_orchestrator_agent()
        else:
            self.orchestrator_agent = None
            logger.warning("OpenAI API key not available, using simplified orchestration")
    
    async def initialize(self) -> None:
        """Initialize the orchestrator and all agents."""
        try:
            # Initialize all agents
            self.agents = {
                "email_monitor": await get_email_monitoring_agent(),
                "customer_support": await get_customer_support_agent(),
                "order_processing": await get_order_processing_agent(),
                "purchasing": await get_purchasing_agent()
            }
            
            logger.info("Agent orchestrator initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize agent orchestrator: {e}")
            raise
    
    def _setup_langchain_tools(self) -> List[BaseTool]:
        """Setup LangChain tools for agent coordination."""
        
        @tool
        async def process_customer_support_query(customer_email: str, query: str, context: str = None) -> str:
            """Process a customer support query using the customer support agent."""
            try:
                context_dict = eval(context) if context else None
                response = await process_support_query(
                    customer_email=customer_email,
                    query=query,
                    context=context_dict
                )
                return f"Support response: {response.response} (Confidence: {response.confidence})"
            except Exception as e:
                return f"Error processing support query: {e}"
        
        @tool
        async def process_order_request(customer_email: str, action: str, order_data: str = None) -> str:
            """Process an order request using the order processing agent."""
            try:
                order_dict = eval(order_data) if order_data else {}
                response = await process_order(
                    customer_email=customer_email,
                    action=action,
                    order_data=order_dict
                )
                return f"Order response: {response.message} (Success: {response.success})"
            except Exception as e:
                return f"Error processing order: {e}"
        
        @tool
        async def get_customer_insights_tool(customer_email: str) -> str:
            """Get customer insights from the knowledge graph."""
            try:
                insights = await get_customer_insights(customer_email)
                return f"Customer insights: {insights}"
            except Exception as e:
                return f"Error getting customer insights: {e}"
        
        @tool
        async def escalate_to_human(customer_email: str, reason: str, context: str = None) -> str:
            """Escalate an issue to human agents."""
            try:
                # This would integrate with a ticketing system
                ticket_id = f"ESC-{uuid.uuid4().hex[:8].upper()}"
                return f"Escalated to human agents. Ticket ID: {ticket_id}. Reason: {reason}"
            except Exception as e:
                return f"Error escalating: {e}"
        
        return [
            process_customer_support_query,
            process_order_request,
            get_customer_insights_tool,
            escalate_to_human
        ]
    
    def _setup_orchestrator_agent(self) -> AgentExecutor:
        """Setup the main orchestrator agent using LangChain."""
        
        system_prompt = """
        You are an intelligent agent orchestrator for a sales support system. Your role is to:
        
        1. Analyze incoming requests and determine the best workflow
        2. Coordinate multiple specialized agents to handle complex tasks
        3. Ensure proper sequencing and data flow between agents
        4. Handle errors and escalations appropriately
        5. Provide comprehensive responses to customers
        
        Available agents and their capabilities:
        - Customer Support Agent: Handles general customer queries and provides information
        - Order Processing Agent: Manages order creation, updates, and status checks
        - Purchasing Agent: Handles inventory monitoring and purchase orders
        - Email Monitoring Agent: Processes incoming emails and extracts information
        
        Guidelines:
        - Always start by understanding the customer's intent
        - Use customer insights to personalize responses
        - Coordinate agents in logical sequence
        - Handle errors gracefully and escalate when necessary
        - Provide clear, helpful responses to customers
        - Log all interactions for audit and improvement
        
        When orchestrating workflows:
        1. Analyze the request type and customer context
        2. Determine which agents need to be involved
        3. Execute agents in the correct sequence
        4. Aggregate results and provide a comprehensive response
        5. Handle any errors or escalations
        """
        
        prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad")
        ])
        
        agent = create_openai_tools_agent(self.llm, self.tools, prompt)
        return AgentExecutor(agent=agent, tools=self.tools, verbose=True)
    
    async def execute_workflow(self, request: WorkflowRequest) -> WorkflowResponse:
        """Execute a workflow based on the request."""
        workflow_id = f"wf_{uuid.uuid4().hex[:8]}"
        start_time = datetime.utcnow()
        
        try:
            logger.info(f"Starting workflow {workflow_id}: {request.workflow_type} for {request.customer_email}")
            
            # Store workflow in active workflows
            self.active_workflows[workflow_id] = {
                "request": request,
                "status": WorkflowStatus.RUNNING,
                "start_time": start_time,
                "steps_completed": [],
                "agent_interactions": []
            }
            
            # Execute workflow based on type
            if request.workflow_type == WorkflowType.EMAIL_TO_SUPPORT:
                result = await self._execute_email_to_support_workflow(workflow_id, request)
            elif request.workflow_type == WorkflowType.ORDER_PROCESSING:
                result = await self._execute_order_processing_workflow(workflow_id, request)
            elif request.workflow_type == WorkflowType.CUSTOMER_INQUIRY:
                result = await self._execute_customer_inquiry_workflow(workflow_id, request)
            elif request.workflow_type == WorkflowType.INVENTORY_MANAGEMENT:
                result = await self._execute_inventory_management_workflow(workflow_id, request)
            elif request.workflow_type == WorkflowType.CROSS_SELL:
                result = await self._execute_cross_sell_workflow(workflow_id, request)
            else:
                raise ValueError(f"Unknown workflow type: {request.workflow_type}")
            
            # Mark workflow as completed
            self.active_workflows[workflow_id]["status"] = WorkflowStatus.COMPLETED
            execution_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            
            # Store workflow interaction
            await self._store_workflow_interaction(workflow_id, request, result)
            
            response = WorkflowResponse(
                workflow_id=workflow_id,
                status=WorkflowStatus.COMPLETED,
                result=result,
                steps_completed=self.active_workflows[workflow_id]["steps_completed"],
                execution_time_ms=execution_time,
                agent_interactions=self.active_workflows[workflow_id]["agent_interactions"]
            )
            
            logger.info(f"Completed workflow {workflow_id} in {execution_time:.2f}ms")
            return response
            
        except Exception as e:
            logger.error(f"Workflow {workflow_id} failed: {e}")
            
            # Mark workflow as failed
            if workflow_id in self.active_workflows:
                self.active_workflows[workflow_id]["status"] = WorkflowStatus.FAILED
            
            execution_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            
            return WorkflowResponse(
                workflow_id=workflow_id,
                status=WorkflowStatus.FAILED,
                error_message=str(e),
                steps_completed=self.active_workflows.get(workflow_id, {}).get("steps_completed", []),
                execution_time_ms=execution_time,
                agent_interactions=self.active_workflows.get(workflow_id, {}).get("agent_interactions", [])
            )
        
        finally:
            # Clean up workflow from active workflows
            if workflow_id in self.active_workflows:
                del self.active_workflows[workflow_id]
    
    async def _execute_email_to_support_workflow(self, workflow_id: str, request: WorkflowRequest) -> Dict[str, Any]:
        """Execute email-to-support workflow."""
        try:
            email_data = request.input_data
            customer_email = request.customer_email
            
            # Step 1: Extract query from email
            await self._log_workflow_step(workflow_id, "extract_query", "email_monitor")
            extracted_query = email_data.get("extracted_query", email_data.get("subject", ""))
            
            # Step 2: Get customer insights
            await self._log_workflow_step(workflow_id, "get_insights", "knowledge_graph")
            insights = await get_customer_insights(customer_email)
            
            # Step 3: Process support query
            await self._log_workflow_step(workflow_id, "process_support", "customer_support")
            support_response = await process_support_query(
                customer_email=customer_email,
                query=extracted_query,
                context={"email_data": email_data, "insights": insights}
            )
            
            # Step 4: Handle escalation if needed
            if support_response.requires_escalation:
                await self._log_workflow_step(workflow_id, "escalate", "human_agent")
                # Escalation logic would go here
            
            return {
                "workflow_type": "email_to_support",
                "extracted_query": extracted_query,
                "support_response": support_response.model_dump(),
                "customer_insights": insights,
                "escalated": support_response.requires_escalation
            }
            
        except Exception as e:
            logger.error(f"Error in email-to-support workflow: {e}")
            raise
    
    async def _execute_order_processing_workflow(self, workflow_id: str, request: WorkflowRequest) -> Dict[str, Any]:
        """Execute order processing workflow."""
        try:
            order_data = request.input_data
            customer_email = request.customer_email
            
            # Step 1: Get customer insights
            await self._log_workflow_step(workflow_id, "get_insights", "knowledge_graph")
            insights = await get_customer_insights(customer_email)
            
            # Step 2: Process order
            await self._log_workflow_step(workflow_id, "process_order", "order_processing")
            order_response = await process_order(
                customer_email=customer_email,
                action=order_data.get("action", "create"),
                order_data=order_data.get("order_details"),
                items=order_data.get("items", [])
            )
            
            # Step 3: Update inventory if order successful
            if order_response.success and order_data.get("action") == "create":
                await self._log_workflow_step(workflow_id, "update_inventory", "purchasing")
                # Inventory update logic would go here
            
            return {
                "workflow_type": "order_processing",
                "order_response": order_response.model_dump(),
                "customer_insights": insights
            }
            
        except Exception as e:
            logger.error(f"Error in order processing workflow: {e}")
            raise
    
    async def _execute_customer_inquiry_workflow(self, workflow_id: str, request: WorkflowRequest) -> Dict[str, Any]:
        """Execute customer inquiry workflow."""
        try:
            inquiry_data = request.input_data
            customer_email = request.customer_email
            
            # Use orchestrator agent if available
            if self.orchestrator_agent:
                await self._log_workflow_step(workflow_id, "orchestrate_inquiry", "orchestrator")
                
                input_text = f"""
                Customer: {customer_email}
                Inquiry: {inquiry_data.get('query', '')}
                Context: {inquiry_data.get('context', {})}
                
                Please coordinate the appropriate agents to handle this customer inquiry.
                """
                
                result = await self.orchestrator_agent.ainvoke({
                    "input": input_text,
                    "chat_history": []
                })
                
                return {
                    "workflow_type": "customer_inquiry",
                    "orchestrator_response": result["output"],
                    "inquiry_data": inquiry_data
                }
            else:
                # Fallback to simple support query processing
                await self._log_workflow_step(workflow_id, "process_support", "customer_support")
                support_response = await process_support_query(
                    customer_email=customer_email,
                    query=inquiry_data.get("query", ""),
                    context=inquiry_data.get("context")
                )
                
                return {
                    "workflow_type": "customer_inquiry",
                    "support_response": support_response.model_dump(),
                    "inquiry_data": inquiry_data
                }
            
        except Exception as e:
            logger.error(f"Error in customer inquiry workflow: {e}")
            raise
    
    async def _execute_inventory_management_workflow(self, workflow_id: str, request: WorkflowRequest) -> Dict[str, Any]:
        """Execute inventory management workflow."""
        try:
            inventory_data = request.input_data
            
            # Step 1: Check inventory levels
            await self._log_workflow_step(workflow_id, "check_inventory", "purchasing")
            purchasing_agent = self.agents.get("purchasing")
            
            if purchasing_agent:
                stats = await purchasing_agent.get_agent_stats()
                
                return {
                    "workflow_type": "inventory_management",
                    "inventory_stats": stats,
                    "inventory_data": inventory_data
                }
            else:
                raise Exception("Purchasing agent not available")
            
        except Exception as e:
            logger.error(f"Error in inventory management workflow: {e}")
            raise
    
    async def _execute_cross_sell_workflow(self, workflow_id: str, request: WorkflowRequest) -> Dict[str, Any]:
        """Execute cross-sell workflow."""
        try:
            customer_email = request.customer_email
            
            # Step 1: Get customer insights and recommendations
            await self._log_workflow_step(workflow_id, "get_insights", "knowledge_graph")
            insights = await get_customer_insights(customer_email)
            
            # Step 2: Generate personalized recommendations
            await self._log_workflow_step(workflow_id, "generate_recommendations", "customer_support")
            
            cross_sell_opportunities = insights.get("cross_sell_opportunities", [])
            
            return {
                "workflow_type": "cross_sell",
                "customer_insights": insights,
                "cross_sell_opportunities": cross_sell_opportunities
            }
            
        except Exception as e:
            logger.error(f"Error in cross-sell workflow: {e}")
            raise
    
    async def _log_workflow_step(self, workflow_id: str, step_name: str, agent_name: str) -> None:
        """Log a workflow step."""
        if workflow_id in self.active_workflows:
            self.active_workflows[workflow_id]["steps_completed"].append(step_name)
            self.active_workflows[workflow_id]["agent_interactions"].append({
                "step": step_name,
                "agent": agent_name,
                "timestamp": datetime.utcnow().isoformat()
            })
    
    async def _store_workflow_interaction(
        self,
        workflow_id: str,
        request: WorkflowRequest,
        result: Dict[str, Any]
    ) -> None:
        """Store workflow interaction in database."""
        try:
            interaction_content = f"Workflow: {request.workflow_type}\nWorkflow ID: {workflow_id}\nResult: {result}"
            
            metadata = {
                "agent": "orchestrator",
                "workflow_id": workflow_id,
                "workflow_type": request.workflow_type,
                "priority": request.priority,
                "session_id": request.session_id,
                "steps_completed": self.active_workflows.get(workflow_id, {}).get("steps_completed", [])
            }
            
            interaction = CustomerInteraction(
                customer_email=request.customer_email,
                interaction_type="workflow",
                content=interaction_content,
                metadata=metadata
            )
            
            await self.db_ops.create_interaction(interaction)
            
        except Exception as e:
            logger.error(f"Error storing workflow interaction: {e}")
    
    async def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get the status of a running workflow."""
        return self.active_workflows.get(workflow_id)
    
    async def cancel_workflow(self, workflow_id: str) -> bool:
        """Cancel a running workflow."""
        if workflow_id in self.active_workflows:
            self.active_workflows[workflow_id]["status"] = WorkflowStatus.CANCELLED
            return True
        return False
    
    async def get_orchestrator_stats(self) -> Dict[str, Any]:
        """Get orchestrator statistics."""
        try:
            # Get recent workflow interactions
            recent_interactions = await self.db_ops.get_interactions(
                interaction_type="workflow",
                limit=100
            )
            
            workflow_types = {}
            for interaction in recent_interactions:
                wf_type = interaction.get("metadata", {}).get("workflow_type", "unknown")
                workflow_types[wf_type] = workflow_types.get(wf_type, 0) + 1
            
            return {
                "active_workflows": len(self.active_workflows),
                "total_workflows_processed": len(recent_interactions),
                "workflow_types": workflow_types,
                "agents_available": list(self.agents.keys()),
                "orchestrator_agent_available": self.orchestrator_agent is not None
            }
            
        except Exception as e:
            logger.error(f"Error getting orchestrator stats: {e}")
            return {"error": str(e)}


# Global orchestrator instance
_orchestrator: Optional[AgentOrchestrator] = None


async def get_orchestrator() -> AgentOrchestrator:
    """Get the global orchestrator instance."""
    global _orchestrator
    
    if _orchestrator is None:
        _orchestrator = AgentOrchestrator()
        await _orchestrator.initialize()
    
    return _orchestrator


async def execute_workflow(
    workflow_type: str,
    customer_email: str,
    input_data: Dict[str, Any],
    priority: str = "medium",
    session_id: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None
) -> WorkflowResponse:
    """Execute a workflow."""
    orchestrator = await get_orchestrator()
    
    request = WorkflowRequest(
        workflow_type=WorkflowType(workflow_type),
        customer_email=customer_email,
        input_data=input_data,
        priority=priority,
        session_id=session_id,
        context=context
    )
    
    return await orchestrator.execute_workflow(request)


async def get_workflow_status(workflow_id: str) -> Optional[Dict[str, Any]]:
    """Get workflow status."""
    orchestrator = await get_orchestrator()
    return await orchestrator.get_workflow_status(workflow_id)


async def get_orchestration_stats() -> Dict[str, Any]:
    """Get orchestration statistics."""
    orchestrator = await get_orchestrator()
    return await orchestrator.get_orchestrator_stats()
