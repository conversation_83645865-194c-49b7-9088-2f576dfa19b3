"""
PDF processing tools using PyMuPDF for the multi-agent sales support system.
"""

import logging
import os
import re
import tempfile
import time
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import fitz  # PyMuPDF
import pymupdf4llm

from config.settings import get_settings

logger = logging.getLogger(__name__)


class PDFProcessingError(Exception):
    """Custom exception for PDF processing errors."""
    pass


class PDFProcessor:
    """Handles PDF document processing and information extraction."""
    
    def __init__(self):
        """Initialize PDF processor."""
        self.settings = get_settings()
        self.temp_dir = Path(self.settings.pdf_temp_dir)
        self.temp_dir.mkdir(parents=True, exist_ok=True)
    
    async def extract_text_from_pdf(self, pdf_data: bytes, filename: str) -> str:
        """Extract text from PDF data.
        
        Args:
            pdf_data: PDF file data as bytes
            filename: Original filename for reference
            
        Returns:
            Extracted text content
        """
        try:
            # Check file size
            if len(pdf_data) > self.settings.pdf_max_size:
                raise PDFProcessingError(f"PDF file too large: {len(pdf_data)} bytes")
            
            # Create temporary file
            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
                temp_file.write(pdf_data)
                temp_path = temp_file.name
            
            try:
                # Open PDF with PyMuPDF
                doc = fitz.open(temp_path)
                
                # Extract text from all pages
                text_content = ""
                for page_num in range(doc.page_count):
                    page = doc[page_num]
                    text_content += page.get_text()
                    text_content += "\n\n"  # Separate pages
                
                doc.close()
                
                logger.debug(f"Extracted {len(text_content)} characters from PDF {filename}")
                return text_content.strip()
                
            finally:
                # Clean up temporary file
                os.unlink(temp_path)
                
        except Exception as e:
            logger.error(f"Error extracting text from PDF {filename}: {e}")
            raise PDFProcessingError(f"Failed to extract text from PDF: {e}")
    
    async def extract_structured_data_from_pdf(self, pdf_data: bytes, filename: str) -> Dict[str, Any]:
        """Extract structured data from PDF using pymupdf4llm.
        
        Args:
            pdf_data: PDF file data as bytes
            filename: Original filename for reference
            
        Returns:
            Structured data extracted from PDF
        """
        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
                temp_file.write(pdf_data)
                temp_path = temp_file.name
            
            try:
                # Use pymupdf4llm for structured extraction
                md_text = pymupdf4llm.to_markdown(temp_path)
                
                # Extract structured information
                structured_data = {
                    "filename": filename,
                    "markdown_content": md_text,
                    "extracted_info": await self._parse_order_information(md_text),
                    "tables": await self._extract_tables(temp_path),
                    "metadata": await self._extract_metadata(temp_path)
                }
                
                logger.debug(f"Extracted structured data from PDF {filename}")
                return structured_data
                
            finally:
                # Clean up temporary file
                os.unlink(temp_path)
                
        except Exception as e:
            logger.error(f"Error extracting structured data from PDF {filename}: {e}")
            raise PDFProcessingError(f"Failed to extract structured data from PDF: {e}")
    
    async def _parse_order_information(self, text: str) -> Dict[str, Any]:
        """Parse order information from extracted text.
        
        Args:
            text: Extracted text content
            
        Returns:
            Parsed order information
        """
        order_info = {
            "order_number": None,
            "customer_info": {},
            "items": [],
            "totals": {},
            "dates": {},
            "skus": []
        }
        
        try:
            # Extract order number
            order_patterns = [
                r"order\s*#?\s*:?\s*([A-Z0-9\-]+)",
                r"po\s*#?\s*:?\s*([A-Z0-9\-]+)",
                r"purchase\s*order\s*:?\s*([A-Z0-9\-]+)"
            ]
            
            for pattern in order_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    order_info["order_number"] = match.group(1)
                    break
            
            # Extract customer information
            email_pattern = r"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})"
            email_match = re.search(email_pattern, text)
            if email_match:
                order_info["customer_info"]["email"] = email_match.group(1)
            
            # Extract phone numbers
            phone_pattern = r"(\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4})"
            phone_match = re.search(phone_pattern, text)
            if phone_match:
                order_info["customer_info"]["phone"] = phone_match.group(1)
            
            # Extract SKUs and item codes
            sku_patterns = [
                r"sku\s*:?\s*([A-Z0-9\-]+)",
                r"item\s*#?\s*:?\s*([A-Z0-9\-]+)",
                r"part\s*#?\s*:?\s*([A-Z0-9\-]+)",
                r"product\s*code\s*:?\s*([A-Z0-9\-]+)"
            ]
            
            for pattern in sku_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                order_info["skus"].extend(matches)
            
            # Remove duplicates
            order_info["skus"] = list(set(order_info["skus"]))
            
            # Extract quantities and prices
            quantity_price_pattern = r"(\d+)\s*x?\s*([A-Z0-9\-]+).*?\$?(\d+\.?\d*)"
            matches = re.findall(quantity_price_pattern, text, re.IGNORECASE)
            
            for match in matches:
                quantity, item_code, price = match
                order_info["items"].append({
                    "quantity": int(quantity),
                    "item_code": item_code,
                    "price": float(price) if price else None
                })
            
            # Extract total amounts
            total_patterns = [
                r"total\s*:?\s*\$?(\d+\.?\d*)",
                r"amount\s*:?\s*\$?(\d+\.?\d*)",
                r"subtotal\s*:?\s*\$?(\d+\.?\d*)"
            ]
            
            for pattern in total_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    order_info["totals"]["total"] = float(match.group(1))
                    break
            
            # Extract dates
            date_patterns = [
                r"date\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})",
                r"(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})"
            ]
            
            for pattern in date_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    order_info["dates"]["order_date"] = match.group(1)
                    break
            
            logger.debug(f"Parsed order information: {order_info}")
            return order_info
            
        except Exception as e:
            logger.error(f"Error parsing order information: {e}")
            return order_info
    
    async def _extract_tables(self, pdf_path: str) -> List[Dict[str, Any]]:
        """Extract tables from PDF.
        
        Args:
            pdf_path: Path to PDF file
            
        Returns:
            List of extracted tables
        """
        tables = []
        
        try:
            doc = fitz.open(pdf_path)
            
            for page_num in range(doc.page_count):
                page = doc[page_num]
                
                # Find tables on the page
                page_tables = page.find_tables()
                
                for table in page_tables:
                    table_data = table.extract()
                    if table_data:
                        tables.append({
                            "page": page_num + 1,
                            "data": table_data,
                            "bbox": table.bbox
                        })
            
            doc.close()
            logger.debug(f"Extracted {len(tables)} tables from PDF")
            
        except Exception as e:
            logger.error(f"Error extracting tables: {e}")
        
        return tables
    
    async def _extract_metadata(self, pdf_path: str) -> Dict[str, Any]:
        """Extract metadata from PDF.
        
        Args:
            pdf_path: Path to PDF file
            
        Returns:
            PDF metadata
        """
        metadata = {}
        
        try:
            doc = fitz.open(pdf_path)
            
            # Get document metadata
            doc_metadata = doc.metadata
            metadata.update(doc_metadata)
            
            # Get document info
            metadata.update({
                "page_count": doc.page_count,
                "is_pdf": doc.is_pdf,
                "is_encrypted": doc.is_encrypted,
                "needs_pass": doc.needs_pass
            })
            
            doc.close()
            
        except Exception as e:
            logger.error(f"Error extracting metadata: {e}")
        
        return metadata
    
    async def extract_order_from_pdf(self, pdf_data: bytes, filename: str) -> Dict[str, Any]:
        """Extract order information from PDF (main method).
        
        Args:
            pdf_data: PDF file data as bytes
            filename: Original filename
            
        Returns:
            Extracted order information
        """
        try:
            # Extract structured data
            structured_data = await self.extract_structured_data_from_pdf(pdf_data, filename)
            
            # Also extract plain text as fallback
            plain_text = await self.extract_text_from_pdf(pdf_data, filename)
            
            # Combine results
            result = {
                "filename": filename,
                "file_size": len(pdf_data),
                "plain_text": plain_text,
                "structured_data": structured_data,
                "processing_status": "success",
                "error": None
            }
            
            logger.info(f"Successfully processed PDF {filename}")
            return result
            
        except Exception as e:
            logger.error(f"Error processing PDF {filename}: {e}")
            return {
                "filename": filename,
                "file_size": len(pdf_data),
                "plain_text": "",
                "structured_data": {},
                "processing_status": "error",
                "error": str(e)
            }
    
    async def is_order_document(self, text: str) -> bool:
        """Determine if the document appears to be an order/purchase order.
        
        Args:
            text: Extracted text content
            
        Returns:
            True if document appears to be an order
        """
        order_keywords = [
            "purchase order", "po", "order", "invoice", "quote",
            "quotation", "estimate", "proposal", "sku", "item",
            "quantity", "price", "total", "amount", "customer"
        ]
        
        text_lower = text.lower()
        keyword_count = sum(1 for keyword in order_keywords if keyword in text_lower)
        
        # Consider it an order document if it contains multiple order-related keywords
        return keyword_count >= 3
    
    async def cleanup_temp_files(self) -> int:
        """Clean up temporary PDF files.
        
        Returns:
            Number of files cleaned up
        """
        try:
            cleanup_count = 0
            
            for file_path in self.temp_dir.glob("*.pdf"):
                try:
                    # Check if file is older than cleanup interval
                    file_age = time.time() - file_path.stat().st_mtime
                    if file_age > self.settings.pdf_cleanup_interval:
                        file_path.unlink()
                        cleanup_count += 1
                except Exception as e:
                    logger.warning(f"Failed to cleanup file {file_path}: {e}")
            
            if cleanup_count > 0:
                logger.info(f"Cleaned up {cleanup_count} temporary PDF files")
            
            return cleanup_count
            
        except Exception as e:
            logger.error(f"Error during PDF cleanup: {e}")
            return 0


# Global PDF processor instance
_pdf_processor: Optional[PDFProcessor] = None


def get_pdf_processor() -> PDFProcessor:
    """Get the global PDF processor instance."""
    global _pdf_processor
    
    if _pdf_processor is None:
        _pdf_processor = PDFProcessor()
    
    return _pdf_processor


async def process_pdf_attachment(pdf_data: bytes, filename: str) -> Dict[str, Any]:
    """Process a PDF attachment and extract order information."""
    processor = get_pdf_processor()
    return await processor.extract_order_from_pdf(pdf_data, filename)


async def extract_text_from_pdf(pdf_data: bytes, filename: str) -> str:
    """Extract plain text from PDF."""
    processor = get_pdf_processor()
    return await processor.extract_text_from_pdf(pdf_data, filename)


async def is_order_pdf(text: str) -> bool:
    """Check if PDF content appears to be an order document."""
    processor = get_pdf_processor()
    return await processor.is_order_document(text)
