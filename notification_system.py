#!/usr/bin/env python3
"""
Notification System for Critical Email Alerts
Sends notifications when emails require human review
"""

import asyncio
import json
import os
import smtplib
from datetime import datetime, timezone
from email.mime.text import MIMEText
from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Dict, Any

import httpx
from dotenv import load_dotenv

# Load environment
load_dotenv()

class NotificationSystem:
    """Handle notifications for critical emails requiring human review."""
    
    def __init__(self):
        """Initialize notification system."""
        self.smtp_server = os.getenv("SMTP_SERVER", "smtp.gmail.com")
        self.smtp_port = int(os.getenv("SMTP_PORT", "587"))
        self.smtp_username = os.getenv("SMTP_USERNAME")
        self.smtp_password = os.getenv("SMTP_PASSWORD")
        self.notification_email = os.getenv("NOTIFICATION_EMAIL", "<EMAIL>")
        self.slack_webhook = os.getenv("SLACK_WEBHOOK_URL")
    
    async def send_email_review_alert(self, email_data: Dict[str, Any], analysis: Dict[str, Any]):
        """Send email alert for emails requiring human review."""
        try:
            subject = f"🚨 Email Review Required - {analysis.get('urgency', 'medium').upper()}"
            
            # Create email content
            body = f"""
Email Review Required

From: {email_data.get('from', 'Unknown')}
Subject: {email_data.get('subject', 'No subject')}
Time: {email_data.get('created_at', 'Unknown')}

AI Analysis:
- Intent: {analysis.get('intent', 'unknown')}
- Urgency: {analysis.get('urgency', 'unknown')}
- Confidence: {analysis.get('confidence', 'unknown')}
- Review Reason: {analysis.get('review_reason', 'Unknown')}

Summary: {analysis.get('summary', 'No summary available')}

Please review this email in the human review system:
http://localhost:8080/review

Email Content Preview:
{email_data.get('content', 'No content')[:500]}...
"""
            
            await self._send_email(subject, body)
            
        except Exception as e:
            print(f"Error sending email notification: {e}")
    
    async def send_slack_alert(self, email_data: Dict[str, Any], analysis: Dict[str, Any]):
        """Send Slack notification for critical emails."""
        if not self.slack_webhook:
            return
        
        try:
            urgency = analysis.get('urgency', 'medium')
            urgency_emoji = {
                'urgent': '🚨',
                'high': '⚠️',
                'medium': '📧',
                'low': '📝'
            }.get(urgency.lower(), '📧')
            
            message = {
                "text": f"{urgency_emoji} Email Review Required",
                "blocks": [
                    {
                        "type": "header",
                        "text": {
                            "type": "plain_text",
                            "text": f"{urgency_emoji} Email Review Required - {urgency.upper()}"
                        }
                    },
                    {
                        "type": "section",
                        "fields": [
                            {
                                "type": "mrkdwn",
                                "text": f"*From:* {email_data.get('from', 'Unknown')}"
                            },
                            {
                                "type": "mrkdwn",
                                "text": f"*Subject:* {email_data.get('subject', 'No subject')}"
                            },
                            {
                                "type": "mrkdwn",
                                "text": f"*Intent:* {analysis.get('intent', 'unknown')}"
                            },
                            {
                                "type": "mrkdwn",
                                "text": f"*Confidence:* {analysis.get('confidence', 'unknown')}"
                            }
                        ]
                    },
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": f"*Review Reason:* {analysis.get('review_reason', 'Unknown')}\n*Summary:* {analysis.get('summary', 'No summary')[:200]}..."
                        }
                    },
                    {
                        "type": "actions",
                        "elements": [
                            {
                                "type": "button",
                                "text": {
                                    "type": "plain_text",
                                    "text": "Review Email"
                                },
                                "url": "http://localhost:8080/review",
                                "style": "primary"
                            }
                        ]
                    }
                ]
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(self.slack_webhook, json=message)
                response.raise_for_status()
                
        except Exception as e:
            print(f"Error sending Slack notification: {e}")
    
    async def _send_email(self, subject: str, body: str):
        """Send email notification."""
        if not self.smtp_username or not self.smtp_password:
            print("SMTP credentials not configured, skipping email notification")
            return
        
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.smtp_username
            msg['To'] = self.notification_email
            msg['Subject'] = subject
            
            # Add body
            msg.attach(MIMEText(body, 'plain'))
            
            # Send email
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.smtp_username, self.smtp_password)
            text = msg.as_string()
            server.sendmail(self.smtp_username, self.notification_email, text)
            server.quit()
            
            print(f"Email notification sent to {self.notification_email}")
            
        except Exception as e:
            print(f"Error sending email: {e}")
    
    async def send_daily_summary(self, stats: Dict[str, Any]):
        """Send daily summary of email processing."""
        try:
            subject = f"📊 Daily Email Processing Summary - {datetime.now(timezone.utc).strftime('%Y-%m-%d')}"
            
            body = f"""
Daily Email Processing Summary

Total Emails Processed: {stats.get('total_emails', 0)}
Emails Requiring Review: {stats.get('review_required', 0)}
Auto-Approved: {stats.get('auto_approved', 0)}
High Urgency: {stats.get('high_urgency', 0)}
Complaints: {stats.get('complaints', 0)}

Top Intents:
{chr(10).join([f"- {intent}: {count}" for intent, count in stats.get('top_intents', {}).items()])}

Average Confidence: {stats.get('avg_confidence', 0):.2f}
Average Response Time: {stats.get('avg_response_time', 0):.2f}s

System Status: {stats.get('system_status', 'Unknown')}
"""
            
            await self._send_email(subject, body)
            
        except Exception as e:
            print(f"Error sending daily summary: {e}")

# Global notification instance
notification_system = NotificationSystem()

async def notify_review_required(email_data: Dict[str, Any], analysis: Dict[str, Any]):
    """Send notifications for emails requiring review."""
    urgency = analysis.get('urgency', 'low').lower()
    
    # Always send email notification for high/urgent
    if urgency in ['high', 'urgent']:
        await notification_system.send_email_review_alert(email_data, analysis)
        await notification_system.send_slack_alert(email_data, analysis)
    
    # Send Slack for medium urgency
    elif urgency == 'medium':
        await notification_system.send_slack_alert(email_data, analysis)
    
    # Log all notifications
    print(f"Notification sent for {urgency} urgency email from {email_data.get('from', 'unknown')}")

if __name__ == "__main__":
    # Test notification system
    test_email = {
        "from": "<EMAIL>",
        "subject": "Test Email",
        "content": "This is a test email for notification system",
        "created_at": datetime.now(timezone.utc).isoformat()
    }
    
    test_analysis = {
        "intent": "complaint",
        "urgency": "high",
        "confidence": 0.6,
        "review_reason": "Low confidence and complaint detected",
        "summary": "Customer complaint about product quality"
    }
    
    asyncio.run(notify_review_required(test_email, test_analysis))
