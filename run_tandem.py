#!/usr/bin/env python3
"""
Run Simple Main and Human Review Dashboard in Tandem
Launches both applications simultaneously with proper process management
"""

import asyncio
import subprocess
import sys
import os
import time
import signal
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.columns import Columns
from rich.text import Text
from rich.live import Live
from rich.table import Table

console = Console()

class TandemRunner:
    """Manages running multiple applications simultaneously."""
    
    def __init__(self):
        self.processes = {}
        self.running = True
        self.venv_python = "./venv/Scripts/python.exe"
        
    def start_simple_main(self):
        """Start the simple main application."""
        try:
            console.print("🚀 [bold blue]Starting Simple Main Application...[/bold blue]")
            
            # Start simple_main.py in the background
            process = subprocess.Popen(
                [self.venv_python, "simple_main.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.processes['simple_main'] = process
            console.print("✅ [green]Simple Main started successfully[/green]")
            return True
            
        except Exception as e:
            console.print(f"❌ [red]Failed to start Simple Main: {e}[/red]")
            return False
    
    def start_human_review_dashboard(self):
        """Start the human review dashboard."""
        try:
            console.print("🔍 [bold yellow]Starting Human Review Dashboard...[/bold yellow]")
            
            # Start human_review_dashboard.py in the background
            process = subprocess.Popen(
                [self.venv_python, "human_review_dashboard.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.processes['dashboard'] = process
            console.print("✅ [green]Human Review Dashboard started successfully[/green]")
            return True
            
        except Exception as e:
            console.print(f"❌ [red]Failed to start Human Review Dashboard: {e}[/red]")
            return False
    
    def show_email_params(self):
        """Show email fetching parameters."""
        try:
            console.print("📋 [bold cyan]Showing Email Parameters...[/bold cyan]")
            
            # Run the email params info script
            result = subprocess.run(
                [self.venv_python, "email_params_info.py"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                console.print(result.stdout)
            else:
                console.print(f"❌ [red]Error showing email params: {result.stderr}[/red]")
                
        except Exception as e:
            console.print(f"❌ [red]Failed to show email params: {e}[/red]")
    
    def monitor_processes(self):
        """Monitor running processes and show status."""
        status_table = Table(title="🔄 Process Status Monitor", show_header=True)
        status_table.add_column("Application", style="cyan", width=25)
        status_table.add_column("Status", style="green", width=15)
        status_table.add_column("PID", style="yellow", width=10)
        status_table.add_column("Info", style="white", width=40)
        
        for name, process in self.processes.items():
            if process.poll() is None:  # Process is running
                status = "🟢 Running"
                pid = str(process.pid)
                if name == 'simple_main':
                    info = "Email monitoring, AI processing, Web API on :8080"
                else:
                    info = "Human review interface, HITL dashboard"
            else:  # Process has terminated
                status = "🔴 Stopped"
                pid = "N/A"
                info = f"Exit code: {process.returncode}"
            
            status_table.add_row(name.replace('_', ' ').title(), status, pid, info)
        
        return status_table
    
    def cleanup(self):
        """Clean up all processes."""
        console.print("\n🛑 [bold red]Shutting down applications...[/bold red]")
        
        for name, process in self.processes.items():
            if process.poll() is None:  # Process is still running
                console.print(f"⏹️  Stopping {name}...")
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    console.print(f"🔨 Force killing {name}...")
                    process.kill()
                    process.wait()
                console.print(f"✅ {name} stopped")
        
        console.print("🏁 [green]All applications stopped successfully[/green]")
    
    def run(self):
        """Main run loop."""
        try:
            # Show startup banner
            startup_panel = Panel(
                """[bold blue]🚀 TANDEM APPLICATION LAUNCHER 🚀[/bold blue]

[cyan]Starting:[/cyan]
• Simple Main Application (Email monitoring, AI processing, Web API)
• Human Review Dashboard (HITL interface)

[yellow]Email Fetching Parameters:[/yellow]
• Query: 'is:unread' (Gmail API)
• Max Results: 10 emails per check
• Frequency: Every 30 seconds
• Filters: Duplicate prevention, internal sender detection

[green]Press Ctrl+C to stop all applications[/green]""",
                title="🎯 Multi-Agent Sales Support System",
                border_style="blue"
            )
            console.print(startup_panel)
            
            # Show email parameters first
            self.show_email_params()
            
            # Start applications
            simple_main_started = self.start_simple_main()
            time.sleep(2)  # Give simple_main time to start
            
            dashboard_started = self.start_human_review_dashboard()
            
            if not simple_main_started and not dashboard_started:
                console.print("❌ [red]Failed to start any applications[/red]")
                return
            
            # Monitor loop
            console.print("\n📊 [bold green]Applications running! Monitoring status...[/bold green]")
            console.print("🌐 [cyan]Web API available at: http://localhost:8080[/cyan]")
            console.print("🔍 [yellow]Human Review Dashboard running in background[/yellow]")
            console.print("\n[dim]Press Ctrl+C to stop all applications[/dim]\n")
            
            # Live monitoring
            with Live(self.monitor_processes(), refresh_per_second=1) as live:
                while self.running:
                    time.sleep(1)
                    live.update(self.monitor_processes())
                    
                    # Check if any process has died
                    for name, process in self.processes.items():
                        if process.poll() is not None:
                            console.print(f"⚠️  [yellow]{name} has stopped unexpectedly[/yellow]")
        
        except KeyboardInterrupt:
            console.print("\n🛑 [yellow]Received interrupt signal[/yellow]")
        except Exception as e:
            console.print(f"\n❌ [red]Error in main loop: {e}[/red]")
        finally:
            self.running = False
            self.cleanup()

def main():
    """Main entry point."""
    # Ensure we're in the right directory
    if not Path("simple_main.py").exists():
        console.print("❌ [red]simple_main.py not found. Please run from the project root directory.[/red]")
        sys.exit(1)
    
    if not Path("human_review_dashboard.py").exists():
        console.print("❌ [red]human_review_dashboard.py not found. Please run from the project root directory.[/red]")
        sys.exit(1)
    
    if not Path("./venv/Scripts/python.exe").exists():
        console.print("❌ [red]Virtual environment not found. Please ensure ./venv/ exists.[/red]")
        sys.exit(1)
    
    # Create logs directory if it doesn't exist
    Path("logs").mkdir(exist_ok=True)
    
    # Run the tandem applications
    runner = TandemRunner()
    runner.run()

if __name__ == "__main__":
    main()
