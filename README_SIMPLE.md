# Simplified AI Sales Support System

A clean, focused AI framework for sales support using:
- **Mistral AI Agents API** for intelligent processing
- **Gmail API** for email monitoring and responses
- **MYOB EXO API** for ERP integration
- **Supabase with pgvector** for memory and data storage
- **Graphiti by <PERSON><PERSON>** for temporal knowledge management

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Gmail API     │    │   Mistral AI    │    │   MYOB EXO API  │
│   📧 Emails     │    │   🤖 Agents     │    │   🏢 ERP Data   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Sales Support  │
                    │     System      │
                    │   🚀 Core AI    │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Supabase      │    │    Graphiti     │    │   File System   │
│   🗄️ Memory     │    │   🧠 Temporal   │    │   📁 Logs       │
│   + pgvector    │    │   Knowledge     │    │   + Configs     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements_simple.txt
```

### 2. Configure Environment

Copy and update the environment file:
```bash
cp .env.simple .env
```

Update `.env` with your API keys:
- `MISTRAL_API_KEY`: Your Mistral AI API key
- `SUPABASE_URL` & `SUPABASE_SERVICE_KEY`: Your Supabase project credentials
- `MYOB_EXO_API_URL` & `MYOB_EXO_API_KEY`: Your MYOB EXO API settings

### 3. Set Up Gmail (Optional)

1. Download `credentials.json` from Google Cloud Console
2. Place it in the project root
3. Run the system - it will guide you through OAuth setup

### 4. Test the Framework

```bash
python test_simple_framework.py
```

### 5. Run the System

```bash
python simple_main.py
```

## 📁 Project Structure

```
simplified-ai-framework/
├── simple_main.py              # Main application entry point
├── agents/                     # AI agents
│   ├── simple_email_agent.py   # Gmail integration + Mistral AI
│   ├── simple_support_agent.py # Customer support + Mistral AI
│   └── simple_myob_agent.py    # MYOB EXO integration + Mistral AI
├── requirements_simple.txt     # Clean dependencies
├── .env.simple                 # Environment template
├── test_simple_framework.py    # Test suite
└── logs/                       # Application logs
```

## 🤖 AI Agents

### Email Agent
- **Purpose**: Monitor Gmail, process incoming emails
- **AI**: Uses Mistral AI to analyze email intent, urgency, and content
- **Actions**: Categorize emails, extract key information, suggest responses

### Support Agent
- **Purpose**: Handle customer queries and support requests
- **AI**: Uses Mistral AI with customer context and history
- **Memory**: Retrieves context from Supabase and temporal knowledge from Graphiti

### MYOB Agent
- **Purpose**: Integrate with MYOB EXO for ERP operations
- **AI**: Uses Mistral AI to process and validate order data
- **Operations**: Customer search, product lookup, order creation, SKU mapping

## 🗄️ Data Storage

### Supabase Tables
- `customer_interactions`: All customer touchpoints
- `orders`: Order records and status
- `sku_mappings`: Customer SKU to internal SKU mappings
- Vector embeddings for semantic search

### Graphiti Temporal Knowledge
- Episodic memories of customer interactions
- Temporal relationships and patterns
- Context-aware knowledge retrieval

## 🔧 Configuration

### Core Settings (.env)
```bash
# AI Engine
MISTRAL_API_KEY=your_key_here
MISTRAL_MODEL=mistral-large-latest

# Database
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your_key_here

# ERP Integration
MYOB_EXO_API_URL=http://your-server:8888
MYOB_EXO_API_KEY=your_key_here

# Features
ENABLE_EMAIL_MONITORING=true
ENABLE_TEMPORAL_KNOWLEDGE=true
EMAIL_CHECK_INTERVAL=300
```

## 🧪 Testing

Run the test suite to verify all components:

```bash
python test_simple_framework.py
```

Tests include:
- ✅ Environment configuration
- ✅ Supabase connection
- ✅ Mistral AI API
- ✅ Graphiti temporal knowledge
- ✅ Agent initialization
- ✅ System startup

## 🔄 Workflow Example

1. **Email Arrives**: Gmail API detects new customer email
2. **AI Analysis**: Mistral AI analyzes intent and extracts key information
3. **Context Retrieval**: System gets customer history from Supabase + Graphiti
4. **Response Generation**: Mistral AI generates contextual response
5. **ERP Integration**: If needed, MYOB agent handles order/product queries
6. **Memory Update**: Interaction stored in Supabase + Graphiti for future context

## 🚀 Deployment

### Local Development
```bash
python simple_main.py
```

### Production
- Use process managers like `systemd` or `supervisor`
- Configure proper logging and monitoring
- Set up database backups
- Use environment-specific configurations

## 📊 Monitoring

The system provides:
- Structured logging to files and console
- Health check endpoints
- Agent performance statistics
- API usage tracking

## 🔒 Security

- API keys stored in environment variables
- JWT tokens for authentication
- Supabase Row Level Security (RLS)
- Input validation and sanitization

## 🤝 Contributing

This is a simplified, focused framework. Key principles:
- **Simplicity**: Minimal dependencies, clear code
- **AI-First**: Mistral AI at the core of all operations
- **Memory**: Persistent context through Supabase + Graphiti
- **Integration**: Direct API calls, no unnecessary abstractions

## 📝 License

MIT License - see LICENSE file for details.
