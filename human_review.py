#!/usr/bin/env python3
"""
Human-in-the-Loop Review System
CLI interface for reviewing AI-processed emails and approving responses
"""

import asyncio
import json
import os
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.columns import Columns
from rich.text import Text
from rich.prompt import Prompt, Confirm
from rich.syntax import Syntax
from rich.layout import Layout
from rich.markdown import Markdown

from dotenv import load_dotenv
from supabase import create_client

# Load environment
load_dotenv()

console = Console()

class HumanReviewSystem:
    """Human-in-the-loop review system for email processing."""
    
    def __init__(self):
        """Initialize the review system."""
        self.supabase = create_client(
            os.getenv("SUPABASE_URL"),
            os.getenv("SUPABASE_SERVICE_KEY")
        )
        self.setup_review_tables()
    
    def setup_review_tables(self):
        """Setup additional tables for human review workflow."""
        # Note: In production, these would be created via Supabase migrations
        # For now, we'll work with the existing customer_interactions table
        # and add review status to metadata
        pass
    
    def get_pending_reviews(self) -> List[Dict[str, Any]]:
        """Get emails pending human review."""
        try:
            # Get recent emails that haven't been reviewed
            response = self.supabase.table("customer_interactions").select("*").eq(
                "interaction_type", "email"
            ).order("created_at", desc=True).limit(20).execute()
            
            emails = response.data or []
            
            # Filter for emails that need review (high urgency or no review status)
            pending = []
            for email in emails:
                metadata = email.get('metadata', {})
                analysis = metadata.get('analysis', {})
                review_status = metadata.get('review_status', 'pending')
                urgency = analysis.get('urgency', 'low')
                
                # Needs review if: pending status OR high/urgent priority
                if review_status == 'pending' or urgency.lower() in ['high', 'urgent']:
                    pending.append(email)
            
            return pending
            
        except Exception as e:
            console.print(f"[red]Error fetching pending reviews: {e}[/red]")
            return []
    
    def show_review_queue(self):
        """Display the review queue with rich formatting."""
        pending = self.get_pending_reviews()
        
        if not pending:
            console.print("[green]✅ No emails pending review![/green]")
            return
        
        # Create review queue table
        table = Table(title=f"📋 Review Queue ({len(pending)} emails)", show_header=True, header_style="bold magenta")
        table.add_column("#", style="dim", width=3)
        table.add_column("Time", style="dim", width=12)
        table.add_column("From", style="cyan", width=30)
        table.add_column("Subject", style="green", width=35)
        table.add_column("Urgency", style="red", width=10)
        table.add_column("Status", style="yellow", width=12)
        
        for i, email in enumerate(pending):
            metadata = email.get('metadata', {})
            analysis = metadata.get('analysis', {})
            
            # Format timestamp
            created_at = datetime.fromisoformat(email['created_at'].replace('Z', '+00:00'))
            time_str = created_at.strftime("%H:%M:%S")
            
            # Extract data
            urgency = analysis.get('urgency', 'low')
            subject = metadata.get('subject', 'No subject')[:32] + "..." if len(metadata.get('subject', '')) > 35 else metadata.get('subject', 'No subject')
            review_status = metadata.get('review_status', 'pending')
            
            # Color code urgency
            urgency_color = {
                'urgent': '[red]URGENT[/red]',
                'high': '[orange1]HIGH[/orange1]',
                'medium': '[yellow]MEDIUM[/yellow]',
                'low': '[green]LOW[/green]'
            }.get(urgency.lower(), urgency)
            
            # Color code status
            status_color = {
                'pending': '[yellow]PENDING[/yellow]',
                'approved': '[green]APPROVED[/green]',
                'rejected': '[red]REJECTED[/red]',
                'escalated': '[orange1]ESCALATED[/orange1]'
            }.get(review_status, review_status)
            
            table.add_row(
                str(i),
                time_str,
                email['customer_email'],
                subject,
                urgency_color,
                status_color
            )
        
        console.print(table)
    
    def review_email(self, email_index: int):
        """Review a specific email and make decisions."""
        pending = self.get_pending_reviews()
        
        if email_index >= len(pending):
            console.print("[red]Invalid email index[/red]")
            return
        
        email = pending[email_index]
        metadata = email.get('metadata', {})
        analysis = metadata.get('analysis', {})
        
        # Create detailed review layout
        layout = Layout()
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="body"),
            Layout(name="footer", size=8)
        )
        
        # Header
        header_text = Text(f"📧 Email Review - {email['customer_email']}", style="bold blue")
        layout["header"].update(Panel(header_text, style="blue"))
        
        # Body - split into left and right
        layout["body"].split_row(
            Layout(name="left"),
            Layout(name="right")
        )
        
        # Left panel - Email content
        email_content = f"""**From:** {email['customer_email']}
**Subject:** {metadata.get('subject', 'No subject')}
**Time:** {email['created_at']}

**Content:**
{email.get('content', 'No content')[:800]}{'...' if len(email.get('content', '')) > 800 else ''}"""
        
        layout["left"].update(Panel(Markdown(email_content), title="📨 Email Content", border_style="green"))
        
        # Right panel - Enhanced AI Analysis
        confidence = analysis.get('confidence', 'unknown')
        flags = analysis.get('flags', [])
        entities = analysis.get('entities', {})
        thread_relevance = analysis.get('thread_relevance', 'No thread context')
        review_reason = analysis.get('review_reason', 'Auto-flagged')

        analysis_content = f"""**🎯 Intent:** {analysis.get('intent', 'unknown')}
**⚡ Urgency:** {analysis.get('urgency', 'low')}
**🎯 Confidence:** {confidence}
**🚩 Flags:** {', '.join(flags) if flags else 'None'}
**⚠️ Review Reason:** {review_reason}

**📋 Summary:**
{analysis.get('summary', 'No summary available')}

**🔗 Thread Context:**
{thread_relevance}

**📊 Entities Found:**
{', '.join([f"{k}: {v}" for k, v in entities.items()]) if entities else 'None'}

**💡 Suggested Action:**
{analysis.get('suggested_action', 'No action suggested')}

**📝 Topics:**
{', '.join(analysis.get('topics', [])) if analysis.get('topics') else 'None'}"""
        
        layout["right"].update(Panel(Markdown(analysis_content), title="🤖 AI Analysis", border_style="yellow"))
        
        # Footer - Action buttons
        footer_text = """**Actions:**
[1] ✅ Approve & Generate Response
[2] ❌ Reject (Mark as spam/invalid)
[3] 🔄 Request Human Response
[4] ⚠️ Escalate to Manager
[5] 📝 Add Notes
[6] ⬅️ Back to Queue"""
        
        layout["footer"].update(Panel(Markdown(footer_text), title="🎯 Review Actions", border_style="cyan"))
        
        console.clear()
        console.print(layout)
        
        # Get user decision
        action = Prompt.ask(
            "Choose action",
            choices=["1", "2", "3", "4", "5", "6"],
            default="1"
        )
        
        if action == "1":
            self.approve_email(email)
        elif action == "2":
            self.reject_email(email)
        elif action == "3":
            self.request_human_response(email)
        elif action == "4":
            self.escalate_email(email)
        elif action == "5":
            self.add_notes(email)
        elif action == "6":
            return
    
    def approve_email(self, email: Dict[str, Any]):
        """Approve email and generate response."""
        console.print("[green]✅ Email approved! Generating response...[/green]")
        
        # Update review status
        self.update_review_status(email, 'approved', 'Email approved for auto-response')
        
        # TODO: Trigger response generation
        console.print("[yellow]📤 Response generation would be triggered here[/yellow]")
        input("Press Enter to continue...")
    
    def reject_email(self, email: Dict[str, Any]):
        """Reject email (mark as spam/invalid)."""
        reason = Prompt.ask("Rejection reason", default="Spam/Invalid")
        
        console.print(f"[red]❌ Email rejected: {reason}[/red]")
        self.update_review_status(email, 'rejected', f'Rejected: {reason}')
        input("Press Enter to continue...")
    
    def request_human_response(self, email: Dict[str, Any]):
        """Request human-written response."""
        console.print("[yellow]📝 Requesting human response...[/yellow]")
        
        response_text = Prompt.ask("Enter response (or 'skip' to queue for later)")
        
        if response_text.lower() != 'skip':
            # TODO: Store human response and send email
            console.print("[green]✅ Human response saved![/green]")
            self.update_review_status(email, 'human_response', f'Human response: {response_text[:100]}...')
        else:
            self.update_review_status(email, 'human_pending', 'Queued for human response')
        
        input("Press Enter to continue...")
    
    def escalate_email(self, email: Dict[str, Any]):
        """Escalate email to manager."""
        reason = Prompt.ask("Escalation reason", default="Complex issue requiring manager review")
        
        console.print(f"[orange1]⚠️ Email escalated: {reason}[/orange1]")
        self.update_review_status(email, 'escalated', f'Escalated: {reason}')
        input("Press Enter to continue...")
    
    def add_notes(self, email: Dict[str, Any]):
        """Add notes to email."""
        notes = Prompt.ask("Add notes")
        
        # Add notes to metadata
        metadata = email.get('metadata', {})
        metadata['review_notes'] = notes
        
        # Update in database
        self.supabase.table("customer_interactions").update({
            "metadata": metadata
        }).eq("id", email['id']).execute()
        
        console.print("[green]✅ Notes added![/green]")
        input("Press Enter to continue...")
    
    def update_review_status(self, email: Dict[str, Any], status: str, notes: str):
        """Update review status in database."""
        try:
            metadata = email.get('metadata', {})
            metadata['review_status'] = status
            metadata['review_notes'] = notes
            metadata['reviewed_at'] = datetime.now(timezone.utc).isoformat()
            
            self.supabase.table("customer_interactions").update({
                "metadata": metadata
            }).eq("id", email['id']).execute()
            
        except Exception as e:
            console.print(f"[red]Error updating review status: {e}[/red]")

def main():
    """Main review interface."""
    review_system = HumanReviewSystem()
    
    while True:
        console.clear()
        console.print(Panel.fit("👤 Human Review System", style="bold blue"))
        console.print()
        
        # Show menu
        menu_table = Table(show_header=False, box=None)
        menu_table.add_column("Option", style="cyan")
        menu_table.add_column("Description", style="white")
        
        menu_table.add_row("1", "📋 View Review Queue")
        menu_table.add_row("2", "🔍 Review Specific Email")
        menu_table.add_row("3", "📊 Review Statistics")
        menu_table.add_row("4", "🔄 Refresh")
        menu_table.add_row("q", "❌ Quit")
        
        console.print(menu_table)
        console.print()
        
        choice = Prompt.ask("Select option", choices=["1", "2", "3", "4", "q"], default="1")
        
        if choice == "1":
            console.clear()
            review_system.show_review_queue()
            input("\nPress Enter to continue...")
            
        elif choice == "2":
            console.clear()
            review_system.show_review_queue()
            console.print()
            try:
                index = int(Prompt.ask("Enter email index to review", default="0"))
                review_system.review_email(index)
            except ValueError:
                console.print("[red]Invalid index[/red]")
                input("Press Enter to continue...")
                
        elif choice == "3":
            console.clear()
            # TODO: Show review statistics
            console.print("[yellow]📊 Review statistics coming soon...[/yellow]")
            input("Press Enter to continue...")
            
        elif choice == "4":
            continue
            
        elif choice == "q":
            console.print("[yellow]Goodbye![/yellow]")
            break

if __name__ == "__main__":
    main()
