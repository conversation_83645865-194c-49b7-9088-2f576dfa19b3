# Multi-Agent Sales Support System

A comprehensive multi-agent system for sales support integrating Mistral AI, PydanticAI, LangChain, Gmail API, PyMuPDF, MYOB EXO ERP, Supabase with pgvector, and Graphiti/FalkorDB.

## System Overview

This system implements four main agents working together to provide automated sales support:

1. **Email Monitoring Agent** - Monitors Gmail for customer emails and processes PDF attachments
2. **Customer Support Agent** - Handles customer queries with context-aware responses
3. **Order Processing Agent** - Manages order workflows through MYOB EXO ERP
4. **Purchasing Agent** - Monitors inventory and handles automated restocking

## Architecture

### Core Components

- **Mistral AI**: LLM provider for agent reasoning and natural language processing
- **PydanticAI**: Type-safe agent framework for structured responses
- **LangChain**: Agent orchestration and tool integration
- **Gmail API**: Email monitoring and processing
- **PyMuPDF**: PDF document processing for order forms
- **MYOB EXO ERP**: 68+ API endpoints for order and inventory management
- **Supabase + pgvector**: Vector database for embeddings and memory storage
- **Graphiti/FalkorDB**: Knowledge graph for customer-order-product relationships

### Key Features

- **Customer-specific SKU mapping**: Transparent mapping between customer SKUs and internal SKUs
- **Model Context Protocol (MCP)**: FastAPI server wrapping all MYOB EXO endpoints
- **Vector-based memory**: Semantic search of customer interaction history
- **Knowledge graphs**: Temporal relationships between customers, orders, and products
- **PDF processing**: Automatic extraction of order information from email attachments
- **Asynchronous processing**: Non-blocking email monitoring and API calls

## Project Structure

```
sales-support-system/
├── agents/                     # Agent implementations
│   ├── __init__.py
│   ├── email_monitor.py       # Gmail monitoring and PDF processing
│   ├── customer_support.py    # Customer query handling
│   ├── order_processing.py    # Order management
│   └── purchasing.py          # Inventory monitoring
├── tools/                     # API integration tools
│   ├── __init__.py
│   ├── gmail_tools.py        # Gmail API integration
│   ├── myob_tools.py         # MYOB EXO API integration
│   ├── pdf_tools.py          # PDF processing tools
│   └── erp_tools.py          # ERP system tools
├── database/                  # Database schemas and operations
│   ├── __init__.py
│   ├── supabase_setup.py     # Supabase configuration
│   ├── schemas.py            # Database schemas
│   └── operations.py         # Database operations
├── knowledge_graph/           # Knowledge graph implementation
│   ├── __init__.py
│   ├── graphiti_setup.py     # Graphiti/FalkorDB setup
│   └── relationships.py      # Graph relationship management
├── tools/                     # LangChain tools and utilities
│   ├── __init__.py
│   ├── gmail_tools.py        # Gmail API tools
│   ├── pdf_tools.py          # PDF processing tools
│   └── erp_tools.py          # ERP integration tools
├── config/                    # Configuration management
│   ├── __init__.py
│   ├── settings.py           # Application settings
│   └── environment.py        # Environment configuration
├── tests/                     # Test suite
│   ├── __init__.py
│   ├── test_agents.py        # Agent tests
│   ├── test_mcp_server.py    # MCP server tests
│   └── test_integration.py   # Integration tests
├── docs/                      # Documentation
│   ├── api.md                # API documentation
│   ├── deployment.md         # Deployment guide
│   └── configuration.md      # Configuration guide
├── scripts/                   # Utility scripts
│   ├── setup_database.py     # Database initialization
│   └── populate_sku_mappings.py  # SKU mapping setup
├── requirements.txt           # Python dependencies
├── .env.example              # Environment variables template
├── docker-compose.yml        # Docker setup for FalkorDB
└── main.py                   # Application entry point
```

## Quick Start

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd sales-support-system
   pip install -r requirements.txt
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

3. **Database Setup**
   ```bash
   python scripts/setup_database.py
   ```

4. **Start Services**
   ```bash
   # Start FalkorDB
   docker-compose up -d falkordb
   
   # Start MCP Server
   uvicorn mcp_server.main:app --host 0.0.0.0 --port 8000
   
   # Run the main application
   python main.py
   ```

## Configuration

See `docs/configuration.md` for detailed configuration instructions.

## API Documentation

See `docs/api.md` for complete API documentation.

## Deployment

See `docs/deployment.md` for production deployment guidelines.

## License

MIT License - see LICENSE file for details.
