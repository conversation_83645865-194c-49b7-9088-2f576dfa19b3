"""
Graphiti and FalkorD<PERSON> setup for knowledge graph functionality.
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import json

try:
    import falkordb
    from graphiti_core import Graphiti
    from graphiti_core.nodes import EntityNode, EpisodicNode
    from graphiti_core.edges import Edge
except ImportError as e:
    logger = logging.getLogger(__name__)
    logger.warning(f"Knowledge graph dependencies not available: {e}")
    falkordb = None
    Graphiti = None

from config.settings import get_settings

logger = logging.getLogger(__name__)


class KnowledgeGraphError(Exception):
    """Custom exception for knowledge graph operations."""
    pass


class GraphitiManager:
    """Manages Graphiti knowledge graph operations."""
    
    def __init__(self):
        """Initialize Graphiti manager."""
        self.settings = get_settings()
        self.graphiti: Optional[Graphiti] = None
        self.falkor_client: Optional[falkordb.FalkorDB] = None
        
    async def initialize(self) -> None:
        """Initialize Graphiti and FalkorDB connections."""
        try:
            if not Graphiti:
                raise KnowledgeGraphError("Graphiti not available. Please install graphiti-core.")
            
            # Initialize FalkorDB connection
            await self._initialize_falkordb()
            
            # Initialize Graphiti
            await self._initialize_graphiti()
            
            logger.info("Knowledge graph initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize knowledge graph: {e}")
            raise KnowledgeGraphError(f"Initialization failed: {e}")
    
    async def _initialize_falkordb(self) -> None:
        """Initialize FalkorDB connection."""
        try:
            if not falkordb:
                raise KnowledgeGraphError("FalkorDB not available. Please install falkordb.")
            
            # Create FalkorDB connection
            self.falkor_client = falkordb.FalkorDB(
                host=self.settings.falkordb_host,
                port=self.settings.falkordb_port,
                password=self.settings.falkordb_password,
                ssl=self.settings.falkordb_ssl
            )
            
            # Test connection
            graph = self.falkor_client.select_graph(self.settings.falkordb_database)
            result = graph.query("RETURN 1")
            
            logger.info("FalkorDB connection established")
            
        except Exception as e:
            logger.error(f"Failed to initialize FalkorDB: {e}")
            raise
    
    async def _initialize_graphiti(self) -> None:
        """Initialize Graphiti knowledge graph."""
        try:
            # Initialize Graphiti with FalkorDB backend
            self.graphiti = Graphiti(
                graph_name=self.settings.falkordb_database,
                falkordb_client=self.falkor_client
            )
            
            # Initialize the graph schema
            await self._setup_graph_schema()
            
            logger.info("Graphiti knowledge graph initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Graphiti: {e}")
            raise
    
    async def _setup_graph_schema(self) -> None:
        """Set up the knowledge graph schema."""
        try:
            # Define node types
            node_types = [
                "Customer",
                "Order", 
                "Product",
                "SKU",
                "Supplier",
                "Interaction",
                "Agent"
            ]
            
            # Define relationship types
            relationship_types = [
                "PLACED_ORDER",
                "CONTAINS_PRODUCT",
                "HAS_SKU",
                "SUPPLIED_BY",
                "INTERACTED_WITH",
                "PROCESSED_BY",
                "RELATED_TO",
                "FOLLOWED_BY"
            ]
            
            # Create schema in FalkorDB
            graph = self.falkor_client.select_graph(self.settings.falkordb_database)
            
            # Create indexes for better performance
            for node_type in node_types:
                try:
                    graph.query(f"CREATE INDEX FOR (n:{node_type}) ON (n.id)")
                    graph.query(f"CREATE INDEX FOR (n:{node_type}) ON (n.created_at)")
                except Exception as e:
                    # Index might already exist
                    logger.debug(f"Index creation for {node_type} failed (might already exist): {e}")
            
            logger.info("Graph schema setup complete")
            
        except Exception as e:
            logger.error(f"Error setting up graph schema: {e}")
            raise
    
    async def add_customer_node(self, customer_email: str, customer_data: Dict[str, Any]) -> str:
        """Add a customer node to the knowledge graph."""
        try:
            node_id = f"customer_{customer_email.replace('@', '_').replace('.', '_')}"
            
            # Create customer entity
            customer_entity = EntityNode(
                name=customer_data.get('name', customer_email),
                entity_type="Customer",
                properties={
                    "email": customer_email,
                    "id": node_id,
                    "created_at": datetime.utcnow().isoformat(),
                    **customer_data
                }
            )
            
            # Add to Graphiti
            await self.graphiti.add_node(customer_entity)
            
            logger.debug(f"Added customer node: {node_id}")
            return node_id
            
        except Exception as e:
            logger.error(f"Error adding customer node: {e}")
            raise KnowledgeGraphError(f"Failed to add customer node: {e}")
    
    async def add_order_node(self, order_id: str, order_data: Dict[str, Any]) -> str:
        """Add an order node to the knowledge graph."""
        try:
            node_id = f"order_{order_id}"
            
            # Create order entity
            order_entity = EntityNode(
                name=f"Order {order_id}",
                entity_type="Order",
                properties={
                    "order_id": order_id,
                    "id": node_id,
                    "created_at": datetime.utcnow().isoformat(),
                    **order_data
                }
            )
            
            # Add to Graphiti
            await self.graphiti.add_node(order_entity)
            
            logger.debug(f"Added order node: {node_id}")
            return node_id
            
        except Exception as e:
            logger.error(f"Error adding order node: {e}")
            raise KnowledgeGraphError(f"Failed to add order node: {e}")
    
    async def add_product_node(self, sku: str, product_data: Dict[str, Any]) -> str:
        """Add a product node to the knowledge graph."""
        try:
            node_id = f"product_{sku}"
            
            # Create product entity
            product_entity = EntityNode(
                name=product_data.get('name', f"Product {sku}"),
                entity_type="Product",
                properties={
                    "sku": sku,
                    "id": node_id,
                    "created_at": datetime.utcnow().isoformat(),
                    **product_data
                }
            )
            
            # Add to Graphiti
            await self.graphiti.add_node(product_entity)
            
            logger.debug(f"Added product node: {node_id}")
            return node_id
            
        except Exception as e:
            logger.error(f"Error adding product node: {e}")
            raise KnowledgeGraphError(f"Failed to add product node: {e}")
    
    async def add_interaction_node(self, interaction_id: str, interaction_data: Dict[str, Any]) -> str:
        """Add an interaction node to the knowledge graph."""
        try:
            node_id = f"interaction_{interaction_id}"
            
            # Create interaction episode
            interaction_episode = EpisodeNode(
                name=f"Interaction {interaction_id}",
                episode_type="Interaction",
                properties={
                    "interaction_id": interaction_id,
                    "id": node_id,
                    "created_at": datetime.utcnow().isoformat(),
                    **interaction_data
                }
            )
            
            # Add to Graphiti
            await self.graphiti.add_node(interaction_episode)
            
            logger.debug(f"Added interaction node: {node_id}")
            return node_id
            
        except Exception as e:
            logger.error(f"Error adding interaction node: {e}")
            raise KnowledgeGraphError(f"Failed to add interaction node: {e}")
    
    async def create_relationship(
        self,
        source_id: str,
        target_id: str,
        relationship_type: str,
        properties: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a relationship between two nodes."""
        try:
            edge_properties = {
                "created_at": datetime.utcnow().isoformat(),
                **(properties or {})
            }
            
            # Create edge
            edge = Edge(
                source_node_id=source_id,
                target_node_id=target_id,
                edge_type=relationship_type,
                properties=edge_properties
            )
            
            # Add to Graphiti
            edge_id = await self.graphiti.add_edge(edge)
            
            logger.debug(f"Created relationship: {source_id} -{relationship_type}-> {target_id}")
            return edge_id
            
        except Exception as e:
            logger.error(f"Error creating relationship: {e}")
            raise KnowledgeGraphError(f"Failed to create relationship: {e}")
    
    async def record_customer_order(
        self,
        customer_email: str,
        order_id: str,
        order_data: Dict[str, Any],
        products: List[Dict[str, Any]]
    ) -> None:
        """Record a complete customer order in the knowledge graph."""
        try:
            # Add customer node
            customer_node_id = await self.add_customer_node(
                customer_email,
                {"name": order_data.get("customer_name", customer_email)}
            )
            
            # Add order node
            order_node_id = await self.add_order_node(order_id, order_data)
            
            # Create customer-order relationship
            await self.create_relationship(
                customer_node_id,
                order_node_id,
                "PLACED_ORDER",
                {
                    "order_date": order_data.get("order_date", datetime.utcnow().isoformat()),
                    "total_amount": order_data.get("total_amount")
                }
            )
            
            # Add products and relationships
            for product in products:
                sku = product.get("sku")
                if sku:
                    # Add product node
                    product_node_id = await self.add_product_node(sku, product)
                    
                    # Create order-product relationship
                    await self.create_relationship(
                        order_node_id,
                        product_node_id,
                        "CONTAINS_PRODUCT",
                        {
                            "quantity": product.get("quantity"),
                            "unit_price": product.get("unit_price")
                        }
                    )
            
            logger.info(f"Recorded customer order in knowledge graph: {order_id}")
            
        except Exception as e:
            logger.error(f"Error recording customer order: {e}")
            raise KnowledgeGraphError(f"Failed to record customer order: {e}")
    
    async def record_customer_interaction(
        self,
        customer_email: str,
        interaction_data: Dict[str, Any]
    ) -> None:
        """Record a customer interaction in the knowledge graph."""
        try:
            interaction_id = interaction_data.get("id", f"int_{datetime.utcnow().timestamp()}")
            
            # Add customer node (if not exists)
            customer_node_id = await self.add_customer_node(customer_email, {})
            
            # Add interaction node
            interaction_node_id = await self.add_interaction_node(interaction_id, interaction_data)
            
            # Create customer-interaction relationship
            await self.create_relationship(
                customer_node_id,
                interaction_node_id,
                "INTERACTED_WITH",
                {
                    "interaction_type": interaction_data.get("interaction_type"),
                    "timestamp": interaction_data.get("created_at", datetime.utcnow().isoformat())
                }
            )
            
            logger.debug(f"Recorded customer interaction: {interaction_id}")
            
        except Exception as e:
            logger.error(f"Error recording customer interaction: {e}")
            raise KnowledgeGraphError(f"Failed to record customer interaction: {e}")
    
    async def query_customer_relationships(self, customer_email: str) -> List[Dict[str, Any]]:
        """Query all relationships for a customer."""
        try:
            customer_node_id = f"customer_{customer_email.replace('@', '_').replace('.', '_')}"
            
            graph = self.falkor_client.select_graph(self.settings.falkordb_database)
            
            # Query customer relationships
            query = f"""
            MATCH (c:Customer {{id: '{customer_node_id}'}})-[r]->(n)
            RETURN c, r, n
            ORDER BY r.created_at DESC
            LIMIT 50
            """
            
            result = graph.query(query)
            
            relationships = []
            for record in result.result_set:
                relationships.append({
                    "customer": record[0].properties,
                    "relationship": {
                        "type": record[1].relation,
                        "properties": record[1].properties
                    },
                    "target": record[2].properties
                })
            
            return relationships
            
        except Exception as e:
            logger.error(f"Error querying customer relationships: {e}")
            return []
    
    async def find_similar_customers(self, customer_email: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Find customers with similar order patterns."""
        try:
            customer_node_id = f"customer_{customer_email.replace('@', '_').replace('.', '_')}"
            
            graph = self.falkor_client.select_graph(self.settings.falkordb_database)
            
            # Find customers who ordered similar products
            query = f"""
            MATCH (c1:Customer {{id: '{customer_node_id}'}})-[:PLACED_ORDER]->(o1:Order)-[:CONTAINS_PRODUCT]->(p:Product)
            MATCH (c2:Customer)-[:PLACED_ORDER]->(o2:Order)-[:CONTAINS_PRODUCT]->(p)
            WHERE c1 <> c2
            WITH c2, COUNT(DISTINCT p) as common_products
            ORDER BY common_products DESC
            LIMIT {limit}
            RETURN c2, common_products
            """
            
            result = graph.query(query)
            
            similar_customers = []
            for record in result.result_set:
                similar_customers.append({
                    "customer": record[0].properties,
                    "common_products": record[1]
                })
            
            return similar_customers
            
        except Exception as e:
            logger.error(f"Error finding similar customers: {e}")
            return []
    
    async def get_product_recommendations(self, customer_email: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Get product recommendations based on similar customers."""
        try:
            customer_node_id = f"customer_{customer_email.replace('@', '_').replace('.', '_')}"
            
            graph = self.falkor_client.select_graph(self.settings.falkordb_database)
            
            # Find products ordered by similar customers but not by this customer
            query = f"""
            MATCH (c1:Customer {{id: '{customer_node_id}'}})-[:PLACED_ORDER]->(o1:Order)-[:CONTAINS_PRODUCT]->(p1:Product)
            MATCH (c2:Customer)-[:PLACED_ORDER]->(o2:Order)-[:CONTAINS_PRODUCT]->(p1)
            MATCH (c2)-[:PLACED_ORDER]->(o3:Order)-[:CONTAINS_PRODUCT]->(p2:Product)
            WHERE c1 <> c2 AND NOT (c1)-[:PLACED_ORDER]->(:Order)-[:CONTAINS_PRODUCT]->(p2)
            WITH p2, COUNT(DISTINCT c2) as popularity
            ORDER BY popularity DESC
            LIMIT {limit}
            RETURN p2, popularity
            """
            
            result = graph.query(query)
            
            recommendations = []
            for record in result.result_set:
                recommendations.append({
                    "product": record[0].properties,
                    "popularity": record[1]
                })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error getting product recommendations: {e}")
            return []
    
    async def get_graph_stats(self) -> Dict[str, Any]:
        """Get knowledge graph statistics."""
        try:
            graph = self.falkor_client.select_graph(self.settings.falkordb_database)
            
            # Count nodes by type
            node_counts = {}
            node_types = ["Customer", "Order", "Product", "Interaction"]
            
            for node_type in node_types:
                result = graph.query(f"MATCH (n:{node_type}) RETURN COUNT(n)")
                node_counts[node_type] = result.result_set[0][0] if result.result_set else 0
            
            # Count relationships
            result = graph.query("MATCH ()-[r]->() RETURN COUNT(r)")
            total_relationships = result.result_set[0][0] if result.result_set else 0
            
            return {
                "node_counts": node_counts,
                "total_relationships": total_relationships,
                "graph_name": self.settings.falkordb_database,
                "connected": self.falkor_client is not None
            }
            
        except Exception as e:
            logger.error(f"Error getting graph stats: {e}")
            return {"error": str(e)}
    
    async def close(self) -> None:
        """Close knowledge graph connections."""
        try:
            if self.falkor_client:
                self.falkor_client.close()
                self.falkor_client = None
            
            self.graphiti = None
            logger.info("Knowledge graph connections closed")
            
        except Exception as e:
            logger.error(f"Error closing knowledge graph connections: {e}")


# Global Graphiti manager instance
_graphiti_manager: Optional[GraphitiManager] = None


async def initialize_graphiti() -> GraphitiManager:
    """Initialize the global Graphiti manager."""
    global _graphiti_manager
    
    if _graphiti_manager is None:
        _graphiti_manager = GraphitiManager()
        await _graphiti_manager.initialize()
    
    return _graphiti_manager


async def get_graphiti_manager() -> Optional[GraphitiManager]:
    """Get the global Graphiti manager instance."""
    global _graphiti_manager
    return _graphiti_manager


async def record_order_in_graph(
    customer_email: str,
    order_id: str,
    order_data: Dict[str, Any],
    products: List[Dict[str, Any]]
) -> None:
    """Record an order in the knowledge graph."""
    manager = await initialize_graphiti()
    await manager.record_customer_order(customer_email, order_id, order_data, products)


async def record_interaction_in_graph(
    customer_email: str,
    interaction_data: Dict[str, Any]
) -> None:
    """Record an interaction in the knowledge graph."""
    manager = await initialize_graphiti()
    await manager.record_customer_interaction(customer_email, interaction_data)


async def get_customer_insights(customer_email: str) -> Dict[str, Any]:
    """Get customer insights from the knowledge graph."""
    manager = await initialize_graphiti()
    
    relationships = await manager.query_customer_relationships(customer_email)
    similar_customers = await manager.find_similar_customers(customer_email)
    recommendations = await manager.get_product_recommendations(customer_email)
    
    return {
        "relationships": relationships,
        "similar_customers": similar_customers,
        "product_recommendations": recommendations
    }


async def get_knowledge_graph_stats() -> Dict[str, Any]:
    """Get knowledge graph statistics."""
    manager = await initialize_graphiti()
    return await manager.get_graph_stats()
