"""
Test runner for the multi-agent sales support system.
"""

import asyncio
import sys
import subprocess
from pathlib import Path
from typing import List, Dict, Any
import pytest


def run_unit_tests() -> int:
    """Run unit tests."""
    print("Running unit tests...")
    
    args = [
        "-v",
        "--tb=short",
        "-m", "unit",
        "--cov=.",
        "--cov-report=term-missing",
        "--cov-report=html:htmlcov",
        "tests/"
    ]
    
    return pytest.main(args)


def run_integration_tests() -> int:
    """Run integration tests."""
    print("Running integration tests...")
    
    args = [
        "-v",
        "--tb=short",
        "-m", "integration",
        "--cov=.",
        "--cov-report=term-missing",
        "tests/"
    ]
    
    return pytest.main(args)


def run_all_tests() -> int:
    """Run all tests."""
    print("Running all tests...")
    
    args = [
        "-v",
        "--tb=short",
        "--cov=.",
        "--cov-report=term-missing",
        "--cov-report=html:htmlcov",
        "tests/"
    ]
    
    return pytest.main(args)


def run_specific_test(test_path: str) -> int:
    """Run a specific test file or test function."""
    print(f"Running specific test: {test_path}")
    
    args = [
        "-v",
        "--tb=short",
        test_path
    ]
    
    return pytest.main(args)


def validate_test_environment() -> bool:
    """Validate that the test environment is properly set up."""
    print("Validating test environment...")
    
    # Check if required test files exist
    test_files = [
        "tests/conftest.py",
        "tests/test_database_operations.py",
        "tests/test_mcp_server.py",
        "tests/test_agents.py",
        "tests/test_integration.py"
    ]
    
    missing_files = []
    for test_file in test_files:
        if not Path(test_file).exists():
            missing_files.append(test_file)
    
    if missing_files:
        print(f"Missing test files: {missing_files}")
        return False
    
    # Check if pytest is available
    try:
        import pytest
        print(f"pytest version: {pytest.__version__}")
    except ImportError:
        print("pytest not available")
        return False
    
    # Check if coverage is available
    try:
        import coverage
        print(f"coverage version: {coverage.__version__}")
    except ImportError:
        print("coverage not available (optional)")
    
    print("Test environment validation complete")
    return True


def main():
    """Main test runner function."""
    if len(sys.argv) < 2:
        print("Usage: python run_tests.py [unit|integration|all|specific <test_path>|validate]")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == "validate":
        if validate_test_environment():
            print("Test environment is valid")
            sys.exit(0)
        else:
            print("Test environment validation failed")
            sys.exit(1)
    
    elif command == "unit":
        exit_code = run_unit_tests()
        sys.exit(exit_code)
    
    elif command == "integration":
        exit_code = run_integration_tests()
        sys.exit(exit_code)
    
    elif command == "all":
        exit_code = run_all_tests()
        sys.exit(exit_code)
    
    elif command == "specific":
        if len(sys.argv) < 3:
            print("Usage: python run_tests.py specific <test_path>")
            sys.exit(1)
        
        test_path = sys.argv[2]
        exit_code = run_specific_test(test_path)
        sys.exit(exit_code)
    
    else:
        print(f"Unknown command: {command}")
        print("Available commands: unit, integration, all, specific, validate")
        sys.exit(1)


if __name__ == "__main__":
    main()
