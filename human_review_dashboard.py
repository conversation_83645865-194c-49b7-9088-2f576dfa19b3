#!/usr/bin/env python3
"""
Consolidated Human Review Dashboard
Single interface for all HITL functionality with robust error handling
"""

import asyncio
import json
import os
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional

import httpx
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.columns import Columns
from rich.text import Text
from rich.prompt import Prompt, Confirm
from rich.syntax import Syntax
from rich.layout import Layout
from rich.markdown import Markdown
from rich.progress import Progress, SpinnerColumn, TextColumn

from dotenv import load_dotenv
from supabase import create_client

# Load environment
load_dotenv()

console = Console()

class HumanReviewDashboard:
    """Consolidated Human-in-the-Loop review system."""
    
    def __init__(self):
        """Initialize the dashboard."""
        self.supabase = create_client(
            os.getenv("SUPABASE_URL"),
            os.getenv("SUPABASE_SERVICE_KEY")
        )
        self.pending_reviews = {}
        
        # Internal senders for shared inbox handling
        self.internal_senders = {
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        }
        
        console.print("[bold green]🚀 Human Review Dashboard Initialized[/bold green]")

    def _extract_original_sender(self, email: Dict[str, Any], metadata: Dict[str, Any]) -> str:
        """Extract sender from X-Original-Sender or Reply-To header for shared inbox handling."""
        headers = metadata.get('headers', {})
        if isinstance(headers, dict):
            # First try X-Original-Sender (case insensitive)
            original_sender = (headers.get('X-Original-Sender') or
                             headers.get('x-original-sender') or
                             headers.get('X-original-sender'))
            if original_sender:
                return self._clean_email_address(original_sender)

            # Then try Reply-To header
            reply_to = (headers.get('Reply-To') or
                       headers.get('reply-to') or
                       headers.get('Reply-to'))
            if reply_to:
                return self._clean_email_address(reply_to)

        # Fallback to customer_email
        sender = email.get('customer_email', 'unknown')
        return self._clean_email_address(sender)

    def _clean_email_address(self, email_str: str) -> str:
        """Extract clean email address from various formats."""
        if not email_str:
            return 'unknown'

        # Extract just the email part if it's in "Name <email>" format
        if '<' in email_str and '>' in email_str:
            start = email_str.find('<') + 1
            end = email_str.find('>')
            if start > 0 and end > start:
                return email_str[start:end].strip()

        return email_str.strip()

    def _check_order_attachment(self, metadata: Dict[str, Any]) -> str:
        """Check if email has PDF attachments that might contain orders."""
        attachments = metadata.get('attachments', [])
        if not attachments:
            return "No"

        # Check for PDF attachments specifically
        pdf_count = 0
        order_indicators = ['order', 'purchase', 'po', 'invoice', 'team systems', 'teamsystems', 'quote', 'quotation']

        for attachment in attachments:
            filename = attachment.get('filename', '').lower()
            content = attachment.get('content', '').lower()

            # Check if it's a PDF
            if filename.endswith('.pdf') or 'pdf' in attachment.get('content_type', '').lower():
                pdf_count += 1

                # Check for order indicators in PDF filename or content
                if any(indicator in filename for indicator in order_indicators):
                    return f"PDF({pdf_count})*"  # * indicates likely order content
                if any(indicator in content for indicator in order_indicators):
                    return f"PDF({pdf_count})*"

        # Return PDF count if PDFs exist but no order indicators found
        if pdf_count > 0:
            return f"PDF({pdf_count})"

        # Check for other order-related attachments
        for attachment in attachments:
            filename = attachment.get('filename', '').lower()
            content = attachment.get('content', '').lower()
            if any(indicator in filename for indicator in order_indicators):
                return "Yes"
            if any(indicator in content for indicator in order_indicators):
                return "Yes"

        return "No"

    async def fetch_recent_emails(self, hours: int = 24, limit: int = 20) -> List[Dict[str, Any]]:
        """Fetch recent emails with robust error handling.

        Args:
            hours: Number of hours to look back for emails
            limit: Maximum number of emails to return (default 20 for dashboard view)
        """
        try:
            time_threshold = (datetime.now(timezone.utc) - timedelta(hours=hours)).isoformat()

            response = self.supabase.table("customer_interactions").select("*").eq(
                "interaction_type", "email"
            ).gte("created_at", time_threshold).order("created_at", desc=True).limit(limit).execute()
            
            emails = response.data or []
            
            # Validate and clean email data
            cleaned_emails = []
            for email in emails:
                if not email or not isinstance(email, dict):
                    console.print(f"[yellow]Warning: Invalid email data: {email}[/yellow]")
                    continue
                
                # Ensure metadata exists and is valid
                metadata = email.get('metadata', {})
                if not isinstance(metadata, dict):
                    metadata = {}
                    email['metadata'] = metadata
                
                # Ensure analysis exists and is valid
                analysis = metadata.get('analysis', {})
                if not isinstance(analysis, dict):
                    analysis = {
                        "intent": "unknown",
                        "urgency": "medium", 
                        "confidence": 0.0,
                        "summary": "Analysis data missing or invalid"
                    }
                    metadata['analysis'] = analysis
                
                cleaned_emails.append(email)
            
            return cleaned_emails
            
        except Exception as e:
            console.print(f"[red]Error fetching recent emails: {e}[/red]")
            return []
    
    async def get_pending_reviews(self) -> List[Dict[str, Any]]:
        """Get emails that need human review - checks all emails, not limited to 20."""
        try:
            emails = await self.fetch_recent_emails(24, limit=200)  # Check more emails for pending reviews
            pending = []
            
            for email in emails:
                metadata = email.get('metadata', {})
                if not isinstance(metadata, dict):
                    metadata = {}

                analysis = metadata.get('analysis', {})
                if not isinstance(analysis, dict):
                    analysis = {}

                review_status = metadata.get('review_status', 'pending')
                urgency = analysis.get('urgency', 'low')
                confidence = analysis.get('confidence', 1.0)
                
                # Check if needs review
                needs_review = (
                    review_status == 'pending' or
                    urgency.lower() in ['high', 'urgent'] or
                    confidence < 0.7 or
                    analysis.get('needs_review', False) or
                    'error' in analysis
                )
                
                if needs_review:
                    # Check if internal sender
                    sender = email.get('customer_email', '').lower()
                    is_internal = any(internal in sender for internal in self.internal_senders)
                    if is_internal:
                        analysis['is_internal'] = True
                        analysis['review_reason'] = f"Internal sender: {sender}"
                    
                    pending.append(email)
            
            return pending
            
        except Exception as e:
            console.print(f"[red]Error getting pending reviews: {e}[/red]")
            return []
    
    async def display_recent_emails(self):
        """Display recent 20 emails with rich formatting for real-time dashboard view."""
        with console.status("[bold green]Loading recent emails..."):
            emails = await self.fetch_recent_emails(24, limit=20)  # Limit to 20 for dashboard

        if not emails:
            console.print("[yellow]No recent emails found[/yellow]")
            return

        # Wide table format with better column sizing
        table = Table(title=f"📧 Recent {len(emails)} Emails (24h) - Last 20", show_header=True, header_style="bold magenta", expand=True, width=None)
        table.add_column("#", style="dim", width=3, no_wrap=True)
        table.add_column("Date/Time", style="dim", width=12, no_wrap=True)
        table.add_column("From", style="cyan", min_width=25, max_width=40)
        table.add_column("Summary", style="green", min_width=40, ratio=3)  # Increased summary space
        table.add_column("PDF", style="blue", width=8, no_wrap=True)  # Renamed for clarity
        table.add_column("Intent", style="yellow", width=10, no_wrap=True)
        table.add_column("🚦", style="red", width=3, no_wrap=True)  # Traffic light urgency
        table.add_column("Status", style="blue", width=10, no_wrap=True)
        
        for i, email in enumerate(emails):
            metadata = email.get('metadata', {})
            analysis = metadata.get('analysis', {})

            # Format timestamp as dd/mm hh:mm
            created_at = email.get('created_at', '')
            if created_at:
                try:
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    time_str = dt.strftime("%d/%m %H:%M")
                except:
                    time_str = created_at[:11] if len(created_at) >= 11 else created_at
            else:
                time_str = "unknown"

            # Extract data with proper parsing
            sender = self._extract_original_sender(email, metadata)

            # Create summary from AI analysis or subject - allow word wrapping
            if isinstance(analysis, dict):
                summary = analysis.get('summary', metadata.get('subject', 'No subject'))
                intent = analysis.get('intent', 'unknown')
                urgency = analysis.get('urgency', 'medium')
            else:
                summary = metadata.get('subject', 'No subject')
                intent = 'unknown'
                urgency = 'medium'

            # Don't truncate summary as much - let table handle wrapping
            if len(summary) > 80:
                summary = summary[:77] + "..."

            # Check for order attachments
            order_attachment = self._check_order_attachment(metadata)

            # Check if internal
            is_internal = analysis.get('is_internal', False) if isinstance(analysis, dict) else False
            if is_internal:
                sender = f"[blue]{sender} (INT)[/blue]"

            # Traffic light urgency display
            urgency_traffic_lights = {
                'urgent': '🔴',
                'high': '🟠',
                'medium': '🟡',
                'low': '🟢'
            }
            urgency_display = urgency_traffic_lights.get(urgency.lower(), '⚪')

            # Get processing status
            status = analysis.get('status', 'pending') if isinstance(analysis, dict) else 'pending'
            status_colors = {
                'processed': '[green]DONE[/green]',
                'pending': '[yellow]PEND[/yellow]',
                'failed': '[red]FAIL[/red]',
                'review': '[orange1]REVIEW[/orange1]'
            }
            status_display = status_colors.get(status.lower(), status.upper())

            table.add_row(
                str(i),
                time_str,
                sender,
                summary,
                order_attachment,
                intent.upper()[:8],  # Truncate intent to fit
                urgency_display,
                status_display
            )
        
        console.print(table)
    
    async def display_pending_reviews(self):
        """Display emails pending human review."""
        with console.status("[bold green]Loading pending reviews..."):
            pending = await self.get_pending_reviews()

        if not pending:
            console.print("[green]✅ No emails pending review![/green]")
            return

        # Wide table format for pending reviews
        table = Table(title=f"📋 Pending Reviews ({len(pending)} emails)", show_header=True, header_style="bold red", expand=True, width=None)
        table.add_column("#", style="dim", width=3, no_wrap=True)
        table.add_column("Date/Time", style="dim", width=12, no_wrap=True)
        table.add_column("From", style="cyan", min_width=25, max_width=40)
        table.add_column("Summary", style="green", min_width=35, ratio=2)
        table.add_column("PDF", style="blue", width=8, no_wrap=True)
        table.add_column("Review Reason", style="yellow", min_width=25, ratio=1)
        table.add_column("🚦", style="red", width=3, no_wrap=True)
        
        for i, email in enumerate(pending):
            metadata = email.get('metadata', {})
            analysis = metadata.get('analysis', {})

            # Format timestamp as dd/mm hh:mm
            created_at = email.get('created_at', '')
            if created_at:
                try:
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    time_str = dt.strftime("%d/%m %H:%M")
                except:
                    time_str = created_at[:11] if len(created_at) >= 11 else created_at
            else:
                time_str = "unknown"

            # Extract sender using improved method
            sender = self._extract_original_sender(email, metadata)

            # Create summary from AI analysis or subject - allow more space
            summary = analysis.get('summary', metadata.get('subject', 'No subject'))
            if len(summary) > 70:
                summary = summary[:67] + "..."

            # Check for order attachments
            order_attachment = self._check_order_attachment(metadata)

            # Get review reason with better formatting
            reason = analysis.get('review_reason', metadata.get('review_reason', 'Auto-flagged'))
            if len(reason) > 35:
                reason = reason[:32] + "..."

            urgency = analysis.get('urgency', 'medium')

            # Check if internal
            is_internal = analysis.get('is_internal', False)
            if is_internal:
                sender = f"[blue]{sender} (INT)[/blue]"

            # Traffic light urgency display
            urgency_traffic_lights = {
                'urgent': '🔴',
                'high': '🟠',
                'medium': '🟡',
                'low': '🟢'
            }
            urgency_display = urgency_traffic_lights.get(urgency.lower(), '⚪')

            table.add_row(str(i), time_str, sender, summary, order_attachment, reason, urgency_display)
        
        console.print(table)
        return pending
    
    async def review_specific_email(self, email_index: int, pending_emails: List[Dict[str, Any]]):
        """Review a specific email with detailed analysis."""
        if email_index >= len(pending_emails):
            console.print("[red]Invalid email index[/red]")
            return
        
        email = pending_emails[email_index]
        metadata = email.get('metadata', {})
        analysis = metadata.get('analysis', {})
        
        # Create detailed review layout
        layout = Layout()
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="body"),
            Layout(name="footer", size=10)
        )
        
        # Header
        sender = email.get('customer_email', 'unknown')
        is_internal = analysis.get('is_internal', False)
        header_text = f"📧 Email Review - {sender}" + (" (INTERNAL)" if is_internal else "")
        layout["header"].update(Panel(Text(header_text, style="bold blue"), style="blue"))
        
        # Body - split into left and right
        layout["body"].split_row(Layout(name="left"), Layout(name="right"))
        
        # Left panel - Email content
        email_content = f"""**From:** {sender}
**Subject:** {metadata.get('subject', 'No subject')}
**Time:** {email.get('created_at', 'Unknown')}
**Thread Context:** {analysis.get('thread_relevance', 'No thread context')}

**Content:**
{email.get('content', 'No content')[:800]}{'...' if len(email.get('content', '')) > 800 else ''}"""
        
        layout["left"].update(Panel(Markdown(email_content), title="📨 Email Content", border_style="green"))
        
        # Right panel - Analysis
        confidence = analysis.get('confidence', 'unknown')
        flags = analysis.get('flags', [])
        entities = analysis.get('entities', {})
        
        analysis_content = f"""**🎯 Intent:** {analysis.get('intent', 'unknown')}
**⚡ Urgency:** {analysis.get('urgency', 'unknown')}
**🎯 Confidence:** {confidence}
**🚩 Flags:** {', '.join(flags) if flags else 'None'}
**⚠️ Review Reason:** {analysis.get('review_reason', 'Auto-flagged')}

**📋 Summary:**
{analysis.get('summary', 'No summary available')}

**📊 Entities Found:**
{', '.join([f"{k}: {v}" for k, v in entities.items()]) if entities else 'None'}

**💡 Suggested Action:**
{analysis.get('suggested_action', 'No action suggested')}

**🔗 Thread Context:**
{analysis.get('thread_relevance', 'No thread context')}"""
        
        layout["right"].update(Panel(Markdown(analysis_content), title="🤖 AI Analysis", border_style="yellow"))
        
        # Footer - Actions
        footer_text = """**Actions:**
[1] ✅ Approve Analysis
[2] ❌ Reject Analysis  
[3] 📝 Add Learning Notes
[4] ⚠️ Escalate to Manager
[5] 🔄 Re-analyze with Context
[6] ⬅️ Back to Queue"""
        
        layout["footer"].update(Panel(Markdown(footer_text), title="🎯 Review Actions", border_style="cyan"))
        
        console.clear()
        console.print(layout)
        
        # Get user decision
        action = Prompt.ask("Choose action", choices=["1", "2", "3", "4", "5", "6"], default="1")
        
        if action == "1":
            await self.approve_analysis(email)
        elif action == "2":
            await self.reject_analysis(email)
        elif action == "3":
            await self.add_learning_notes(email)
        elif action == "4":
            await self.escalate_email(email)
        elif action == "5":
            await self.reanalyze_with_context(email)
        elif action == "6":
            return
    
    async def approve_analysis(self, email: Dict[str, Any]):
        """Approve the AI analysis and store with vector embedding."""
        notes = Prompt.ask("Add approval notes (optional)", default="")

        metadata = email.get('metadata', {})
        metadata['review_status'] = 'approved'
        metadata['review_notes'] = f"Approved: {notes}"
        metadata['reviewed_at'] = datetime.now(timezone.utc).isoformat()
        metadata['reviewed_by'] = 'human_reviewer'

        try:
            # Update the existing record
            self.supabase.table("customer_interactions").update({
                "metadata": metadata
            }).eq("id", email['id']).execute()

            # Store with vector embedding for future similarity search
            try:
                from vector_embeddings import store_approved_email
                email_data = {
                    'subject': metadata.get('subject', ''),
                    'content': email.get('content', ''),
                    'from': email.get('customer_email', ''),
                    'email_id': email.get('id', ''),
                    'analysis': metadata.get('analysis', {})
                }
                await store_approved_email(email_data, metadata.get('analysis', {}))
                console.print("[green]✅ Analysis approved and stored with vector embedding![/green]")
            except ImportError:
                console.print("[green]✅ Analysis approved (vector embeddings not available)![/green]")
            except Exception as embed_error:
                console.print(f"[yellow]✅ Analysis approved (embedding error: {embed_error})[/yellow]")

        except Exception as e:
            console.print(f"[red]Error saving approval: {e}[/red]")

        input("Press Enter to continue...")
    
    async def reject_analysis(self, email: Dict[str, Any]):
        """Reject the AI analysis with feedback."""
        reason = Prompt.ask("Rejection reason", default="Incorrect analysis")
        correct_intent = Prompt.ask("Correct intent", default="")
        correct_urgency = Prompt.ask("Correct urgency (low/medium/high/urgent)", default="medium")
        
        metadata = email.get('metadata', {})
        metadata['review_status'] = 'rejected'
        metadata['review_notes'] = f"Rejected: {reason}"
        metadata['correct_intent'] = correct_intent
        metadata['correct_urgency'] = correct_urgency
        metadata['reviewed_at'] = datetime.now(timezone.utc).isoformat()
        metadata['reviewed_by'] = 'human_reviewer'
        
        try:
            self.supabase.table("customer_interactions").update({
                "metadata": metadata
            }).eq("id", email['id']).execute()
            
            # Store learning feedback
            await self.store_learning_feedback(email, reason, correct_intent, correct_urgency)
            
            console.print("[red]❌ Analysis rejected and feedback stored for learning![/red]")
        except Exception as e:
            console.print(f"[red]Error saving rejection: {e}[/red]")
        
        input("Press Enter to continue...")
    
    async def add_learning_notes(self, email: Dict[str, Any]):
        """Add learning notes for system improvement."""
        notes = Prompt.ask("Learning notes for system improvement")
        category = Prompt.ask("Category (intent/urgency/context/other)", default="other")
        
        learning_data = {
            "email_id": email.get('id'),
            "customer_email": email.get('customer_email'),
            "notes": notes,
            "category": category,
            "created_at": datetime.now(timezone.utc).isoformat(),
            "reviewer": "human_reviewer"
        }
        
        try:
            self.supabase.table("learning_feedback").insert(learning_data).execute()
            console.print("[green]📝 Learning notes saved for system improvement![/green]")
        except Exception as e:
            console.print(f"[red]Error saving learning notes: {e}[/red]")
        
        input("Press Enter to continue...")
    
    async def store_learning_feedback(self, email: Dict[str, Any], reason: str, correct_intent: str, correct_urgency: str):
        """Store feedback for machine learning improvement."""
        feedback_data = {
            "email_id": email.get('id'),
            "original_analysis": email.get('metadata', {}).get('analysis', {}),
            "rejection_reason": reason,
            "correct_intent": correct_intent,
            "correct_urgency": correct_urgency,
            "created_at": datetime.now(timezone.utc).isoformat()
        }

        try:
            self.supabase.table("analysis_feedback").insert(feedback_data).execute()
        except Exception as e:
            console.print(f"[yellow]Warning: Could not store learning feedback: {e}[/yellow]")

    async def search_emails(self, query: str, search_type: str = "content", hours: int = 168) -> List[Dict[str, Any]]:
        """Search emails with multiple search strategies."""
        try:
            time_threshold = (datetime.now(timezone.utc) - timedelta(hours=hours)).isoformat()

            if search_type == "semantic":
                # Use vector similarity search if available
                try:
                    from vector_embeddings import find_similar_emails
                    results = find_similar_emails(query, limit=20)
                    # Filter by time threshold
                    filtered_results = []
                    for email in results:
                        if email.get('created_at', '') >= time_threshold:
                            filtered_results.append(email)
                    return filtered_results
                except ImportError:
                    console.print("[yellow]Vector embeddings not available, falling back to content search[/yellow]")
                    search_type = "content"

            if search_type == "content":
                # Text-based content search
                response = self.supabase.table("customer_interactions").select("*").eq(
                    "interaction_type", "email"
                ).ilike("content", f"%{query}%").gte(
                    "created_at", time_threshold
                ).order("created_at", desc=True).limit(50).execute()

            elif search_type == "sender":
                # Search by sender email
                response = self.supabase.table("customer_interactions").select("*").eq(
                    "interaction_type", "email"
                ).ilike("customer_email", f"%{query}%").gte(
                    "created_at", time_threshold
                ).order("created_at", desc=True).limit(50).execute()

            elif search_type == "subject":
                # Search by subject in metadata
                response = self.supabase.table("customer_interactions").select("*").eq(
                    "interaction_type", "email"
                ).gte("created_at", time_threshold).order("created_at", desc=True).execute()

                # Filter by subject in metadata (Supabase doesn't support nested JSON search easily)
                all_emails = response.data or []
                filtered_emails = []
                for email in all_emails:
                    metadata = email.get('metadata', {})
                    subject = metadata.get('subject', '').lower()
                    if query.lower() in subject:
                        filtered_emails.append(email)
                return filtered_emails[:50]

            elif search_type == "intent":
                # Search by AI-analyzed intent
                response = self.supabase.table("customer_interactions").select("*").eq(
                    "interaction_type", "email"
                ).gte("created_at", time_threshold).order("created_at", desc=True).execute()

                # Filter by intent in analysis
                all_emails = response.data or []
                filtered_emails = []
                for email in all_emails:
                    metadata = email.get('metadata', {})
                    analysis = metadata.get('analysis', {})
                    intent = analysis.get('intent', '').lower()
                    if query.lower() in intent:
                        filtered_emails.append(email)
                return filtered_emails[:50]

            elif search_type == "urgency":
                # Search by urgency level
                response = self.supabase.table("customer_interactions").select("*").eq(
                    "interaction_type", "email"
                ).gte("created_at", time_threshold).order("created_at", desc=True).execute()

                # Filter by urgency in analysis
                all_emails = response.data or []
                filtered_emails = []
                for email in all_emails:
                    metadata = email.get('metadata', {})
                    analysis = metadata.get('analysis', {})
                    urgency = analysis.get('urgency', '').lower()
                    if query.lower() == urgency:
                        filtered_emails.append(email)
                return filtered_emails[:50]

            return response.data or []

        except Exception as e:
            console.print(f"[red]Error searching emails: {e}[/red]")
            return []

    async def display_search_results(self, results: List[Dict[str, Any]], query: str, search_type: str):
        """Display search results with rich formatting."""
        if not results:
            console.print(f"[yellow]No results found for '{query}' in {search_type}[/yellow]")
            return

        # Wide table format for search results
        table = Table(
            title=f"🔍 Search Results: '{query}' ({len(results)} found)",
            show_header=True,
            header_style="bold cyan",
            expand=True,
            width=None
        )
        table.add_column("#", style="dim", width=3, no_wrap=True)
        table.add_column("Date/Time", style="dim", width=12, no_wrap=True)
        table.add_column("From", style="cyan", min_width=25, max_width=40)
        table.add_column("Summary", style="green", min_width=40, ratio=3)
        table.add_column("PDF", style="blue", width=8, no_wrap=True)
        table.add_column("Intent", style="yellow", width=10, no_wrap=True)
        table.add_column("🚦", style="red", width=3, no_wrap=True)
        table.add_column("Match", style="magenta", width=10, no_wrap=True)

        for i, email in enumerate(results):
            metadata = email.get('metadata', {})
            if not isinstance(metadata, dict):
                metadata = {}

            analysis = metadata.get('analysis', {})
            if not isinstance(analysis, dict):
                analysis = {}

            # Format timestamp as dd/mm hh:mm
            created_at = email.get('created_at', '')
            if created_at:
                try:
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    time_str = dt.strftime("%d/%m %H:%M")
                except:
                    time_str = created_at[:11] if len(created_at) >= 11 else created_at
            else:
                time_str = "unknown"

            # Extract data with safe defaults
            sender = self._extract_original_sender(email, metadata)

            # Create summary from AI analysis or subject - allow more space
            summary = analysis.get('summary', metadata.get('subject', 'No subject'))
            if len(summary) > 80:
                summary = summary[:77] + "..."

            # Check for order attachments
            order_attachment = self._check_order_attachment(metadata)

            intent = analysis.get('intent', 'unknown')
            urgency = analysis.get('urgency', 'medium')

            # Determine match type - shorter labels
            if search_type == "semantic":
                match_info = "Semantic"
            elif search_type == "content":
                match_info = "Content"
            elif search_type == "sender":
                match_info = "Sender"
            elif search_type == "subject":
                match_info = "Subject"
            elif search_type == "intent":
                match_info = "Intent"
            elif search_type == "urgency":
                match_info = "Urgency"
            else:
                match_info = "Unknown"

            # Check if internal
            is_internal = analysis.get('is_internal', False)
            if is_internal:
                sender = f"[blue]{sender} (INT)[/blue]"

            # Traffic light urgency display
            urgency_traffic_lights = {
                'urgent': '🔴',
                'high': '🟠',
                'medium': '🟡',
                'low': '🟢'
            }
            urgency_display = urgency_traffic_lights.get(urgency.lower(), '⚪')

            table.add_row(
                str(i),
                time_str,
                sender,
                summary,
                order_attachment,
                intent[:8] + "..." if len(intent) > 8 else intent,
                urgency_display,
                match_info[:8]
            )

        console.print(table)
        return results

    async def advanced_search_interface(self):
        """Advanced search interface with multiple options."""
        console.clear()
        console.print(Panel.fit("🔍 Advanced Email Search", style="bold cyan"))
        console.print()

        # Search type selection
        search_table = Table(show_header=False, box=None)
        search_table.add_column("Option", style="cyan")
        search_table.add_column("Description", style="white")

        search_table.add_row("1", "📝 Content Search - Search email body text")
        search_table.add_row("2", "👤 Sender Search - Search by email address")
        search_table.add_row("3", "📋 Subject Search - Search email subjects")
        search_table.add_row("4", "🎯 Intent Search - Search by AI-detected intent")
        search_table.add_row("5", "⚡ Urgency Search - Filter by urgency level")
        search_table.add_row("6", "🧠 Semantic Search - AI similarity search")
        search_table.add_row("7", "🔄 Multi-field Search - Search across multiple fields")

        console.print(search_table)
        console.print()

        search_choice = Prompt.ask(
            "Select search type",
            choices=["1", "2", "3", "4", "5", "6", "7"],
            default="1"
        )

        # Map choices to search types
        search_type_map = {
            "1": "content",
            "2": "sender",
            "3": "subject",
            "4": "intent",
            "5": "urgency",
            "6": "semantic",
            "7": "multi"
        }

        search_type = search_type_map[search_choice]

        # Get search query
        if search_type == "urgency":
            query = Prompt.ask(
                "Select urgency level",
                choices=["low", "medium", "high", "urgent"],
                default="high"
            )
        else:
            query = Prompt.ask("Enter search query")

        if not query.strip():
            console.print("[red]Search query cannot be empty[/red]")
            input("Press Enter to continue...")
            return

        # Time range selection
        time_choice = Prompt.ask(
            "Time range",
            choices=["24h", "7d", "30d", "all"],
            default="7d"
        )

        time_map = {"24h": 24, "7d": 168, "30d": 720, "all": 8760}  # hours
        hours = time_map[time_choice]

        # Perform search
        with console.status(f"[bold green]Searching for '{query}'..."):
            if search_type == "multi":
                # Multi-field search
                results = await self.multi_field_search(query, hours)
            else:
                results = await self.search_emails(query, search_type, hours)

        # Display results
        search_results = await self.display_search_results(results, query, search_type)

        if search_results:
            console.print()
            if Confirm.ask("Review a specific email from results?", default=False):
                try:
                    index = int(Prompt.ask("Enter email index to review", default="0"))
                    if 0 <= index < len(search_results):
                        await self.review_specific_email(index, search_results)
                    else:
                        console.print("[red]Invalid index[/red]")
                except ValueError:
                    console.print("[red]Invalid index[/red]")

        input("\nPress Enter to continue...")

    async def quick_refresh_recent_emails(self):
        """Quick refresh of recent emails for real-time polling support."""
        try:
            with console.status("[bold green]Refreshing recent emails..."):
                emails = await self.fetch_recent_emails(24, limit=20)

            if emails:
                console.print(f"[green]✅ Refreshed - {len(emails)} recent emails loaded[/green]")
                return emails
            else:
                console.print("[yellow]No recent emails found[/yellow]")
                return []
        except Exception as e:
            console.print(f"[red]Error refreshing emails: {e}[/red]")
            return []

    async def escalate_email(self, email: Dict[str, Any]):
        """Escalate email to manager with context."""
        reason = Prompt.ask("Escalation reason", default="Requires manager review")
        manager_notes = Prompt.ask("Notes for manager", default="")

        metadata = email.get('metadata', {})
        metadata['escalated'] = True
        metadata['escalation_reason'] = reason
        metadata['escalation_notes'] = manager_notes
        metadata['escalated_at'] = datetime.now(timezone.utc).isoformat()
        metadata['escalated_by'] = 'human_reviewer'

        try:
            self.supabase.table("customer_interactions").update({
                "metadata": metadata
            }).eq("id", email['id']).execute()

            console.print("[orange1]⚠️ Email escalated to manager![/orange1]")
        except Exception as e:
            console.print(f"[red]Error escalating email: {e}[/red]")

        input("Press Enter to continue...")

    async def reanalyze_with_context(self, email: Dict[str, Any]):
        """Re-analyze email with additional context."""
        console.print("[yellow]🔄 Re-analyzing email with enhanced context...[/yellow]")

        # Search for similar emails for context
        content = email.get('content', '')
        if content:
            try:
                similar_emails = await self.search_emails(content[:100], "semantic", hours=720)  # 30 days
                context_info = f"Found {len(similar_emails)} similar emails for context"
            except:
                context_info = "Context search unavailable"
        else:
            context_info = "No content available for context"

        metadata = email.get('metadata', {})
        metadata['reanalyzed'] = True
        metadata['reanalysis_context'] = context_info
        metadata['reanalyzed_at'] = datetime.now(timezone.utc).isoformat()
        metadata['reanalyzed_by'] = 'human_reviewer'

        try:
            self.supabase.table("customer_interactions").update({
                "metadata": metadata
            }).eq("id", email['id']).execute()

            console.print(f"[green]✅ Email marked for re-analysis! {context_info}[/green]")
        except Exception as e:
            console.print(f"[red]Error marking for re-analysis: {e}[/red]")

        input("Press Enter to continue...")

    async def quick_search(self, query: str) -> List[Dict[str, Any]]:
        """Quick search function for immediate results."""
        try:
            # Try semantic search first, fall back to content search
            results = await self.search_emails(query, "semantic", hours=168)
            if not results:
                results = await self.search_emails(query, "content", hours=168)
            return results[:10]  # Limit to 10 quick results
        except Exception as e:
            console.print(f"[red]Quick search error: {e}[/red]")
            return []

    async def multi_field_search(self, query: str, hours: int = 168) -> List[Dict[str, Any]]:
        """Search across multiple fields and combine results."""
        try:
            # Search in different fields
            content_results = await self.search_emails(query, "content", hours)
            sender_results = await self.search_emails(query, "sender", hours)
            subject_results = await self.search_emails(query, "subject", hours)

            # Combine and deduplicate results
            all_results = {}

            for email in content_results + sender_results + subject_results:
                email_id = email.get('id')
                if email_id and email_id not in all_results:
                    all_results[email_id] = email

            # Sort by creation date
            sorted_results = sorted(
                all_results.values(),
                key=lambda x: x.get('created_at', ''),
                reverse=True
            )

            return sorted_results[:50]  # Limit to 50 results

        except Exception as e:
            console.print(f"[red]Error in multi-field search: {e}[/red]")
            return []
    
    async def show_system_statistics(self):
        """Display comprehensive system statistics."""
        with console.status("[bold green]Calculating statistics..."):
            emails = await self.fetch_recent_emails(24, limit=100)  # Use more emails for accurate statistics
        
        if not emails:
            console.print("[yellow]No data available for statistics[/yellow]")
            return
        
        # Calculate statistics
        stats = {
            "total_emails": len(emails),
            "urgency_dist": {"low": 0, "medium": 0, "high": 0, "urgent": 0},
            "intent_dist": {},
            "review_status_dist": {"pending": 0, "approved": 0, "rejected": 0, "auto_approved": 0},
            "internal_emails": 0,
            "avg_confidence": 0,
            "confidence_scores": []
        }

        for email in emails:
            metadata = email.get('metadata', {})
            analysis = metadata.get('analysis', {})

            # Check if internal email by sender
            sender = email.get('customer_email', '').lower()
            is_internal = any(internal in sender for internal in self.internal_senders)
            if is_internal:
                stats["internal_emails"] += 1

            # Urgency distribution
            urgency = analysis.get('urgency', 'medium').lower()
            if urgency in stats["urgency_dist"]:
                stats["urgency_dist"][urgency] += 1

            # Intent distribution
            intent = analysis.get('intent', 'unknown')
            stats["intent_dist"][intent] = stats["intent_dist"].get(intent, 0) + 1

            # Review status
            review_status = metadata.get('review_status', 'pending')
            if review_status in stats["review_status_dist"]:
                stats["review_status_dist"][review_status] += 1

            # Confidence scores
            confidence = analysis.get('confidence')
            if isinstance(confidence, (int, float)):
                stats["confidence_scores"].append(confidence)
        
        # Calculate average confidence
        if stats["confidence_scores"]:
            stats["avg_confidence"] = sum(stats["confidence_scores"]) / len(stats["confidence_scores"])
        
        # Display statistics
        layout = Layout()
        layout.split_row(Layout(name="left"), Layout(name="right"))
        
        # Left panel - Email metrics
        email_table = Table(title="📧 Email Processing Metrics", show_header=False, expand=True)
        email_table.add_column("Metric", style="cyan", ratio=1)
        email_table.add_column("Value", style="green", ratio=1)
        
        email_table.add_row("Total Emails (24h)", str(stats["total_emails"]))
        email_table.add_row("Internal Emails", str(stats["internal_emails"]))
        email_table.add_row("Avg Confidence", f"{stats['avg_confidence']:.2f}")
        email_table.add_row("Pending Review", str(stats["review_status_dist"]["pending"]))
        email_table.add_row("Approved", str(stats["review_status_dist"]["approved"]))
        email_table.add_row("Rejected", str(stats["review_status_dist"]["rejected"]))
        
        layout["left"].update(Panel(email_table, border_style="green"))
        
        # Right panel - Distributions
        dist_table = Table(title="📈 Distribution Analysis", show_header=True, expand=True)
        dist_table.add_column("Category", style="cyan", width=10, no_wrap=True)
        dist_table.add_column("Type", style="yellow", min_width=12, ratio=1)
        dist_table.add_column("Count", style="green", width=6, no_wrap=True)
        dist_table.add_column("Percentage", style="magenta", width=8, no_wrap=True)
        
        # Urgency distribution
        total = stats["total_emails"]
        for urgency, count in stats["urgency_dist"].items():
            percentage = (count / total * 100) if total > 0 else 0
            dist_table.add_row("Urgency", urgency.title(), str(count), f"{percentage:.1f}%")
        
        # Top intents
        sorted_intents = sorted(stats["intent_dist"].items(), key=lambda x: x[1], reverse=True)[:5]
        for intent, count in sorted_intents:
            percentage = (count / total * 100) if total > 0 else 0
            dist_table.add_row("Intent", intent, str(count), f"{percentage:.1f}%")
        
        layout["right"].update(Panel(dist_table, border_style="yellow"))
        
        console.print(layout)
        input("\nPress Enter to continue...")

async def main():
    """Main dashboard interface."""
    dashboard = HumanReviewDashboard()

    while True:
        console.clear()
        console.print(Panel.fit("👤 Human Review Dashboard", style="bold blue"))
        console.print()

        menu_table = Table(show_header=False, box=None)
        menu_table.add_column("Option", style="cyan")
        menu_table.add_column("Description", style="white")

        menu_table.add_row("1", "📧 View Recent Emails (Last 20)")
        menu_table.add_row("2", "📋 Review Pending Emails")
        menu_table.add_row("3", "📊 System Statistics")
        menu_table.add_row("4", "� Search Emails")
        menu_table.add_row("5", "�🔄 Refresh Data")
        menu_table.add_row("q", "❌ Quit")

        console.print(menu_table)
        console.print()

        choice = Prompt.ask("Select option", choices=["1", "2", "3", "4", "5", "q"], default="1")

        if choice == "1":
            console.clear()
            await dashboard.display_recent_emails()
            input("\nPress Enter to continue...")

        elif choice == "2":
            console.clear()
            pending = await dashboard.display_pending_reviews()
            if pending:
                console.print()
                try:
                    index = int(Prompt.ask("Enter email index to review", default="0"))
                    await dashboard.review_specific_email(index, pending)
                except ValueError:
                    console.print("[red]Invalid index[/red]")
                    input("Press Enter to continue...")

        elif choice == "3":
            console.clear()
            await dashboard.show_system_statistics()

        elif choice == "4":
            await dashboard.advanced_search_interface()

        elif choice == "5":
            console.clear()
            await dashboard.quick_refresh_recent_emails()
            input("\nPress Enter to continue...")

        elif choice == "q":
            console.print("[yellow]Goodbye![/yellow]")
            break

if __name__ == "__main__":
    asyncio.run(main())
