---
url: "https://developer.myob.com/api/exo/endpoints/purchaseorder/"
title: "Purchase Order"
---

[![](https://developer.myob.com/media/cms_page_media/23/myob_logo_small.png)](http://www.myob.com/)

# Purchase Order

Return a purchase order.

**Date Released:** Sep 20th 2023 **Date Updated:** Sep 20th 2023

| URL | Supports |
| --- | --- |
| {URI}/purchaseorder/<br>{URI}/purchaseorder/search?q={query} | [GET](https://developer.myob.com/api/exo/endpoints/purchaseorder/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/purchaseorder/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/purchaseorder/#POST)<br>[GET](https://developer.myob.com/api/exo/endpoints/purchaseorder/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/purchaseorder/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/purchaseorder/#POST) |
| {URI} is exo.api.myob.com when connecting to the cloud or the address of the machine hosting the API when connecting on premise. |

`search` returns purchase orders that match the specified search string.

**Note:** Actual fields returned may differ slightly depending on local settings and configuration.

The elements list below details information for Purchase Order. To view the descriptions for the elements you can either hover any attribute to reveal details [or click here to show all details inline.](https://developer.myob.com/api/exo/endpoints/purchaseorder/#reveal)

#### Attribute Details

- lines array,null
- Type: array,null. Optional, but orders without lines don't have much purpose.
  - stockcode string
  - Type: string. Length defined by database schema, default 23.
  - orderquantity number
  - Type: number. Decimal Precision defined by profile QTYDECIMALS in Exo system.
  - supplyquantity number,calculated
  - Type: number. DO NOT SPECIFY. Always 0 for new Orders/Quotes. Do not change value as Supply via API is not enabled.
  - invoicequantity number,calculated
  - Type: number. DO NOT SPECIFY. Always 0 for new Orders/Quotes. Do not change value as Supply via API is not enabled.
  - correctionquantity number,calculated
  - Type: number.
  - correctedquantity number,calculated
  - Type: number.
  - supplynowquantity number,calculated
  - Type: number. DO NOT SPECIFY. Always the full value of the line for new Orders/Quotes. Do not change value as Supply via API is not enabled.
  - invoicenowquantity number,calculated
  - Type: number. DO NOT SPECIFY. Always 0 for new Orders/Quotes. Do not change value as Supply via API is not enabled.
  - lastsupplyquantity number,calculated
  - Type: number. DO NOT SPECIFY. Always 0 for new Orders/Quotes. Do not change value as Supply via API is not enabled.
  - lastinvoicequantity number,calculated
  - Type: number. DO NOT SPECIFY. Always 0 for new Orders/Quotes. Do not change value as Supply via API is not enabled.
  - linkedquantity number,calculated
  - Type: number.
  - purchasepackquantity number,calculated
  - Type: number.
  - costquantitynumber,calculated
  - Type: number.
  - jobcode string,null
  - Type: string.
  - jobno integer,null
  - Type: integer.
  - salesorderno integer,null
  - Type: integer.
  - codetype char
  - Type: char.
  - suppliercode string
  - Type: string.
  - polineid integer
  - Type: integer.
  - linkedstockcode string
  - Type: string.
  - linkedstatus boolean
  - Type: boolean.
  - solineid integer
  - Type: integer.
  - purchasepackprice number,null
  - Type: number.
  - sellprice number,null
  - Type: number.
  - discount number,null
  - Type: number. Optional. Decimal Precision defined by profile DISCOUNTDECIMALS in Exo system. Note that discounts may have additional restrictions on them in system.
  - unitprice number,null
  - Type: number. Optional. Decimal Precision defined by profile SELLPRDECIMALS in Exo system. Tax exclusive price. Failure to specify will return the default price on order validation.
  - ispriceoverridden boolean,null
  - Type: boolean. Optional. Specifies if the price has been overridden. Overridden prices are not recalculated if an order is edited in Exo Business.
  - locationid integer,null
  - Type: integer. Optional. Stock Location that the line should be supplied from. If not specified will be defaulted based on API User.
  - taxrateid integer,null
  - Type: integer. Optional. TaxrateID for the line. If unspecified will default based on Debtor/Stock Item hierarchy from Exo.
  - linetype integer,enum,null
  - Type: integer. Optional. Linetypes can be (0 = Normal stockline,1 = BOM header line,2 = BOM component line,4 = Comment/Narrative line). Default is 0.
  - stocktype enum,calculated
  - Type: enum. DO NOT SPECIFY. Will be either PhysicalItem or Lookup Item. (Defines if Item has stock, retrieved from stock item record)
  - id integer,null
  - Type: integer. Database ID number for Order line. SOLINEID value from SALESORD\_LINES.
  - branchid integer,null
  - Type: integer. Optional. Branch the line's profit should be reported to. If not specified is inherited from Order branch or User.
  - taxratevalue number,null
  - Type: number. Optional. (Recommended DO NOT SPECIFY). The taxrate for the line. (Percentage). To correctly specify taxrates use taxrateid
  - istaxoverridden boolean,null
  - Type: boolean. Optional. Only specify if you have overridden the tax calculation so it no longer matches the database default. Note that if you overwrite the tax value for a line, this prevents Exo from recalculating prices on Order edits/processing.
  - taxoverridevalue number,null
  - Type: number. Optional. If istaxoverridden is true then must specify the exact tax amount for the line.
  - narrative string,null
  - Type: string. Optional. Additional narrative (nvarchar(Max)) for recording additional information.
  - backorderquantity number,null
  - Type: number. Optional. Specifies the backorder quantity for a line (which affects stock availability and supply processes in Exo Business.)
  - description string,null
  - Type: string. Optional. If not specified will be the standard item description from the stock item. String truncation errors can occur if you don't obey database schema. (Default schema length 100 on new databases)
  - batchcode string,null
  - Type: string. Optional. Specify a batchcode for batch tracking purposes, note batch tracking is not fully supported via the API. Schema bound with default length of 20 characters.
  - duedate date,null
  - Type: date. Optional, line specific duedate. Should be in format YYYY-MM-DD when set.
  - linetotal number,calculated
  - Type: number. DO NOT SPECIFY. Calculated based on unitprice \* orderquantity less discount. Tax Exclusive line-total.
  - rankorder integer,null
  - Type: integer. For determining line-position when printing.
  - extrafields array,null
  - Type: array. Optional. Defined in individual user databases against SALESORD\_LINES level. For assistance speak to database's implementation partner.
- creditorid integer,null
- Type: integer.
- authorisationstaffno integer,null
- Type: integer.
- authorisationdate date,null
- Type: date.
- isconfirmed boolean,null
- Type: boolean.
- leadtimeused integer,null
- Type: integer.
- hstatus integer,enum
- Type: integer.
- sosourceref string,null
- Type: string.
- campaignwaveseqno string,null
- Type: string.
- hasuninvoiced boolean,calculated
- Type: boolean. DO NOT SPECIFY. Whether order has uninvoiced lines.
- hasunsupplied boolean,calculated
- Type: boolean. DO NOT SPECIFY. Whether order has unsupplied lines.
- accountname string,null
- Type: string. Optional. Always retrieves the Accountname value from the Debtor account.
- istaxinclusive boolean,null
- Type: boolean, defaults false. (Tax Exclusive). Defines if pricing on the order is tax inclusive or tax exclusive.
- hasbackorders boolean,calculated
- Type: boolean. DO NOT SPECIFY. Whether order has backordered lines.
- branchid integer,null
- Type: integer. Default branch for the order. If unspecified will be drawn based on user profile. BRANCHNO field.
- defaultlocationid integer, null
- Type: integer. Default stock location for order. If unspecified will default based on user. DEFLOCNO field.
- narrative string,null
- Type: string. Optional. Additional narrative (nvarchar(Max)) for recording information at a Sales Order Header level.
- currencyid integer,null
- Type: integer. Optional. Defines the currency the order is in. Should always be the same as the related Debtor account. If not specified will default to Debtor account Currrrency. CURRENCYNO
- salespersonid integer,null
- Type: integer. Optional. Salesperson for the Order, is an integer specifying the staff member from the STAFF table.
- exchangerate number,null
- Type: number. Optional. Exchange rate order was received at. Must be 1 for currencyno 0.
- taxtotal number,calculated
- Type: number. DO NOT SPECIFY. The total tax value of the order.
- subtotal number,calculated
- Type: number. DO NOT SPECIFY. The subtotal of all lines without tax.
- reference string,null
- Type: string. Optional, reference field for order. Default definition NVARCHAR(20)
- instructions string,null
- Type: string. Optional, delivery instruction field for order. Default database definition NVARCHAR(255)
- createdate date,calculated
- Type: date. DO NOT SPECIFY. Timestamp of order being created.
- orderdate date,null
- Type: date. Optional, date of order. Should be in format YYYY-MM-DD when set. Defaults to today's date.
- duedate date,null
- Type: date. Optional, overall order duedate. Should be in format YYYY-MM-DD when set.
- finalisationdate date,null
- Type: date. Optional, date order was finalised. Should be in format YYYY-MM-DD when set. Should be left alone.
- activationdate datetime,null
- Type: date. Optional. Date order was activated. Full timestamp value. Defaults to current time. Returns string.
- deliveryaddress array,null
- Type: array. Delivery address for order, if unspecified will default to Debtor account delivery address.
  - line1 string,null
  - Type: string. Delivery address line 1. Display definition defined as part of Exo Profiles. Default length is NVARCHAR(30). If longer lengths are required speak to implementer.
  - line2 string,null
  - Type: string. Delivery address line 2. Display definition defined as part of Exo Profiles. Default length is NVARCHAR(30). If longer lengths are required speak to implementer.
  - line3 string,null
  - Type: string. Delivery address line 3. Display definition defined as part of Exo Profiles. Default length is NVARCHAR(30). If longer lengths are required speak to implementer.
  - line4 string,null
  - Type: string. Delivery address line 4. Display definition defined as part of Exo Profiles. Default length is NVARCHAR(30). If longer lengths are required speak to implementer.
  - line5 string,null
  - Type: string. Delivery address line 5. Display definition defined as part of Exo Profiles. Default length is NVARCHAR(30). If longer lengths are required speak to implementer. OPTIONAL FIELD - may not be visible to end users in all systems.
  - line6 string,null
  - Type: string. Delivery address line 6. Display definition defined as part of Exo Profiles. Default length is NVARCHAR(30). If longer lengths are required speak to implementer. OPTIONAL FIELD - may not be visible to end users in all systems.
- status integer,enum
- Type: integer. 0 = "Not Processed", 3 = "Quotation", 4 = "Standing Order", 5 - "Layby". Note that not all statuses are available if Extended Order types is off. (1 = Partially Processed, 2 = Fully Processed. These will be set by system users on processing orders.)
- statusdescription string calculated
- Type: string. DO NOT SPECIFY. Description of the Status integer.
- finalised integer,null
- Type: integer. Optional. Specifies the Finalisation status of order. 0 = Normal, 1 = Deleted, 2 = Forced Fully Processed, 3 = Lost Quote
- ordertotal number,calculated
- Type: number. DO NOT SPECIFY. Calculated total of the Order.
- localvalue number,calculated
- Type: number. DO NOT SPECIFY. The converted value of the order in the system denomination currency. (Ordertotal \* exchangerate)
- lastupdated datetime,calculated
- Type: date. DO NOT SPECIFY. Date order was last edited. Full timestamp value. Defaults to current time on order update. Returns string.
- extrafields array,null
- Type: array. Optional. Defined in individual user databases against SALESORD\_HDR level. For assistance speak to database's implementation partner.
- id integer,null
- Type: integer. ID value specified is the SEQNO from SALESORD\_HDR
- href string,null
- Type: string. DO NOT SPECIFY. URI location for retrieving sales order.

#### Example json GET response

- {
  - lines
    - {
      - 0
        - {
          - stockcode : AIRSUS01
          - orderquantity : 1.0
          - supplyquantity : 0.0
          - invoicequantity : 0.0
          - correctionquantity : 0.0
          - correctedquantity : 1.0
          - supplynowquantity : 0.0
          - invoicenowquantity : 0.0
          - lastsupplyquantity : 0.0
          - lastinvoicequantity : 0.0
          - linkedquantity : 1.0
          - purchasepackquantity : 1.0
          - costquantity : 0.0
          - jobcode : 1
          - jobno : 1
          - salesorderno : -1
          - codetype : S
          - suppliercode :
          - polineid : 359
          - linkedstockcode : AIRSUS01
          - linkedstatus : N
          - solineid : -1
          - purchasepackprice : 0.0
          - sellprice : -1.00
          - discount : 0.0
          - unitprice : 184.55
          - locationid : 1
          - taxrateid : 3
          - linetype : 0.0
          - stocktype : PhysicalItem
          - id : 359
          - branchid : 0
          - taxratevalue : 12.5
          - istaxoverridden : false
          - taxoverridevalue : 0.0
          - narrative
          - backorderquantity : 1.00
          - description : AIR SUSPENSION
          - batchcode
          - duedate : 2024-01-12
          - linetotal : 184.55
          - rankorder : 1
          - extrafields
            - {
            - }
        - }
    - }
  - creditorid : 0
  - authorisationstaffno : -1
  - authorisationdate :
  - isconfirmed : false
  - leadtimeused : 0
  - hstatus : 0
  - sosourceref :
  - campaignwaveseqno : -1
  - hasuninvoiced : true
  - hasunsupplied : true
  - accountname : MISC PURCHASES
  - istaxinclusive : false
  - hasbackorders : true
  - branchid : 0
  - defaultlocationid : -1
  - narrative
  - currencyid : 0
  - salespersonid : 8
  - exchangerate : 1.0
  - taxtotal : 23.07
  - subtotal : 184.55
  - reference
  - instructions
  - createdate : 2013-09-18T10:23:13+12:00
  - orderdate : 2024-01-12
  - duedate : 2024-01-12
  - finalisationdate
  - activationdate
  - deliveryaddress
    - {
      - line1 : 238 GREAT NORTH ROAD
      - line2 : GREY LYNN
      - line3 : NSW
      - line4 : Australia
      - line5 :
      - line6 :
    - }
  - status : 0
  - statusdescription : Unprocessed Order
  - finalised : 0
  - ordertotal : 207.62
  - localvalue : 184.55
  - lastupdated : 2024-01-12T11:31:04+13:00
  - extrafields
    - {
      - 0
        - {
          - key : X\_CHECKEDBY
          - value
        - }
    - }
  - id : 10082
  - href : https://relaycloud.exo.api.myob.com/relay/purchaseorder/10082
- }