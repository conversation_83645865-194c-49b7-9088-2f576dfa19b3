# Production-specific requirements for Multi-Agent Sales Support System
# Updated: January 2025 with latest versions

# WSGI/ASGI servers
gunicorn==23.0.0  # Latest version
uvicorn[standard]==0.35.0  # Latest version

# Production monitoring and logging
prometheus-client==0.21.0  # Latest version
structlog==25.4.0  # Latest version
python-json-logger==2.0.7

# Security
cryptography==45.0.5  # Latest version
python-jose[cryptography]==3.5.0  # Latest version
passlib[bcrypt]==1.7.4

# Performance and caching
redis==6.2.0  # Latest version
hiredis==3.1.0  # Latest version

# Database connection pooling
psycopg2-binary==2.9.10  # Latest version

# HTTP client with connection pooling
httpx[http2]==0.28.0  # Latest version

# Process management
supervisor==4.2.5

# Health checks and monitoring
psutil==6.1.0  # Latest version

# Error tracking (optional)
sentry-sdk[fastapi]==2.18.0  # Latest version

# Rate limiting
slowapi==0.1.9

# Background task processing
celery==5.4.0  # Latest version
kombu==5.4.2  # Latest version

# File handling and compression
python-multipart==0.0.12  # Latest version
aiofiles==24.1.0  # Latest version

# Email handling
email-validator==2.2.0  # Latest version

# Configuration management
python-decouple==3.8

# Timezone handling
pytz==2025.2  # Latest version

# Data validation and serialization
marshmallow==3.20.2

# API documentation
fastapi-users==12.1.2

# Backup and archiving
boto3==1.34.0  # For AWS S3 backups

# Metrics and telemetry
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0
opentelemetry-exporter-prometheus==1.12.0rc1

# Load balancing and service discovery
consul-python==1.1.0

# Circuit breaker pattern
pybreaker==1.0.1

# Retry mechanisms
tenacity==8.2.3

# Memory profiling (for debugging)
memory-profiler==0.61.0

# Production-grade JSON handling
orjson==3.9.10

# Async database drivers
asyncpg==0.29.0

# Message queuing
aio-pika==9.3.1  # For RabbitMQ

# Distributed tracing
jaeger-client==4.8.0

# Configuration validation
cerberus==1.3.5

# Production logging formatters
colorlog==6.8.0

# System monitoring
py-cpuinfo==9.0.0

# Network utilities
netifaces==0.11.0

# Time series data handling
pandas==2.1.4  # For analytics and reporting

# Image processing (for PDF handling)
Pillow==10.1.0

# Compression
zstandard==0.22.0

# UUID generation
shortuuid==1.0.11

# Date/time utilities
arrow==1.3.0

# Async utilities
asyncio-throttle==1.0.2

# Production-grade templating
Jinja2==3.1.2

# File system monitoring
watchdog==3.0.0

# Network protocols
websockets==12.0

# Data serialization
msgpack==1.0.7

# Concurrent processing
concurrent-futures==3.1.1

# System information
distro==1.8.0

# Production utilities
click==8.1.7  # For CLI tools
rich==13.7.0  # For beautiful terminal output

# Backup utilities
schedule==1.2.0  # For scheduled tasks

# Production database migrations
alembic==1.13.1

# API versioning
fastapi-versioning==0.10.0

# CORS handling
fastapi-cors==0.0.6

# Request ID tracking
fastapi-request-id==1.0.0

# Production middleware
fastapi-limiter==0.1.5

# Health check utilities
healthcheck==1.3.3

# Production configuration
dynaconf==3.2.4

# Async context managers
async-timeout==4.0.3

# Production-grade UUID
uuid7==0.1.0

# Memory optimization
pympler==0.9

# Production testing utilities
factory-boy==3.3.0  # For test data generation

# API client generation
openapi-generator-cli==7.1.0

# Production documentation
mkdocs==1.5.3
mkdocs-material==9.4.8

# Code quality in production
bandit==1.7.5  # Security linting
safety==2.3.5  # Dependency vulnerability scanning
