ag-ui-protocol==0.1.8
agno==1.5.9
aiohappyeyeballs==2.6.1
aiohttp==3.11.18
aioresponses==0.7.8
aiosignal==1.3.2
alembic==1.15.1
altair==5.5.0
annotated-types==0.7.0
anthropic==0.60.0
anyio==4.9.0
argcomplete==3.6.2
asgiref==3.8.1
asttokens==3.0.0
asyncio==3.4.3
asyncpg==0.30.0
attrs==25.3.0
azure-common==1.1.28
azure-core==1.32.0
azure-search-documents==11.5.2
babel==2.17.0
backoff==2.2.1
backrefs==5.8
bcrypt==4.3.0
beautifulsoup4==4.13.4
black==25.1.0
blinker==1.9.0
blockbuster==1.5.24
blosc2==3.3.4
boto3==1.38.46
botocore==1.38.46
branca==0.8.1
bs4==0.0.2
build==1.2.2.post1
CacheControl==0.14.3
cachetools==5.5.2
camelot-py==1.0.0
certifi==2025.1.31
cffi==1.17.1
cfgv==3.4.0
chardet==5.2.0
charset-normalizer==3.4.1
chroma-hnswlib==0.7.6
chromadb==1.0.13
cleo==2.1.0
click==8.2.0
cloudpickle==3.1.1
cohere==5.16.1
colorama==0.4.6
coloredlogs==15.0.1
colorlog==6.9.0
comm==0.2.2
contourpy==1.3.2
coverage==7.8.0
crashtest==0.4.1
cryptography==45.0.5
cycler==0.12.1
dataclasses-json==0.6.7
debugpy==1.8.13
decorator==5.2.1
Deprecated==1.2.18
deprecation==2.1.0
dirtyjson==1.0.8
diskcache==5.6.3
distlib==0.3.9
distro==1.9.0
dnspython==2.7.0
docstring_parser==0.16
duckduckgo_search==7.5.2
dulwich==0.22.8
durationpy==0.9
ecdsa==0.19.1
email_validator==2.2.0
embedchain==0.1.128
et_xmlfile==2.0.0
eval_type_backport==0.2.2
exa==0.5.26
execnet==2.1.1
executing==2.2.0
FalkorDB==1.2.0
fasta2a==0.3.4
fastapi==0.116.1
fastavro==1.10.0
fastjsonschema==2.21.1
filelock==3.18.0
filetype==1.2.0
findpython==0.6.3
Flask==3.1.1
flask-cors==6.0.1
flatbuffers==25.2.10
folium==0.19.5
fonttools==4.58.0
forbiddenfruit==0.1.4
freezegun==1.5.1
frozenlist==1.5.0
fsspec==2025.3.0
geographiclib==2.0
geopy==2.4.1
ghp-import==2.1.0
gitdb==4.0.12
GitPython==3.1.44
google-ai-generativelanguage==0.6.6
google-api-core==2.24.2
google-api-python-client==2.177.0
google-auth==2.40.3
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.2.2
google-genai==1.27.0
google-generativeai==0.7.2
googleapis-common-protos==1.69.2
gotrue==2.12.3
gptcache==0.1.44
graphiti-core==0.18.0
greenlet==3.1.1
griffe==1.7.3
groq==0.30.0
grpcio==1.71.0
grpcio-status==1.63.0rc1
grpcio-tools==1.71.0
h11==0.14.0
h2==4.2.0
hpack==4.1.0
httpcore==1.0.7
httplib2==0.22.0
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.34.2
humanfriendly==10.0
hyperframe==6.1.0
identify==2.6.12
idna==3.10
importlib_metadata==8.4.0
importlib_resources==6.5.2
iniconfig==2.1.0
installer==0.7.0
instructor==1.9.0
ipykernel==6.29.5
ipython==9.0.2
ipython_pygments_lexers==1.1.1
isodate==0.7.2
itsdangerous==2.2.0
jaraco.classes==3.4.0
jaraco.context==6.0.1
jaraco.functools==4.1.0
jedi==0.19.2
Jinja2==3.1.6
jiter==0.9.0
jmespath==1.0.1
joblib==1.4.2
json5==0.12.0
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jsonschema_rs==0.29.1
jupyter_client==8.6.3
jupyter_core==5.7.2
keyring==25.6.0
kiwisolver==1.4.8
kubernetes==32.0.1
langchain==0.3.27
langchain-anthropic==0.3.17
langchain-chroma==0.2.2
langchain-cohere==0.3.5
langchain-community==0.3.27
langchain-core==0.3.72
langchain-experimental==0.3.4
langchain-gemini==0.1.1
langchain-google-genai==2.0.0
langchain-openai==0.3.28
langchain-qdrant==0.2.0
langchain-text-splitters==0.3.9
langgraph==0.4.5
langgraph-api==0.2.27
langgraph-checkpoint==2.0.26
langgraph-cli==0.2.10
langgraph-prebuilt==0.1.8
langgraph-runtime-inmem==0.0.11
langgraph-sdk==0.1.69
langsmith==0.4.8
llama-cloud==0.1.23
llama-cloud-services==0.6.24
llama-index==0.12.25
llama-index-agent-openai==0.4.6
llama-index-cli==0.4.1
llama-index-core==0.12.25
llama-index-embeddings-openai==0.3.1
llama-index-indices-managed-llama-cloud==0.6.9
llama-index-llms-openai==0.3.25
llama-index-multi-modal-llms-openai==0.4.3
llama-index-program-openai==0.3.1
llama-index-question-gen-openai==0.3.0
llama-index-readers-file==0.4.6
llama-index-readers-llama-parse==0.4.0
llama-parse==0.6.22
llvmlite==0.44.0
logfire==3.15.1
logfire-api==3.21.1
lxml==5.3.1
Mako==1.3.9
Markdown==3.8
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib==3.10.3
matplotlib-inline==0.1.7
mcp==1.10.1
mdurl==0.1.2
mem0ai==0.1.75
mergedeep==1.3.4
mistralai==1.9.3
mkdocs==1.6.1
mkdocs-get-deps==0.2.0
mkdocs-material==9.6.14
mkdocs-material-extensions==1.3.1
mmh3==5.1.0
monotonic==1.6
more-itertools==10.7.0
mpmath==1.3.0
msgpack==1.1.0
multidict==6.2.0
mypy-extensions==1.0.0
narwhals==1.32.0
ndindex==1.10.0
neo4j==5.28.1
nest-asyncio==1.6.0
networkx==3.4.2
nltk==3.9.1
nodeenv==1.9.1
npm==0.1.1
numba==0.61.2
numexpr==2.10.2
numpy==2.3.2
oauthlib==3.2.2
onnxruntime==1.21.0
openai==1.97.1
opencv-python-headless==4.11.0.86
openpyxl==3.1.5
opentelemetry-api==1.34.1
opentelemetry-exporter-otlp-proto-common==1.27.0
opentelemetry-exporter-otlp-proto-grpc==1.27.0
opentelemetry-exporter-otlp-proto-http==1.33.0
opentelemetry-instrumentation==0.54b0
opentelemetry-instrumentation-asgi==0.52b0
opentelemetry-instrumentation-fastapi==0.52b0
opentelemetry-instrumentation-system-metrics==0.54b0
opentelemetry-proto==1.27.0
opentelemetry-sdk==1.27.0
opentelemetry-semantic-conventions==0.48b0
opentelemetry-util-http==0.52b0
optional-django==0.1.0
orjson==3.10.15
ormsgpack==1.9.1
overrides==7.7.0
packaging==24.2
paginate==0.5.7
pandas==2.3.1
parso==0.8.4
pathlib==1.0.1
pathspec==0.12.1
pbs-installer==2025.4.9
pdfminer.six==20250327
pdfplumber==0.11.6
pglast==7.7
pgvector==0.4.1
pillow==11.2.1
pkginfo==1.12.1.2
platformdirs==4.3.7
plotly==6.0.1
pluggy==1.5.0
poetry==2.1.3
poetry-core==2.1.3
portalocker==2.10.1
postgrest==1.1.1
posthog==3.21.0
pre_commit==4.2.0
primp==0.14.0
prompt_toolkit==3.0.50
propcache==0.3.0
proto-plus==1.26.1
protobuf==4.25.8
psutil==7.0.0
psycopg2-binary==2.9.10
pure_eval==0.2.3
py-cpuinfo==9.0.0
pyarrow==19.0.1
pyasn1==0.4.8
pyasn1_modules==0.4.1
pybase64==1.4.1
pycparser==2.22
pydantic==2.11.7
pydantic-ai==0.4.8
pydantic-ai-slim==0.4.8
pydantic-evals==0.4.8
pydantic-graph==0.4.8
pydantic-settings==2.8.1
pydantic_core==2.33.2
pydeck==0.9.1
pyfiglet==1.0.2
Pygments==2.19.1
PyJWT==2.10.1
pymdown-extensions==10.15
PyMuPDF==1.26.3
pymupdf4llm==0.0.27
pyparsing==3.2.3
pypdf==5.4.0
PyPDF2==3.0.1
pypdfium2==4.30.1
PyPika==0.48.9
pyproject_hooks==1.2.0
pyreadline3==3.5.4
pysbd==0.3.4
pytesseract==0.3.13
pytest==8.3.5
pytest-asyncio==0.26.0
pytest-cov==6.1.1
pytest-env==1.1.5
pytest-mock==3.14.0
pytest-xdist==3.6.1
python-dateutil==2.9.0.post0
python-docx==1.2.0
python-dotenv==1.1.0
python-jose==3.4.0
python-magic==0.4.27
python-magic-bin==0.4.14
python-multipart==0.0.20
pytz==2025.2
pywin32==310
pywin32-ctypes==0.2.3
PyYAML==6.0.2
pyyaml_env_tag==1.1
pyzmq==26.3.0
qdrant-client==1.12.1
RapidFuzz==3.13.0
realtime==2.6.0
redis==6.2.0
referencing==0.36.2
regex==2024.11.6
requests==2.32.4
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
respx==0.22.0
rich==13.9.4
rpds-py==0.23.1
rsa==4.9
ruff==0.12.5
s3transfer==0.13.0
safetensors==0.5.3
schedule==1.2.2
schema==0.7.7
scikit-learn==1.6.1
scipy==1.15.2
seaborn==0.13.2
sentence-transformers==4.1.0
setuptools==78.0.2
shellingham==1.5.4
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.39
sse-starlette==2.1.3
stack-data==0.6.3
starlette==0.46.1
storage3==0.12.0
streamlit==1.41.1
StrEnum==0.4.15
striprtf==0.0.26
structlog==25.4.0
supabase==2.17.0
supabase-mcp-server==0.4
supafunc==0.10.1
sympy==1.13.3
tables==3.10.2
tabulate==0.9.0
-e git+https://github.com/slothmc-ctrl/TeamsysV0.1.git@75446eff98bd2e7b81193c9da2411a8923d89ddf#egg=teamsys
tenacity==9.1.2
termcolor==3.1.0
threadpoolctl==3.6.0
tiktoken==0.9.0
tokenizers==0.21.1
toml==0.10.2
tomli==2.2.1
tomlkit==0.13.2
torch==2.7.1
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
transformers==4.52.4
trove-classifiers==2025.5.9.12
truststore==0.10.1
typer==0.15.2
types-requests==2.32.0.20250306
typing-inspect==0.9.0
typing-inspection==0.4.0
typing_extensions==4.14.1
tzdata==2025.2
uritemplate==4.1.1
urllib3==2.3.0
uv==0.7.8
uvicorn==0.35.0
validators==0.35.0
virtualenv==20.31.2
watchdog==6.0.0
watchfiles==1.0.4
wcwidth==0.2.13
websocket-client==1.8.0
websockets==14.2
Werkzeug==3.1.3
wikipedia==1.4.0
wrapt==1.17.2
xxhash==3.5.0
xyzservices==2025.1.0
yarl==1.18.3
zipp==3.21.0
zstandard==0.23.0
