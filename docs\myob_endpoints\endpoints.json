{"myob_exo_api": {"base_uri": "http://***********:8888", "cloud_uri": "https://exo.api.myob.com", "description": "MYOB EXO API provides an interface for apps to connect and interact with EXO Business databases", "version": "2015.1+", "license_required": true}, "endpoints": [{"rel": "collection/endpoint", "title": "Endpoints", "href": "http://***********:8888/discovery", "methods": ["GET"], "description": "Discovery endpoint for listing all available endpoints"}, {"rel": "collection/contact", "title": "Contacts", "href": "http://***********:8888/contact", "methods": ["GET", "PUT", "POST"], "description": "Manage contact information"}, {"rel": "collection/contact", "title": "Contact Search", "href": "http://***********:8888/contact/search", "methods": ["GET"], "description": "Search contacts with query parameter", "parameters": ["q={query}"]}, {"rel": "collection/company", "title": "Companies", "href": "http://***********:8888/company"}, {"rel": "collection/company", "title": "Company Search", "href": "http://***********:8888/company/search"}, {"rel": "collection/prospect", "title": "Prospects", "href": "http://***********:8888/prospect"}, {"rel": "collection/prospect", "title": "Prospect Search", "href": "http://***********:8888/prospect/search"}, {"rel": "collection/prospecthistorynote", "title": "Prospect History Notes", "href": "http://***********:8888/prospect/%7Bprospectid%7D/historynote/%7Bid%7D?action=historynote"}, {"rel": "collection/debtor", "title": "Debtor", "href": "http://***********:8888/debtor"}, {"rel": "collection/debtor", "title": "Debtor Search", "href": "http://***********:8888/debtor/search"}, {"rel": "collection/debtor", "title": "Debtor Transactions", "href": "http://***********:8888/debtor/%7Bdebtorid%7D/transaction/%7Bid%7D?action=transaction"}, {"rel": "collection/debtor", "title": "Debtor Transaction Report", "href": "http://***********:8888/debtor/%7Bdebtorid%7D/transaction/%7Bid%7D/report"}, {"rel": "collection/debtor", "title": "Debtor History Notes", "href": "http://***********:8888/debtor/%7Bdebtorid%7D/historynote/%7Bid%7D?action=historynote"}, {"rel": "collection/debtor", "title": "Debtor Report", "href": "http://***********:8888/debtor/%7Bid%7D/report"}, {"rel": "collection/accountgroup", "title": "AccountGroup", "href": "http://***********:8888/accountgroup"}, {"rel": "collection/baseprice", "title": "BasePrice", "href": "http://***********:8888/baseprice"}, {"rel": "collection/creditterm", "title": "CreditTerm", "href": "http://***********:8888/creditterm"}, {"rel": "collection/currency", "title": "<PERSON><PERSON><PERSON><PERSON>", "href": "http://***********:8888/currency"}, {"rel": "collection/paymenttype", "title": "PaymentType", "href": "http://***********:8888/paymenttype"}, {"rel": "collection/bom", "title": "BOM", "href": "http://***********:8888/bom"}, {"rel": "collection/staff", "title": "Staff", "href": "http://***********:8888/staff"}, {"rel": "collection/stockitem", "title": "StockItem", "href": "http://***********:8888/stockitem", "methods": ["GET", "PUT", "POST"], "description": "Return detailed information on stock items", "date_released": "2013-10-25", "date_updated": "2014-05-30", "additional_endpoints": [{"path": "/stockitem/{id}", "description": "Get specific stock item by ID"}, {"path": "/stockitem/search?q={query}", "description": "Search stock items"}, {"path": "/stockitem/{id}/image?height={h}&width={w}", "description": "Get stock item image with specified dimensions"}]}, {"rel": "collection/stockitemreport", "title": "StockItem Report", "href": "http://***********:8888/stockitem/%7Bstockcode%7D/report", "methods": ["GET"], "description": "Generate reports for specific stock items"}, {"rel": "collection/stockpricegroup", "title": "StockPriceGroup", "href": "http://***********:8888/stockpricegroup"}, {"rel": "collection/stockclassification", "title": "StockClassification", "href": "http://***********:8888/stockclassification"}, {"rel": "collection/stockunitofmeasure", "title": "StockUnitOfMeasure", "href": "http://***********:8888/stockunitofmeasure"}, {"rel": "collection/stockitemimage", "title": "StockItem Image", "href": "http://***********:8888/stockitem/%7Bstockcode%7D/image"}, {"rel": "collection/taxrate", "title": "TaxRate", "href": "http://***********:8888/taxrate"}, {"rel": "collection/stocklocation", "title": "StockLocation", "href": "http://***********:8888/stocklocation"}, {"rel": "collection/stockprimarygroup", "title": "StockPrimaryGroup", "href": "http://***********:8888/stockprimarygroup"}, {"rel": "collection/stocksecondarygroup", "title": "StockSecondaryGroup", "href": "http://***********:8888/stocksecondarygroup"}, {"rel": "collection/salesorder", "title": "Sales Order", "href": "http://***********:8888/salesorder", "methods": ["GET", "PUT", "POST"], "description": "Return information on Sales Orders", "date_released": "2014-10-25", "date_updated": "2014-11-14", "additional_endpoints": [{"path": "/salesorder/{salesorderid}", "description": "Get specific sales order"}, {"path": "/salesorder/{salesorderid}?overridelock=true", "description": "Override object locks on sales order for updates"}]}, {"rel": "collection/salesorder", "title": "Sales Order Search", "href": "http://***********:8888/salesorder/search", "methods": ["GET", "PUT", "POST"], "description": "Search sales orders that match the specified search string", "parameters": ["q={query}"]}, {"rel": "collection/salesorder", "title": "Sales Order Validate", "href": "http://***********:8888/salesorder/validate", "methods": ["GET", "PUT", "POST"], "description": "Validates a sales order before performing PUT or POST. Minimum required: Debtorid, Status, Stockcode (for non-BOM), Bomcode (for BOM items)", "validation_scenarios": ["Updating existing record - returns list of errors that would prevent successful PUT", "Creating new Sales Order - accepts minimum attributes and returns all other attributes for POST payload"]}, {"rel": "collection/salesorder", "title": "Sales Order Convert Quote", "href": "http://***********:8888/salesorder/convertquotetoorder", "methods": ["POST"], "description": "Converts specified sales order quote to full sales order. Requires status 3 (Quotation)", "payload_example": "{\"id\": 12345}"}, {"rel": "collection/salesorder", "title": "Sales Order Report", "href": "http://***********:8888/salesorder/%7Bid%7D/report", "methods": ["GET"], "description": "Generate reports for specific sales orders"}, {"rel": "collection/branch", "title": "Branch", "href": "http://***********:8888/branch"}, {"rel": "collection/computer", "title": "Computer", "href": "http://***********:8888/computer"}, {"rel": "collection/adverttype", "title": "AdvertType", "href": "http://***********:8888/adverttype"}, {"rel": "collection/communicationprocess", "title": "CommunicationProcess", "href": "http://***********:8888/communicationprocess"}, {"rel": "collection/CompanyDataFileInfo", "title": "CompanyDataFileInfo", "href": "http://***********:8888/companydatafileinfo"}, {"rel": "collection/CompanyDataFileInfo/image", "title": "Company Logo", "href": "http://***********:8888/companydatafileinfo/image"}, {"rel": "collection/commonphrases", "title": "CommonPhrases", "href": "http://***********:8888/commonphrases"}, {"rel": "collection/token", "title": "Token", "href": "http://***********:8888/Token"}, {"rel": "collection/searchTemplate", "title": "SearchTemplate", "href": "http://***********:8888/SearchTemplate"}, {"rel": "collection/geolocationTemplate", "title": "GeolocationTemplate", "href": "http://***********:8888/GeolocationTemplate"}, {"rel": "collection/stocksearchTemplate", "title": "StockSearchTemplate", "href": "http://***********:8888/StockSearchTemplate"}, {"rel": "collection/companysearchTemplate", "title": "CompanySearchTemplate", "href": "http://***********:8888/CompanySearchTemplate"}, {"rel": "collection/stock", "title": "Stock", "href": "http://***********:8888/stock", "methods": ["GET", "PUT", "POST"], "description": "Return brief details of Stock items and Bills of Materials. For detailed information, use stockitem and bom endpoints", "date_released": "2013-10-25", "date_updated": "2013-10-25"}, {"rel": "collection/stock", "title": "Stock Search", "href": "http://***********:8888/stock/search", "methods": ["GET", "PUT", "POST"], "description": "Search stock items that match the specified search string", "parameters": ["q={query}"]}, {"rel": "collection/activitytype", "title": "Activity Types", "href": "http://***********:8888/activitytype"}, {"rel": "collection/activitystatus", "title": "Activity Statuses", "href": "http://***********:8888/activitystatus"}, {"rel": "collection/activity", "title": "Activity", "href": "http://***********:8888/activity"}, {"rel": "collection/report", "title": "Report", "href": "http://***********:8888/report"}, {"rel": "collection/report", "title": "Run Report", "href": "http://***********:8888/report/run/%7Breportname%7D"}, {"rel": "collection/report", "title": "Fetch Report", "href": "http://***********:8888/report/fetch/%7Bid%7D"}, {"rel": "collection/jobstatus", "title": "Job Status", "href": "http://***********:8888/jobstatus"}, {"rel": "collection/jobtype", "title": "Job Types", "href": "http://***********:8888/jobtype"}, {"rel": "collection/jobcategory", "title": "Job Categories", "href": "http://***********:8888/jobcategory"}, {"rel": "collection/jobflagdescription", "title": "Job Flag Description", "href": "http://***********:8888/jobflagdescription"}, {"rel": "collection/jobproject", "title": "Job Projects", "href": "http://***********:8888/jobproject"}, {"rel": "collection/job", "title": "Job Sheet Report", "href": "http://***********:8888/job/%7Bid%7D/report"}, {"rel": "collection/customtable", "title": "Custom Tables", "href": "http://***********:8888/customtable"}, {"rel": "collection/stock/sale", "title": "Stock Sale", "href": "http://***********:8888/stock/sale"}, {"rel": "collection/stock/receipt", "title": "Stock Receipt", "href": "http://***********:8888/stock/receipt"}, {"rel": "collection/stock/transfer", "title": "Stock Transfer", "href": "http://***********:8888/stock/transfer"}, {"rel": "collection/stock/adjustin", "title": "Stock AdjustIn", "href": "http://***********:8888/stock/adjustin"}, {"rel": "collection/stock/adjustout", "title": "Stock AdjustOut", "href": "http://***********:8888/stock/adjustout"}, {"rel": "collection/stock/transaction", "title": "Stock Transactions", "href": "http://***********:8888/stock/transaction/%7Bledger_type%7D/%7Bid%7D"}, {"rel": "collection/periodstatus", "title": "Period Status", "href": "http://***********:8888/periodstatus"}, {"rel": "collection/ledger", "title": "Ledger", "href": "http://***********:8888/Ledger"}, {"rel": "collection/ledger/chart", "title": "Chart Of Accounts", "href": "http://***********:8888/ledger/chart", "methods": ["GET"], "description": "Access chart of accounts information"}, {"rel": "collection/purchaseorder", "title": "Purchase Order", "href": "http://***********:8888/purchaseorder", "methods": ["GET", "PUT", "POST"], "description": "Return purchase order information", "date_released": "2023-09-20", "date_updated": "2023-09-20", "additional_endpoints": [{"path": "/purchaseorder/{id}", "description": "Get specific purchase order"}, {"path": "/purchaseorder/search?q={query}", "description": "Search purchase orders that match specified search string"}]}], "stock_operations": {"description": "Stock-related operations available through the API", "endpoints": [{"operation": "get_stock", "endpoint": "/stock", "methods": ["GET"], "description": "Retrieve brief stock item details and BOMs"}, {"operation": "search_stock", "endpoint": "/stock/search", "methods": ["GET"], "parameters": ["q={query}"], "description": "Search for stock items matching query"}, {"operation": "get_stock_item_details", "endpoint": "/stockitem/{id}", "methods": ["GET"], "description": "Get detailed information for specific stock item"}, {"operation": "stock_transactions", "endpoints": ["/stock/sale", "/stock/receipt", "/stock/transfer", "/stock/adjustin", "/stock/adjustout", "/stock/transaction/{ledger_type}/{id}"], "methods": ["GET", "POST"], "description": "Various stock transaction operations"}]}, "order_operations": {"description": "Order validation and posting operations", "sales_orders": {"validate_order": {"endpoint": "/salesorder/validate/{salesorderid}", "methods": ["GET", "PUT", "POST"], "description": "Validate sales order before PUT/POST operations", "required_fields": ["debtorid", "status", "stockcode (non-BOM)", "bomcode (BOM items)"], "use_cases": ["Validate existing order for updates", "Validate new order creation with minimum attributes"]}, "create_order": {"endpoint": "/salesorder", "methods": ["POST"], "description": "Create new sales order", "recommendation": "Use VALIDATE endpoint first to get complete payload"}, "update_order": {"endpoint": "/salesorder/{id}", "methods": ["PUT"], "description": "Update existing sales order", "options": ["overridelock=true to override object locks"]}, "convert_quote": {"endpoint": "/salesorder/convertquotetoorder", "methods": ["POST"], "description": "Convert quote (status 3) to full sales order", "payload": "{\"id\": salesorder_id}"}}, "purchase_orders": {"create_order": {"endpoint": "/purchaseorder", "methods": ["POST"], "description": "Create new purchase order"}, "update_order": {"endpoint": "/purchaseorder/{id}", "methods": ["PUT"], "description": "Update existing purchase order"}, "search_orders": {"endpoint": "/purchaseorder/search", "methods": ["GET"], "parameters": ["q={query}"], "description": "Search purchase orders"}}}}