# API Documentation

This document provides comprehensive API documentation for the Multi-Agent Sales Support System.

## 🌐 Base URLs

- **Development**: `http://localhost:8000`
- **Production**: `https://your-domain.com`
- **MCP Server**: `http://localhost:8000` (same as main API)

## 🔐 Authentication

The API uses JWT tokens for authentication. Include the token in the Authorization header:

```http
Authorization: Bearer <your-jwt-token>
```

### Get Authentication Token

```http
POST /auth/token
Content-Type: application/json

{
  "username": "your-username",
  "password": "your-password"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}
```

## 📊 Health and Status Endpoints

### Health Check

```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "checks": {
    "database": true,
    "erp_api": true,
    "agents": true,
    "knowledge_graph": true
  },
  "version": "1.0.0"
}
```

### System Status

```http
GET /status
```

**Response:**
```json
{
  "system_running": true,
  "timestamp": "2024-01-15T10:30:00Z",
  "database_health": {
    "supabase_connected": true,
    "connection_pool_size": 10
  },
  "orchestrator_stats": {
    "active_workflows": 3,
    "total_workflows_processed": 150
  },
  "background_tasks": {
    "total_tasks": 4,
    "running_tasks": 4
  }
}
```

## 🤖 Agent Endpoints

### Execute Workflow

```http
POST /workflow/execute
Content-Type: application/json
Authorization: Bearer <token>

{
  "workflow_type": "email_to_support",
  "customer_email": "<EMAIL>",
  "input_data": {
    "subject": "Order inquiry",
    "body": "I need help with my order",
    "extracted_query": "Order status inquiry"
  },
  "priority": "medium",
  "session_id": "session-123"
}
```

**Response:**
```json
{
  "workflow_id": "wf_abc123",
  "status": "completed",
  "result": {
    "workflow_type": "email_to_support",
    "extracted_query": "Order status inquiry",
    "support_response": {
      "response": "Your order ORD001 is currently being processed...",
      "confidence": 0.95,
      "requires_escalation": false
    }
  },
  "steps_completed": ["extract_query", "get_insights", "process_support"],
  "execution_time_ms": 2500,
  "agent_interactions": [
    {
      "step": "extract_query",
      "agent": "email_monitor",
      "timestamp": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### Get Workflow Status

```http
GET /workflow/{workflow_id}/status
Authorization: Bearer <token>
```

**Response:**
```json
{
  "workflow_id": "wf_abc123",
  "status": "running",
  "steps_completed": ["extract_query", "get_insights"],
  "current_step": "process_support",
  "start_time": "2024-01-15T10:30:00Z"
}
```

### Cancel Workflow

```http
DELETE /workflow/{workflow_id}
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "message": "Workflow wf_abc123 cancelled successfully"
}
```

## 📧 Customer Support Endpoints

### Process Support Query

```http
POST /support/query
Content-Type: application/json
Authorization: Bearer <token>

{
  "customer_email": "<EMAIL>",
  "query": "What is the status of my order ORD001?",
  "priority": "medium",
  "context": {
    "previous_interaction_id": "int_123"
  }
}
```

**Response:**
```json
{
  "response": "Your order ORD001 was shipped on January 10th and should arrive by January 15th. You can track it using tracking number TRK123456.",
  "confidence": 0.95,
  "requires_escalation": false,
  "suggested_actions": ["track_package", "contact_shipping"],
  "referenced_orders": ["ORD001"],
  "referenced_skus": [],
  "processing_time_ms": 1200
}
```

### Get Customer Context

```http
GET /support/customer/{customer_email}/context
Authorization: Bearer <token>
```

**Response:**
```json
{
  "customer_email": "<EMAIL>",
  "recent_interactions": [
    {
      "id": "int_123",
      "type": "email",
      "content": "Order inquiry",
      "timestamp": "2024-01-14T15:30:00Z"
    }
  ],
  "order_history": [
    {
      "order_id": "ORD001",
      "status": "shipped",
      "total_amount": 150.00,
      "order_date": "2024-01-10T10:00:00Z"
    }
  ],
  "sku_mappings": [
    {
      "customer_sku": "CUST-001",
      "internal_sku": "PROD001"
    }
  ]
}
```

## 📦 Order Processing Endpoints

### Create Order

```http
POST /orders
Content-Type: application/json
Authorization: Bearer <token>

{
  "customer_email": "<EMAIL>",
  "items": [
    {
      "sku": "CUST-001",
      "quantity": 5,
      "unit_price": 20.00
    }
  ],
  "notes": "Rush order",
  "priority": "high"
}
```

**Response:**
```json
{
  "success": true,
  "order_id": "ORD002",
  "status": "pending",
  "message": "Order ORD002 created successfully",
  "total_amount": 100.00,
  "sku_mappings_applied": [
    {
      "customer_sku": "CUST-001",
      "internal_sku": "PROD001"
    }
  ],
  "estimated_delivery": "2024-01-20"
}
```

### Get Order Status

```http
GET /orders/{order_id}
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "order_id": "ORD002",
  "status": "processing",
  "customer_email": "<EMAIL>",
  "items": [
    {
      "sku": "PROD001",
      "quantity": 5,
      "unit_price": 20.00
    }
  ],
  "total_amount": 100.00,
  "order_date": "2024-01-15T10:30:00Z",
  "estimated_delivery": "2024-01-20"
}
```

### Update Order

```http
PUT /orders/{order_id}
Content-Type: application/json
Authorization: Bearer <token>

{
  "status": "shipped",
  "tracking_number": "TRK123456",
  "notes": "Shipped via express delivery"
}
```

**Response:**
```json
{
  "success": true,
  "order_id": "ORD002",
  "status": "shipped",
  "message": "Order ORD002 updated successfully"
}
```

## 🛒 Purchasing Endpoints

### Create Purchase Order

```http
POST /purchasing/orders
Content-Type: application/json
Authorization: Bearer <token>

{
  "items": [
    {
      "sku": "PROD001",
      "quantity": 100,
      "unit_cost": 15.00
    }
  ],
  "supplier": "ABC Supplies",
  "priority": "high",
  "notes": "Restock for high demand item"
}
```

**Response:**
```json
{
  "success": true,
  "purchase_order_id": "PO001",
  "message": "Purchase order PO001 created successfully",
  "total_amount": 1500.00,
  "items_count": 1,
  "estimated_delivery": "2024-01-25"
}
```

### Get Inventory Alerts

```http
GET /purchasing/alerts
Authorization: Bearer <token>
```

**Response:**
```json
{
  "alerts": [
    {
      "id": "alert_001",
      "internal_sku": "PROD001",
      "current_quantity": 5,
      "threshold_quantity": 10,
      "alert_type": "low_stock",
      "status": "active",
      "created_at": "2024-01-15T09:00:00Z"
    }
  ],
  "total_alerts": 1
}
```

## 🔗 SKU Mapping Endpoints

### Create SKU Mapping

```http
POST /sku-mapping
Content-Type: application/json
Authorization: Bearer <token>

{
  "customer_email": "<EMAIL>",
  "customer_sku": "CUST-002",
  "internal_sku": "PROD002",
  "description": "Customer's product code mapping"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "customer_email": "<EMAIL>",
    "customer_sku": "CUST-002",
    "internal_sku": "PROD002",
    "active": true
  }
}
```

### Get Customer SKU Mappings

```http
GET /sku-mapping/{customer_email}
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "customer_sku": "CUST-001",
      "internal_sku": "PROD001",
      "description": "Product A",
      "active": true
    },
    {
      "customer_sku": "CUST-002",
      "internal_sku": "PROD002",
      "description": "Product B",
      "active": true
    }
  ]
}
```

### Get Specific SKU Mapping

```http
GET /sku-mapping/{customer_email}/{customer_sku}
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "customer_sku": "CUST-001",
    "internal_sku": "PROD001",
    "description": "Product A"
  }
}
```

## 🧠 Knowledge Graph Endpoints

### Get Customer Insights

```http
GET /knowledge-graph/customer/{customer_email}/insights
Authorization: Bearer <token>
```

**Response:**
```json
{
  "customer_email": "<EMAIL>",
  "relationship_summary": {
    "total_orders": 5,
    "relationships_created": 15
  },
  "cross_sell_opportunities": [
    {
      "type": "product_recommendation",
      "recommended_product": {
        "sku": "PROD003",
        "name": "Related Product"
      },
      "confidence": 0.8,
      "basis": "Frequently bought together"
    }
  ],
  "knowledge_graph_relationships": [
    {
      "customer": {"email": "<EMAIL>"},
      "relationship": {"type": "PLACED_ORDER"},
      "target": {"order_id": "ORD001"}
    }
  ]
}
```

### Find Similar Customers

```http
GET /knowledge-graph/customer/{customer_email}/similar
Authorization: Bearer <token>
```

**Response:**
```json
{
  "similar_customers": [
    {
      "customer": {"email": "<EMAIL>"},
      "common_products": 3,
      "similarity_score": 0.75
    }
  ]
}
```

## 📈 Analytics Endpoints

### Get Agent Statistics

```http
GET /stats/agents
Authorization: Bearer <token>
```

**Response:**
```json
{
  "email_monitor": {
    "total_emails_processed": 150,
    "success_rate": 95.5,
    "average_processing_time_ms": 800
  },
  "customer_support": {
    "total_queries_processed": 200,
    "average_confidence": 0.87,
    "escalation_rate": 5.2
  },
  "order_processing": {
    "total_orders_processed": 75,
    "successful_orders": 72,
    "success_rate": 96.0
  },
  "purchasing": {
    "active_alerts": 3,
    "total_purchase_orders": 25,
    "auto_purchasing_enabled": false
  }
}
```

### Get System Metrics

```http
GET /stats/system
Authorization: Bearer <token>
```

**Response:**
```json
{
  "uptime_seconds": 86400,
  "total_requests": 1500,
  "average_response_time_ms": 250,
  "error_rate": 0.5,
  "active_connections": 25,
  "memory_usage_mb": 512,
  "cpu_usage_percent": 15.5
}
```

## ❌ Error Responses

All endpoints return consistent error responses:

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "customer_email",
      "issue": "Invalid email format"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_abc123"
}
```

### Common Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `VALIDATION_ERROR` | 400 | Invalid input data |
| `UNAUTHORIZED` | 401 | Authentication required |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `NOT_FOUND` | 404 | Resource not found |
| `RATE_LIMITED` | 429 | Too many requests |
| `INTERNAL_ERROR` | 500 | Internal server error |
| `SERVICE_UNAVAILABLE` | 503 | Service temporarily unavailable |

## 📝 Rate Limiting

API endpoints are rate limited to ensure fair usage:

- **General endpoints**: 100 requests per minute
- **Workflow execution**: 10 requests per minute
- **Analytics endpoints**: 50 requests per minute

Rate limit headers are included in responses:

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248600
```

## 🔄 Webhooks

The system supports webhooks for real-time notifications:

### Register Webhook

```http
POST /webhooks
Content-Type: application/json
Authorization: Bearer <token>

{
  "url": "https://your-app.com/webhook",
  "events": ["order.created", "order.updated", "alert.created"],
  "secret": "your-webhook-secret"
}
```

### Webhook Events

- `order.created`: New order created
- `order.updated`: Order status changed
- `alert.created`: New inventory alert
- `workflow.completed`: Workflow execution completed
- `escalation.created`: Support escalation created

### Webhook Payload Example

```json
{
  "event": "order.created",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "order_id": "ORD002",
    "customer_email": "<EMAIL>",
    "total_amount": 100.00
  },
  "signature": "sha256=abc123..."
}
```

This API documentation provides comprehensive coverage of all endpoints in the Multi-Agent Sales Support System, including request/response formats, authentication, error handling, and webhook support.
