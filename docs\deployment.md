# Deployment Guide

This guide covers deploying the Multi-Agent Sales Support System in production environments.

## 🚀 Deployment Options

### 1. Docker Deployment (Recommended)

#### Prerequisites
- Docker 20.10+
- Docker Compose 2.0+
- 4GB+ RAM
- 20GB+ disk space

#### Quick Docker Deployment

```bash
# Clone repository
git clone <repository-url>
cd multi-agent-sales-support

# Copy and configure environment
cp .env.example .env
# Edit .env with production values

# Build and start services
docker-compose up -d
```

#### Docker Compose Configuration

```yaml
# docker-compose.yml
version: '3.8'

services:
  sales-support:
    build: .
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
    env_file:
      - .env
    depends_on:
      - falkordb
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  mcp-server:
    build:
      context: .
      dockerfile: Dockerfile.mcp
    ports:
      - "8001:8001"
    env_file:
      - .env
    restart: unless-stopped

  falkordb:
    image: falkordb/falkordb:latest
    ports:
      - "6379:6379"
    volumes:
      - falkordb_data:/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  falkordb_data:
  redis_data:
```

#### Dockerfile

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Start application
CMD ["python", "main.py"]
```

### 2. Kubernetes Deployment

#### Prerequisites
- Kubernetes 1.20+
- kubectl configured
- Helm 3.0+ (optional)

#### Kubernetes Manifests

```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: sales-support

---
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: sales-support-config
  namespace: sales-support
data:
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  MCP_SERVER_HOST: "0.0.0.0"
  MCP_SERVER_PORT: "8000"

---
# k8s/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: sales-support-secrets
  namespace: sales-support
type: Opaque
stringData:
  MISTRAL_API_KEY: "your-mistral-key"
  OPENAI_API_KEY: "your-openai-key"
  SUPABASE_URL: "your-supabase-url"
  SUPABASE_SERVICE_KEY: "your-supabase-key"
  MYOB_EXO_API_URL: "your-myob-url"
  MYOB_EXO_API_KEY: "your-myob-key"

---
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sales-support
  namespace: sales-support
spec:
  replicas: 2
  selector:
    matchLabels:
      app: sales-support
  template:
    metadata:
      labels:
        app: sales-support
    spec:
      containers:
      - name: sales-support
        image: sales-support:latest
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: sales-support-config
        - secretRef:
            name: sales-support-secrets
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"

---
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: sales-support-service
  namespace: sales-support
spec:
  selector:
    app: sales-support
  ports:
  - port: 80
    targetPort: 8000
  type: ClusterIP

---
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: sales-support-ingress
  namespace: sales-support
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: sales-support.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: sales-support-service
            port:
              number: 80
```

#### Deploy to Kubernetes

```bash
# Apply manifests
kubectl apply -f k8s/

# Check deployment status
kubectl get pods -n sales-support
kubectl get services -n sales-support

# View logs
kubectl logs -f deployment/sales-support -n sales-support
```

### 3. Cloud Platform Deployment

#### AWS ECS Deployment

```json
{
  "family": "sales-support",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::account:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "sales-support",
      "image": "your-account.dkr.ecr.region.amazonaws.com/sales-support:latest",
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "ENVIRONMENT",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "MISTRAL_API_KEY",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:sales-support/mistral-key"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/sales-support",
          "awslogs-region": "us-west-2",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"],
        "interval": 30,
        "timeout": 5,
        "retries": 3
      }
    }
  ]
}
```

#### Google Cloud Run Deployment

```yaml
# cloud-run.yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: sales-support
  annotations:
    run.googleapis.com/ingress: all
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: "10"
        run.googleapis.com/cpu-throttling: "false"
    spec:
      containerConcurrency: 80
      containers:
      - image: gcr.io/your-project/sales-support:latest
        ports:
        - containerPort: 8000
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: MISTRAL_API_KEY
          valueFrom:
            secretKeyRef:
              name: sales-support-secrets
              key: mistral-api-key
        resources:
          limits:
            cpu: "2"
            memory: "4Gi"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
```

## 🔧 Production Configuration

### Environment Variables

```bash
# Production .env file
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# API Keys (use secrets management)
MISTRAL_API_KEY=your-production-mistral-key
OPENAI_API_KEY=your-production-openai-key

# Database
SUPABASE_URL=your-production-supabase-url
SUPABASE_SERVICE_KEY=your-production-supabase-key

# ERP Integration
MYOB_EXO_API_URL=your-production-myob-url
MYOB_EXO_API_KEY=your-production-myob-key

# Email (if enabled)
GOOGLE_CLIENT_ID=your-production-google-client-id
GOOGLE_CLIENT_SECRET=your-production-google-client-secret

# Knowledge Graph (if enabled)
FALKORDB_HOST=falkordb-service
FALKORDB_PORT=6379
FALKORDB_PASSWORD=your-falkordb-password

# Feature Flags
ENABLE_EMAIL_MONITORING=true
ENABLE_INVENTORY_MONITORING=true
ENABLE_KNOWLEDGE_GRAPH=true
ENABLE_AUTO_PURCHASING=false
ENABLE_MCP_SERVER=true

# Performance Settings
EMAIL_CHECK_INTERVAL=300
INVENTORY_CHECK_INTERVAL=600
SUPPORT_CONTEXT_LIMIT=10
ORDER_PROCESSING_TIMEOUT=60
```

### Security Configuration

#### SSL/TLS Setup

```nginx
# nginx.conf
server {
    listen 443 ssl http2;
    server_name sales-support.yourdomain.com;

    ssl_certificate /etc/ssl/certs/sales-support.crt;
    ssl_certificate_key /etc/ssl/private/sales-support.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    location / {
        proxy_pass http://sales-support-backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

upstream sales-support-backend {
    server sales-support:8000;
}
```

#### API Rate Limiting

```python
# In main.py or middleware
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

@app.get("/api/workflow")
@limiter.limit("10/minute")
async def process_workflow(request: Request):
    # Workflow processing logic
    pass
```

## 📊 Monitoring and Observability

### Health Checks

```python
# Enhanced health check endpoint
@app.get("/health")
async def health_check():
    checks = {
        "database": await check_database_health(),
        "erp_api": await check_erp_health(),
        "agents": await check_agents_health(),
        "knowledge_graph": await check_kg_health()
    }
    
    overall_status = "healthy" if all(checks.values()) else "unhealthy"
    
    return {
        "status": overall_status,
        "timestamp": datetime.utcnow().isoformat(),
        "checks": checks,
        "version": "1.0.0"
    }
```

### Logging Configuration

```python
# Production logging setup
import structlog
import logging.config

LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "json": {
            "()": structlog.stdlib.ProcessorFormatter,
            "processor": structlog.dev.ConsoleRenderer(),
        },
    },
    "handlers": {
        "default": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "json",
        },
        "file": {
            "level": "INFO",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": "/var/log/sales-support/app.log",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
            "formatter": "json",
        },
    },
    "loggers": {
        "": {
            "handlers": ["default", "file"],
            "level": "INFO",
            "propagate": False,
        },
    },
}

logging.config.dictConfig(LOGGING_CONFIG)
```

### Metrics Collection

```python
# Prometheus metrics
from prometheus_client import Counter, Histogram, Gauge, generate_latest

# Metrics
workflow_counter = Counter('workflows_total', 'Total workflows processed', ['type', 'status'])
workflow_duration = Histogram('workflow_duration_seconds', 'Workflow processing time')
active_agents = Gauge('active_agents', 'Number of active agents')

@app.get("/metrics")
async def metrics():
    return Response(generate_latest(), media_type="text/plain")
```

## 🔄 CI/CD Pipeline

### GitHub Actions

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    - name: Run tests
      run: |
        python run_tests.py all
    - name: Run validation
      run: |
        python validate_system.py

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Build Docker image
      run: |
        docker build -t sales-support:${{ github.sha }} .
        docker tag sales-support:${{ github.sha }} sales-support:latest
    - name: Push to registry
      run: |
        echo ${{ secrets.DOCKER_PASSWORD }} | docker login -u ${{ secrets.DOCKER_USERNAME }} --password-stdin
        docker push sales-support:${{ github.sha }}
        docker push sales-support:latest

  deploy:
    needs: build
    runs-on: ubuntu-latest
    steps:
    - name: Deploy to production
      run: |
        # Deployment commands here
        kubectl set image deployment/sales-support sales-support=sales-support:${{ github.sha }}
        kubectl rollout status deployment/sales-support
```

## 🚨 Backup and Recovery

### Database Backup

```bash
#!/bin/bash
# backup-database.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/supabase"

# Create backup
pg_dump $SUPABASE_DATABASE_URL > $BACKUP_DIR/backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/backup_$DATE.sql

# Clean old backups (keep last 30 days)
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +30 -delete

echo "Backup completed: backup_$DATE.sql.gz"
```

### Configuration Backup

```bash
#!/bin/bash
# backup-config.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/config"

# Backup environment configuration
kubectl get configmap sales-support-config -o yaml > $BACKUP_DIR/configmap_$DATE.yaml
kubectl get secret sales-support-secrets -o yaml > $BACKUP_DIR/secrets_$DATE.yaml

echo "Configuration backup completed"
```

## 📈 Scaling Considerations

### Horizontal Scaling

```yaml
# k8s/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: sales-support-hpa
  namespace: sales-support
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: sales-support
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### Load Balancing

```yaml
# Load balancer configuration
apiVersion: v1
kind: Service
metadata:
  name: sales-support-lb
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
spec:
  type: LoadBalancer
  selector:
    app: sales-support
  ports:
  - port: 80
    targetPort: 8000
```

This deployment guide provides comprehensive instructions for deploying the Multi-Agent Sales Support System in production environments with proper security, monitoring, and scaling considerations.
