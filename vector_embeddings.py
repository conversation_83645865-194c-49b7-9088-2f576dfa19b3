#!/usr/bin/env python3
"""
Vector Embeddings Module for Email Analysis
Provides semantic search and similarity matching capabilities
"""

import os
import asyncio
import logging
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional

import numpy as np
from sentence_transformers import SentenceTransformer
from supabase import create_client
from dotenv import load_dotenv

# Load environment
load_dotenv()

logger = logging.getLogger(__name__)

class VectorEmbeddingManager:
    """Manage vector embeddings for email content and analysis."""
    
    def __init__(self):
        """Initialize the vector embedding manager."""
        self.model = None
        self.supabase = create_client(
            os.getenv("SUPABASE_URL"),
            os.getenv("SUPABASE_SERVICE_KEY")
        )
        self._initialize_model()
    
    def _initialize_model(self):
        """Initialize the sentence transformer model."""
        try:
            # Use a lightweight but effective model
            self.model = SentenceTransformer('all-MiniLM-L6-v2')
            logger.info("[EMBEDDINGS] Sentence transformer model loaded successfully")
        except Exception as e:
            logger.error(f"[EMBEDDINGS] Failed to load model: {e}")
            self.model = None
    
    def generate_embedding(self, text: str) -> Optional[List[float]]:
        """Generate vector embedding for text."""
        if not self.model or not text:
            return None
        
        try:
            # Generate embedding
            embedding = self.model.encode(text)
            
            # Convert to list for JSON serialization
            return embedding.tolist()
            
        except Exception as e:
            logger.error(f"[EMBEDDINGS] Error generating embedding: {e}")
            return None
    
    def generate_email_embedding(self, email_data: Dict[str, Any]) -> Optional[List[float]]:
        """Generate embedding for email content including metadata."""
        try:
            # Combine email content for embedding
            content_parts = []
            
            # Add subject
            if email_data.get('subject'):
                content_parts.append(f"Subject: {email_data['subject']}")
            
            # Add body content
            if email_data.get('content'):
                content_parts.append(f"Content: {email_data['content']}")
            
            # Add analysis summary if available
            analysis = email_data.get('analysis', {})
            if isinstance(analysis, dict) and analysis.get('summary'):
                content_parts.append(f"Summary: {analysis['summary']}")
            
            # Add intent and topics
            if analysis.get('intent'):
                content_parts.append(f"Intent: {analysis['intent']}")
            
            if analysis.get('topics'):
                topics = analysis['topics'] if isinstance(analysis['topics'], list) else []
                content_parts.append(f"Topics: {', '.join(topics)}")
            
            # Combine all parts
            combined_text = " | ".join(content_parts)
            
            return self.generate_embedding(combined_text)
            
        except Exception as e:
            logger.error(f"[EMBEDDINGS] Error generating email embedding: {e}")
            return None
    
    async def store_email_with_embedding(self, email_data: Dict[str, Any], analysis: Dict[str, Any]) -> bool:
        """Store email with vector embedding in Supabase."""
        try:
            # Generate embedding
            embedding_data = {
                'subject': email_data.get('subject', ''),
                'content': email_data.get('content', ''),
                'analysis': analysis
            }
            
            embedding = self.generate_email_embedding(embedding_data)
            
            if not embedding:
                logger.warning("[EMBEDDINGS] Could not generate embedding, storing without it")
            
            # Prepare data for storage
            storage_data = {
                "customer_email": email_data.get('from', email_data.get('customer_email', 'unknown')),
                "interaction_type": "email",
                "content": email_data.get('content', ''),
                "metadata": {
                    "subject": email_data.get('subject', ''),
                    "analysis": analysis,
                    "email_id": email_data.get('email_id', ''),
                    "thread_id": email_data.get('thread_id', ''),
                    "review_status": "approved",
                    "reviewed_at": datetime.now(timezone.utc).isoformat(),
                    "has_embedding": embedding is not None
                },
                "embedding": embedding,
                "created_at": datetime.now(timezone.utc).isoformat()
            }
            
            # Store in Supabase
            result = self.supabase.table("customer_interactions").insert(storage_data).execute()
            
            if result.data:
                logger.info(f"[EMBEDDINGS] Stored email with embedding: {email_data.get('subject', 'No subject')}")
                return True
            else:
                logger.error(f"[EMBEDDINGS] Failed to store email: {result}")
                return False
                
        except Exception as e:
            logger.error(f"[EMBEDDINGS] Error storing email with embedding: {e}")
            return False
    
    def find_similar_emails(self, query_text: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Find similar emails using vector similarity."""
        try:
            if not self.model:
                logger.warning("[EMBEDDINGS] Model not available for similarity search")
                return []
            
            # Generate embedding for query
            query_embedding = self.generate_embedding(query_text)
            if not query_embedding:
                return []
            
            # Get emails with embeddings from Supabase
            # Note: This is a simplified approach. In production, you'd use pgvector for efficient similarity search
            result = self.supabase.table("customer_interactions").select(
                "id, customer_email, content, metadata, embedding"
            ).not_.is_("embedding", "null").limit(100).execute()
            
            if not result.data:
                return []
            
            # Calculate similarities
            similarities = []
            for email in result.data:
                if email.get('embedding'):
                    try:
                        email_embedding = np.array(email['embedding'])
                        query_embedding_np = np.array(query_embedding)
                        
                        # Calculate cosine similarity
                        similarity = np.dot(query_embedding_np, email_embedding) / (
                            np.linalg.norm(query_embedding_np) * np.linalg.norm(email_embedding)
                        )
                        
                        similarities.append({
                            'email': email,
                            'similarity': float(similarity)
                        })
                    except Exception as e:
                        logger.warning(f"[EMBEDDINGS] Error calculating similarity: {e}")
                        continue
            
            # Sort by similarity and return top results
            similarities.sort(key=lambda x: x['similarity'], reverse=True)
            return [item['email'] for item in similarities[:limit]]
            
        except Exception as e:
            logger.error(f"[EMBEDDINGS] Error finding similar emails: {e}")
            return []
    
    def get_embedding_stats(self) -> Dict[str, Any]:
        """Get statistics about stored embeddings."""
        try:
            # Count total emails
            total_result = self.supabase.table("customer_interactions").select("count").execute()
            total_emails = len(total_result.data) if total_result.data else 0
            
            # Count emails with embeddings
            embedding_result = self.supabase.table("customer_interactions").select(
                "count"
            ).not_.is_("embedding", "null").execute()
            emails_with_embeddings = len(embedding_result.data) if embedding_result.data else 0
            
            return {
                "total_emails": total_emails,
                "emails_with_embeddings": emails_with_embeddings,
                "embedding_coverage": (emails_with_embeddings / total_emails * 100) if total_emails > 0 else 0,
                "model_loaded": self.model is not None
            }
            
        except Exception as e:
            logger.error(f"[EMBEDDINGS] Error getting embedding stats: {e}")
            return {
                "total_emails": 0,
                "emails_with_embeddings": 0,
                "embedding_coverage": 0,
                "model_loaded": False
            }

# Global instance
embedding_manager = VectorEmbeddingManager()

async def store_approved_email(email_data: Dict[str, Any], analysis: Dict[str, Any]) -> bool:
    """Store an approved email with vector embedding."""
    return await embedding_manager.store_email_with_embedding(email_data, analysis)

def find_similar_emails(query: str, limit: int = 5) -> List[Dict[str, Any]]:
    """Find emails similar to the query."""
    return embedding_manager.find_similar_emails(query, limit)

def get_embedding_statistics() -> Dict[str, Any]:
    """Get embedding system statistics."""
    return embedding_manager.get_embedding_stats()

if __name__ == "__main__":
    # Test the embedding system
    print("🧪 Testing Vector Embedding System")
    
    # Test model loading
    stats = get_embedding_statistics()
    print(f"Model loaded: {stats['model_loaded']}")
    print(f"Total emails: {stats['total_emails']}")
    print(f"Emails with embeddings: {stats['emails_with_embeddings']}")
    print(f"Embedding coverage: {stats['embedding_coverage']:.1f}%")
    
    # Test embedding generation
    test_text = "Customer inquiry about order status and delivery"
    embedding = embedding_manager.generate_embedding(test_text)
    print(f"Generated embedding for test text: {len(embedding) if embedding else 0} dimensions")
    
    # Test similarity search
    similar = find_similar_emails("order inquiry", limit=3)
    print(f"Found {len(similar)} similar emails")
    
    print("✅ Vector embedding system test complete")
