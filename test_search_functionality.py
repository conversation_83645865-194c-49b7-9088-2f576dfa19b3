#!/usr/bin/env python3
"""
Test script for the enhanced search functionality in human review dashboard
"""

import asyncio
import os
from dotenv import load_dotenv
from human_review_dashboard import HumanReviewDashboard

# Load environment
load_dotenv()

async def test_search_functions():
    """Test the various search functions."""
    print("🧪 Testing Human Review Dashboard Search Functionality")
    print("=" * 60)
    
    # Initialize dashboard
    try:
        dashboard = HumanReviewDashboard()
        print("✅ Dashboard initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize dashboard: {e}")
        return
    
    # Test 1: Content Search
    print("\n📝 Testing Content Search...")
    try:
        results = await dashboard.search_emails("order", "content", hours=168)
        print(f"✅ Content search returned {len(results)} results")
    except Exception as e:
        print(f"❌ Content search failed: {e}")
    
    # Test 2: Sender Search
    print("\n👤 Testing Sender Search...")
    try:
        results = await dashboard.search_emails("@teamsystems", "sender", hours=168)
        print(f"✅ Sender search returned {len(results)} results")
    except Exception as e:
        print(f"❌ Sender search failed: {e}")
    
    # Test 3: Subject Search
    print("\n📋 Testing Subject Search...")
    try:
        results = await dashboard.search_emails("invoice", "subject", hours=168)
        print(f"✅ Subject search returned {len(results)} results")
    except Exception as e:
        print(f"❌ Subject search failed: {e}")
    
    # Test 4: Intent Search
    print("\n🎯 Testing Intent Search...")
    try:
        results = await dashboard.search_emails("inquiry", "intent", hours=168)
        print(f"✅ Intent search returned {len(results)} results")
    except Exception as e:
        print(f"❌ Intent search failed: {e}")
    
    # Test 5: Urgency Search
    print("\n⚡ Testing Urgency Search...")
    try:
        results = await dashboard.search_emails("high", "urgency", hours=168)
        print(f"✅ Urgency search returned {len(results)} results")
    except Exception as e:
        print(f"❌ Urgency search failed: {e}")
    
    # Test 6: Semantic Search (if available)
    print("\n🧠 Testing Semantic Search...")
    try:
        results = await dashboard.search_emails("delivery problem", "semantic", hours=168)
        print(f"✅ Semantic search returned {len(results)} results")
    except Exception as e:
        print(f"❌ Semantic search failed: {e}")
    
    # Test 7: Multi-field Search
    print("\n🔄 Testing Multi-field Search...")
    try:
        results = await dashboard.multi_field_search("payment", hours=168)
        print(f"✅ Multi-field search returned {len(results)} results")
    except Exception as e:
        print(f"❌ Multi-field search failed: {e}")
    
    # Test 8: Quick Search
    print("\n⚡ Testing Quick Search...")
    try:
        results = await dashboard.quick_search("customer")
        print(f"✅ Quick search returned {len(results)} results")
    except Exception as e:
        print(f"❌ Quick search failed: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 Search functionality testing completed!")
    print("\nTo use the enhanced dashboard, run:")
    print("python human_review_dashboard.py")
    print("\nNew features available:")
    print("- Option 4: 🔍 Search Emails (Advanced search interface)")
    print("- Multiple search types: content, sender, subject, intent, urgency, semantic")
    print("- Time range filtering: 24h, 7d, 30d, all")
    print("- Multi-field search across all fields")
    print("- Search result review and analysis")

async def test_database_connection():
    """Test if database connection works."""
    print("\n🔌 Testing Database Connection...")
    try:
        dashboard = HumanReviewDashboard()
        emails = await dashboard.fetch_recent_emails(1)  # Just fetch 1 email
        print(f"✅ Database connection successful - found {len(emails)} recent emails")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("Make sure your .env file has correct SUPABASE_URL and SUPABASE_SERVICE_KEY")
        return False

async def main():
    """Main test function."""
    # Test database connection first
    if await test_database_connection():
        # Run search tests
        await test_search_functions()
    else:
        print("❌ Cannot run search tests without database connection")

if __name__ == "__main__":
    asyncio.run(main())
