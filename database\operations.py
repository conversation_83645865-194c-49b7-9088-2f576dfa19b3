"""
Database operations for the multi-agent sales support system.
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from database.supabase_setup import get_supabase_client, get_vector_store
from database.schemas import (
    CustomerInteraction, SKUMapping, AgentMemory, OrderRecord,
    SupportTicket, InventoryAlert, EmailProcessingLog, SystemMetrics, APIUsageLog
)

logger = logging.getLogger(__name__)


class DatabaseOperations:
    """Database operations manager."""
    
    def __init__(self):
        """Initialize database operations."""
        self.client = None
        self.vector_store = None
    
    def _get_client(self):
        """Get Supabase client."""
        if not self.client:
            self.client = get_supabase_client()
        return self.client
    
    def _get_vector_store(self):
        """Get vector store."""
        if not self.vector_store:
            self.vector_store = get_vector_store()
        return self.vector_store
    
    # Customer Interactions
    async def create_interaction(self, interaction: CustomerInteraction) -> Dict[str, Any]:
        """Create a new customer interaction."""
        try:
            client = self._get_client()
            data = interaction.model_dump(exclude={"id"}, exclude_none=True, mode='json')

            response = client.table("customer_interactions").insert(data).execute()
            logger.info(f"Created interaction for {interaction.customer_email}")
            return response.data[0] if response.data else {}

        except Exception as e:
            logger.error(f"Failed to create interaction: {e}")
            raise
    
    async def get_interactions(
        self,
        customer_email: Optional[str] = None,
        interaction_type: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get customer interactions."""
        try:
            client = self._get_client()
            query = client.table("customer_interactions").select("*")
            
            if customer_email:
                query = query.eq("customer_email", customer_email)
            
            if interaction_type:
                query = query.eq("interaction_type", interaction_type)
            
            query = query.order("created_at", desc=True).limit(limit)
            response = query.execute()
            
            return response.data
            
        except Exception as e:
            logger.error(f"Failed to get interactions: {e}")
            raise
    
    async def search_interactions_semantic(
        self,
        query_text: str,
        customer_email: Optional[str] = None,
        limit: int = 5,
        similarity_threshold: float = 0.8
    ) -> List[Dict[str, Any]]:
        """Search interactions using semantic similarity."""
        try:
            vector_store = self._get_vector_store()
            
            if not vector_store:
                logger.warning("Vector store not available")
                return []
            
            filter_dict = {"customer_email": customer_email} if customer_email else None
            
            results = vector_store.similarity_search_with_score(
                query_text,
                k=limit,
                filter=filter_dict
            )
            
            return [
                {
                    "content": doc.page_content,
                    "metadata": doc.metadata,
                    "similarity": score
                }
                for doc, score in results
                if score >= similarity_threshold
            ]
            
        except Exception as e:
            logger.error(f"Semantic search failed: {e}")
            return []
    
    # SKU Mappings
    async def create_sku_mapping(self, mapping: SKUMapping) -> Dict[str, Any]:
        """Create or update a SKU mapping."""
        try:
            client = self._get_client()
            data = mapping.model_dump(exclude={"id"}, exclude_none=True, mode='json')

            response = client.table("sku_mappings").upsert(data).execute()
            logger.info(f"Created SKU mapping: {mapping.customer_sku} -> {mapping.internal_sku}")
            return response.data[0] if response.data else {}
            
        except Exception as e:
            logger.error(f"Failed to create SKU mapping: {e}")
            raise
    
    async def get_sku_mapping(self, customer_email: str, customer_sku: str) -> Optional[Dict[str, Any]]:
        """Get SKU mapping."""
        try:
            client = self._get_client()
            
            response = client.table("sku_mappings").select("*").eq(
                "customer_email", customer_email
            ).eq("customer_sku", customer_sku).eq("active", True).execute()
            
            return response.data[0] if response.data else None
            
        except Exception as e:
            logger.error(f"Failed to get SKU mapping: {e}")
            return None
    
    async def get_customer_sku_mappings(self, customer_email: str) -> List[Dict[str, Any]]:
        """Get all SKU mappings for a customer."""
        try:
            client = self._get_client()
            
            response = client.table("sku_mappings").select("*").eq(
                "customer_email", customer_email
            ).eq("active", True).execute()
            
            return response.data
            
        except Exception as e:
            logger.error(f"Failed to get customer SKU mappings: {e}")
            return []
    
    # Agent Memory
    async def store_agent_memory(self, memory: AgentMemory) -> Dict[str, Any]:
        """Store agent memory."""
        try:
            client = self._get_client()
            data = memory.model_dump(exclude={"id"}, exclude_none=True, mode='json')

            response = client.table("agent_memory").insert(data).execute()
            logger.debug(f"Stored memory for agent {memory.agent_name}")
            return response.data[0] if response.data else {}
            
        except Exception as e:
            logger.error(f"Failed to store agent memory: {e}")
            raise
    
    async def get_agent_memory(
        self,
        agent_name: str,
        customer_email: Optional[str] = None,
        session_id: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get agent memory."""
        try:
            client = self._get_client()
            query = client.table("agent_memory").select("*").eq("agent_name", agent_name)
            
            if customer_email:
                query = query.eq("customer_email", customer_email)
            
            if session_id:
                query = query.eq("session_id", session_id)
            
            # Filter out expired memories
            query = query.or_("expires_at.is.null,expires_at.gt.now()")
            query = query.order("created_at", desc=True).limit(limit)
            
            response = query.execute()
            return response.data
            
        except Exception as e:
            logger.error(f"Failed to get agent memory: {e}")
            return []
    
    # Order Records
    async def create_order_record(self, order: OrderRecord) -> Dict[str, Any]:
        """Create an order record."""
        try:
            client = self._get_client()
            data = order.model_dump(exclude={"id"}, exclude_none=True, mode='json')

            response = client.table("order_records").insert(data).execute()
            logger.info(f"Created order record {order.order_id}")
            return response.data[0] if response.data else {}
            
        except Exception as e:
            logger.error(f"Failed to create order record: {e}")
            raise
    
    async def update_order_status(self, order_id: str, status: str, notes: Optional[str] = None) -> bool:
        """Update order status."""
        try:
            client = self._get_client()
            update_data = {"status": status, "updated_at": datetime.utcnow().isoformat()}
            
            if notes:
                update_data["notes"] = notes
            
            response = client.table("order_records").update(update_data).eq("order_id", order_id).execute()
            
            if response.data:
                logger.info(f"Updated order {order_id} status to {status}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Failed to update order status: {e}")
            return False
    
    async def get_customer_orders(self, customer_email: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get customer orders."""
        try:
            client = self._get_client()
            
            response = client.table("order_records").select("*").eq(
                "customer_email", customer_email
            ).order("created_at", desc=True).limit(limit).execute()
            
            return response.data
            
        except Exception as e:
            logger.error(f"Failed to get customer orders: {e}")
            return []
    
    # Support Tickets
    async def create_support_ticket(self, ticket: SupportTicket) -> Dict[str, Any]:
        """Create a support ticket."""
        try:
            client = self._get_client()
            data = ticket.model_dump(exclude={"id"}, exclude_none=True, mode='json')

            response = client.table("support_tickets").insert(data).execute()
            logger.info(f"Created support ticket {ticket.ticket_id}")
            return response.data[0] if response.data else {}
            
        except Exception as e:
            logger.error(f"Failed to create support ticket: {e}")
            raise
    
    async def update_ticket_escalation(self, ticket_id: str, escalation_level: str) -> bool:
        """Update ticket escalation level."""
        try:
            client = self._get_client()
            
            response = client.table("support_tickets").update({
                "escalation_level": escalation_level,
                "updated_at": datetime.utcnow().isoformat()
            }).eq("ticket_id", ticket_id).execute()
            
            if response.data:
                logger.info(f"Updated ticket {ticket_id} escalation to {escalation_level}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Failed to update ticket escalation: {e}")
            return False
    
    # Inventory Alerts
    async def create_inventory_alert(self, alert: InventoryAlert) -> Dict[str, Any]:
        """Create an inventory alert."""
        try:
            client = self._get_client()
            data = alert.model_dump(exclude={"id"}, exclude_none=True, mode='json')

            response = client.table("inventory_alerts").insert(data).execute()
            logger.info(f"Created inventory alert for SKU {alert.internal_sku}")
            return response.data[0] if response.data else {}
            
        except Exception as e:
            logger.error(f"Failed to create inventory alert: {e}")
            raise
    
    async def get_active_inventory_alerts(self) -> List[Dict[str, Any]]:
        """Get active inventory alerts."""
        try:
            client = self._get_client()
            
            response = client.table("inventory_alerts").select("*").eq(
                "status", "active"
            ).order("created_at", desc=True).execute()
            
            return response.data
            
        except Exception as e:
            logger.error(f"Failed to get inventory alerts: {e}")
            return []
    
    # Email Processing Log
    async def log_email_processing(self, log_entry: EmailProcessingLog) -> Dict[str, Any]:
        """Log email processing."""
        try:
            client = self._get_client()
            data = log_entry.model_dump(exclude={"id"}, exclude_none=True, mode='json')

            response = client.table("email_processing_log").insert(data).execute()
            return response.data[0] if response.data else {}

        except Exception as e:
            logger.error(f"Failed to log email processing: {e}")
            raise
    
    async def get_unprocessed_emails(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get unprocessed emails."""
        try:
            client = self._get_client()
            
            response = client.table("email_processing_log").select("*").eq(
                "processed", False
            ).order("created_at", asc=True).limit(limit).execute()
            
            return response.data
            
        except Exception as e:
            logger.error(f"Failed to get unprocessed emails: {e}")
            return []
    
    # System Metrics
    async def record_metric(self, metric: SystemMetrics) -> Dict[str, Any]:
        """Record a system metric."""
        try:
            client = self._get_client()
            data = metric.model_dump(exclude={"id"}, exclude_none=True, mode='json')

            response = client.table("system_metrics").insert(data).execute()
            return response.data[0] if response.data else {}
            
        except Exception as e:
            logger.error(f"Failed to record metric: {e}")
            raise
    
    # API Usage Log
    async def log_api_usage(self, usage_log: APIUsageLog) -> Dict[str, Any]:
        """Log API usage."""
        try:
            client = self._get_client()
            data = usage_log.model_dump(exclude={"id"}, exclude_none=True, mode='json')

            response = client.table("api_usage_log").insert(data).execute()
            return response.data[0] if response.data else {}
            
        except Exception as e:
            logger.error(f"Failed to log API usage: {e}")
            raise
    
    # Cleanup Operations
    async def cleanup_expired_memories(self) -> int:
        """Clean up expired agent memories."""
        try:
            client = self._get_client()
            
            response = client.table("agent_memory").delete().lt(
                "expires_at", datetime.utcnow().isoformat()
            ).execute()
            
            count = len(response.data) if response.data else 0
            logger.info(f"Cleaned up {count} expired memories")
            return count
            
        except Exception as e:
            logger.error(f"Failed to cleanup expired memories: {e}")
            return 0
    
    async def cleanup_old_interactions(self, days: int = 365) -> int:
        """Clean up old customer interactions."""
        try:
            client = self._get_client()
            cutoff_date = (datetime.utcnow() - timedelta(days=days)).isoformat()
            
            response = client.table("customer_interactions").delete().lt(
                "created_at", cutoff_date
            ).execute()
            
            count = len(response.data) if response.data else 0
            logger.info(f"Cleaned up {count} old interactions")
            return count
            
        except Exception as e:
            logger.error(f"Failed to cleanup old interactions: {e}")
            return 0


# Global database operations instance
_db_ops: Optional[DatabaseOperations] = None


def get_database_operations() -> DatabaseOperations:
    """Get the global database operations instance."""
    global _db_ops
    
    if _db_ops is None:
        _db_ops = DatabaseOperations()
    
    return _db_ops


async def setup_database_schema() -> None:
    """Set up the database schema."""
    # This is handled by the Supabase setup module
    from database.supabase_setup import setup_database_schema as setup_schema
    await setup_schema()
