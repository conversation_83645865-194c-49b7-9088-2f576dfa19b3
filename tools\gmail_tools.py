"""
Gmail API tools for the multi-agent sales support system.
"""

import base64
import logging
import os
import pickle
import json
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from email.mime.base import MIMEBase
from email import encoders

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from config.settings import get_settings
from database.operations import get_database_operations
from database.schemas import EmailProcessingLog

logger = logging.getLogger(__name__)


class GmailError(Exception):
    """Custom exception for Gmail operations."""
    pass


class GmailManager:
    """Manages Gmail API operations."""
    
    def __init__(self):
        """Initialize Gmail manager."""
        self.settings = get_settings()
        self.service = None
        self.credentials = None
        self.db_ops = get_database_operations()
    
    async def initialize(self) -> None:
        """Initialize Gmail API service."""
        try:
            self.credentials = await self._get_credentials()
            self.service = build('gmail', 'v1', credentials=self.credentials)
            logger.info("Gmail API service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Gmail API: {e}")
            raise GmailError(f"Gmail initialization failed: {e}")
    
    async def _get_credentials(self) -> Credentials:
        """Get or refresh Gmail API credentials using the official Google flow."""
        creds = None

        # Define file paths - following Google's official pattern
        token_json_file = "token.json"
        token_pickle_file = "token.pickle"  # Preferred for persistence
        credentials_file = "credentials.json"

        # Try to load from token.pickle first (more reliable)
        if os.path.exists(token_pickle_file):
            try:
                with open(token_pickle_file, 'rb') as token:
                    creds = pickle.load(token)
                logger.debug("Loaded credentials from token.pickle")
            except Exception as e:
                logger.warning(f"Failed to load token.pickle: {e}")
                creds = None

        # Fallback to token.json if pickle doesn't exist
        elif os.path.exists(token_json_file):
            try:
                creds = Credentials.from_authorized_user_file(token_json_file, self.settings.gmail_scopes)
                logger.debug("Loaded credentials from token.json")
            except Exception as e:
                logger.warning(f"Failed to load token.json: {e}")
                creds = None

        # If there are no (valid) credentials available, let the user log in
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                try:
                    creds.refresh(Request())
                    logger.info("Gmail credentials refreshed successfully")
                except Exception as e:
                    logger.warning(f"Failed to refresh credentials: {e}")
                    creds = None

            # Need to get new credentials
            if not creds:
                if not os.path.exists(credentials_file):
                    raise GmailError(
                        f"Gmail credentials file not found: {credentials_file}. "
                        "Please download credentials.json from Google Cloud Console and place it in the project root."
                    )

                try:
                    flow = InstalledAppFlow.from_client_secrets_file(
                        credentials_file, self.settings.gmail_scopes
                    )
                    creds = flow.run_local_server(port=8080)
                    logger.info("New Gmail credentials obtained through OAuth flow")
                except Exception as e:
                    raise GmailError(f"Failed to complete OAuth flow: {e}")

            # Save the credentials for the next run
            # Save both formats for compatibility
            try:
                # Save as pickle (preferred)
                with open(token_pickle_file, 'wb') as token:
                    pickle.dump(creds, token)
                logger.debug("Saved credentials to token.pickle")

                # Also save as JSON for compatibility
                with open(token_json_file, 'w') as token:
                    token.write(creds.to_json())
                logger.debug("Saved credentials to token.json")

            except Exception as e:
                logger.warning(f"Failed to save credentials: {e}")

        return creds
    
    async def get_messages(
        self,
        query: str = "",
        max_results: int = 10,
        include_spam_trash: bool = False
    ) -> List[Dict[str, Any]]:
        """Get Gmail messages based on query.
        
        Args:
            query: Gmail search query (e.g., "from:<EMAIL>")
            max_results: Maximum number of messages to return
            include_spam_trash: Whether to include spam and trash
            
        Returns:
            List of message metadata
        """
        try:
            if not self.service:
                await self.initialize()
            
            results = self.service.users().messages().list(
                userId='me',
                q=query,
                maxResults=max_results,
                includeSpamTrash=include_spam_trash
            ).execute()
            
            messages = results.get('messages', [])
            logger.debug(f"Found {len(messages)} messages for query: {query}")
            
            return messages
            
        except HttpError as e:
            logger.error(f"Gmail API error getting messages: {e}")
            raise GmailError(f"Failed to get messages: {e}")
        except Exception as e:
            logger.error(f"Error getting Gmail messages: {e}")
            raise GmailError(f"Failed to get messages: {e}")
    
    async def get_message_details(self, message_id: str) -> Dict[str, Any]:
        """Get detailed information about a specific message.
        
        Args:
            message_id: Gmail message ID
            
        Returns:
            Detailed message information
        """
        try:
            if not self.service:
                await self.initialize()
            
            message = self.service.users().messages().get(
                userId='me',
                id=message_id,
                format='full'
            ).execute()
            
            # Extract key information
            headers = message['payload'].get('headers', [])
            header_dict = {h['name']: h['value'] for h in headers}
            
            # Get message body
            body = self._extract_message_body(message['payload'])
            
            # Get attachments info
            attachments = self._get_attachment_info(message['payload'])
            
            message_details = {
                'id': message['id'],
                'thread_id': message['threadId'],
                'label_ids': message.get('labelIds', []),
                'snippet': message.get('snippet', ''),
                'headers': header_dict,
                'body': body,
                'attachments': attachments,
                'size_estimate': message.get('sizeEstimate', 0),
                'internal_date': message.get('internalDate', ''),
                'from': header_dict.get('From', ''),
                'to': header_dict.get('To', ''),
                'subject': header_dict.get('Subject', ''),
                'date': header_dict.get('Date', '')
            }
            
            logger.debug(f"Retrieved details for message {message_id}")

            # Create temp file for testing
            await self._create_temp_file(message_details)

            return message_details

        except HttpError as e:
            logger.error(f"Gmail API error getting message details: {e}")
            raise GmailError(f"Failed to get message details: {e}")
        except Exception as e:
            logger.error(f"Error getting message details: {e}")
            raise GmailError(f"Failed to get message details: {e}")
    
    def _extract_message_body(self, payload: Dict[str, Any]) -> str:
        """Extract message body from payload."""
        body = ""
        
        if 'parts' in payload:
            for part in payload['parts']:
                if part['mimeType'] == 'text/plain':
                    data = part['body'].get('data', '')
                    if data:
                        body = base64.urlsafe_b64decode(data).decode('utf-8')
                        break
                elif part['mimeType'] == 'text/html' and not body:
                    data = part['body'].get('data', '')
                    if data:
                        body = base64.urlsafe_b64decode(data).decode('utf-8')
        else:
            if payload['mimeType'] == 'text/plain':
                data = payload['body'].get('data', '')
                if data:
                    body = base64.urlsafe_b64decode(data).decode('utf-8')
        
        return body
    
    def _get_attachment_info(self, payload: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get attachment information from message payload."""
        attachments = []
        
        def extract_attachments(part):
            if 'parts' in part:
                for subpart in part['parts']:
                    extract_attachments(subpart)
            else:
                if part.get('filename') and part['body'].get('attachmentId'):
                    attachments.append({
                        'filename': part['filename'],
                        'mime_type': part['mimeType'],
                        'size': part['body'].get('size', 0),
                        'attachment_id': part['body']['attachmentId']
                    })
        
        extract_attachments(payload)
        return attachments
    
    async def download_attachment(
        self,
        message_id: str,
        attachment_id: str,
        filename: str
    ) -> bytes:
        """Download message attachment.
        
        Args:
            message_id: Gmail message ID
            attachment_id: Attachment ID
            filename: Attachment filename
            
        Returns:
            Attachment data as bytes
        """
        try:
            if not self.service:
                await self.initialize()
            
            attachment = self.service.users().messages().attachments().get(
                userId='me',
                messageId=message_id,
                id=attachment_id
            ).execute()
            
            data = attachment['data']
            file_data = base64.urlsafe_b64decode(data)
            
            logger.debug(f"Downloaded attachment {filename} ({len(file_data)} bytes)")
            return file_data
            
        except HttpError as e:
            logger.error(f"Gmail API error downloading attachment: {e}")
            raise GmailError(f"Failed to download attachment: {e}")
        except Exception as e:
            logger.error(f"Error downloading attachment: {e}")
            raise GmailError(f"Failed to download attachment: {e}")
    
    async def send_message(
        self,
        to: str,
        subject: str,
        body: str,
        cc: Optional[str] = None,
        bcc: Optional[str] = None,
        attachments: Optional[List[str]] = None
    ) -> str:
        """Send an email message.
        
        Args:
            to: Recipient email address
            subject: Email subject
            body: Email body
            cc: CC recipients (optional)
            bcc: BCC recipients (optional)
            attachments: List of file paths to attach (optional)
            
        Returns:
            Sent message ID
        """
        try:
            if not self.service:
                await self.initialize()
            
            # Create message
            message = MIMEMultipart()
            message['to'] = to
            message['subject'] = subject
            
            if cc:
                message['cc'] = cc
            if bcc:
                message['bcc'] = bcc
            
            # Add body
            message.attach(MIMEText(body, 'plain'))
            
            # Add attachments
            if attachments:
                for file_path in attachments:
                    if os.path.exists(file_path):
                        with open(file_path, 'rb') as attachment_file:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(attachment_file.read())
                        
                        encoders.encode_base64(part)
                        part.add_header(
                            'Content-Disposition',
                            f'attachment; filename= {os.path.basename(file_path)}'
                        )
                        message.attach(part)
            
            # Send message
            raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode()
            send_message = {'raw': raw_message}
            
            result = self.service.users().messages().send(
                userId='me',
                body=send_message
            ).execute()
            
            message_id = result['id']
            logger.info(f"Sent email to {to}, message ID: {message_id}")
            
            return message_id
            
        except HttpError as e:
            logger.error(f"Gmail API error sending message: {e}")
            raise GmailError(f"Failed to send message: {e}")
        except Exception as e:
            logger.error(f"Error sending message: {e}")
            raise GmailError(f"Failed to send message: {e}")
    
    async def mark_as_read(self, message_id: str) -> bool:
        """Mark a message as read.
        
        Args:
            message_id: Gmail message ID
            
        Returns:
            True if successful
        """
        try:
            if not self.service:
                await self.initialize()
            
            self.service.users().messages().modify(
                userId='me',
                id=message_id,
                body={'removeLabelIds': ['UNREAD']}
            ).execute()
            
            logger.debug(f"Marked message {message_id} as read")
            return True
            
        except HttpError as e:
            logger.error(f"Gmail API error marking message as read: {e}")
            return False
        except Exception as e:
            logger.error(f"Error marking message as read: {e}")
            return False
    
    async def add_label(self, message_id: str, label_name: str) -> bool:
        """Add a label to a message.
        
        Args:
            message_id: Gmail message ID
            label_name: Label name to add
            
        Returns:
            True if successful
        """
        try:
            if not self.service:
                await self.initialize()
            
            # Get or create label
            label_id = await self._get_or_create_label(label_name)
            
            if label_id:
                self.service.users().messages().modify(
                    userId='me',
                    id=message_id,
                    body={'addLabelIds': [label_id]}
                ).execute()
                
                logger.debug(f"Added label {label_name} to message {message_id}")
                return True
            
            return False
            
        except HttpError as e:
            logger.error(f"Gmail API error adding label: {e}")
            return False
        except Exception as e:
            logger.error(f"Error adding label: {e}")
            return False
    
    async def _get_or_create_label(self, label_name: str) -> Optional[str]:
        """Get or create a Gmail label.
        
        Args:
            label_name: Name of the label
            
        Returns:
            Label ID if successful
        """
        try:
            # Get existing labels
            labels_result = self.service.users().labels().list(userId='me').execute()
            labels = labels_result.get('labels', [])
            
            # Check if label exists
            for label in labels:
                if label['name'] == label_name:
                    return label['id']
            
            # Create new label
            label_object = {
                'name': label_name,
                'labelListVisibility': 'labelShow',
                'messageListVisibility': 'show'
            }
            
            created_label = self.service.users().labels().create(
                userId='me',
                body=label_object
            ).execute()
            
            logger.info(f"Created new Gmail label: {label_name}")
            return created_label['id']
            
        except Exception as e:
            logger.error(f"Error getting/creating label: {e}")
            return None
    
    async def log_email_processing(
        self,
        message_id: str,
        customer_email: str,
        subject: str,
        processed: bool = False,
        processing_status: str = "pending",
        extracted_query: Optional[str] = None,
        extracted_sku: Optional[str] = None,
        attachments_count: int = 0,
        error_message: Optional[str] = None
    ) -> None:
        """Log email processing to database."""
        try:
            log_entry = EmailProcessingLog(
                message_id=message_id,
                customer_email=customer_email,
                subject=subject,
                processed=processed,
                processing_status=processing_status,
                extracted_query=extracted_query,
                extracted_sku=extracted_sku,
                attachments_count=attachments_count,
                error_message=error_message
            )
            
            await self.db_ops.log_email_processing(log_entry)
            
        except Exception as e:
            logger.error(f"Failed to log email processing: {e}")

    async def _create_temp_file(self, message_details: Dict[str, Any]) -> None:
        """Create a temp file with email processing output for testing."""
        try:
            # Ensure temp directory exists
            temp_dir = "temp"
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)

            # Create filename with timestamp and message ID
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            message_id = message_details.get('id', 'unknown')[:8]  # First 8 chars of message ID
            filename = f"email_processing_{timestamp}_{message_id}.json"
            filepath = os.path.join(temp_dir, filename)

            # Prepare data for temp file
            temp_data = {
                "timestamp": datetime.now().isoformat(),
                "message_id": message_details.get('id'),
                "from": message_details.get('from'),
                "to": message_details.get('to'),
                "subject": message_details.get('subject'),
                "date": message_details.get('date'),
                "snippet": message_details.get('snippet'),
                "body_preview": message_details.get('body', '')[:500] + "..." if len(message_details.get('body', '')) > 500 else message_details.get('body', ''),
                "attachments_count": len(message_details.get('attachments', [])),
                "attachments": [att.get('filename', 'unknown') for att in message_details.get('attachments', [])],
                "size_estimate": message_details.get('size_estimate'),
                "label_ids": message_details.get('label_ids', []),
                "processing_status": "extracted_for_testing"
            }

            # Write to temp file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(temp_data, f, indent=2, ensure_ascii=False)

            logger.info(f"Created temp file for testing: {filepath}")

        except Exception as e:
            logger.error(f"Failed to create temp file: {e}")


# Global Gmail manager instance
_gmail_manager: Optional[GmailManager] = None


async def get_gmail_manager() -> GmailManager:
    """Get the global Gmail manager instance."""
    global _gmail_manager
    
    if _gmail_manager is None:
        _gmail_manager = GmailManager()
        await _gmail_manager.initialize()
    
    return _gmail_manager


async def get_unread_emails(query: str = "is:unread", max_results: int = 10) -> List[Dict[str, Any]]:
    """Get unread emails."""
    manager = await get_gmail_manager()
    return await manager.get_messages(query, max_results)


async def get_email_details(message_id: str) -> Dict[str, Any]:
    """Get detailed email information."""
    manager = await get_gmail_manager()
    return await manager.get_message_details(message_id)


async def download_email_attachment(
    message_id: str,
    attachment_id: str,
    filename: str
) -> bytes:
    """Download email attachment."""
    manager = await get_gmail_manager()
    return await manager.download_attachment(message_id, attachment_id, filename)


async def send_email(
    to: str,
    subject: str,
    body: str,
    cc: Optional[str] = None,
    bcc: Optional[str] = None,
    attachments: Optional[List[str]] = None
) -> str:
    """Send an email."""
    manager = await get_gmail_manager()
    return await manager.send_message(to, subject, body, cc, bcc, attachments)
