#!/usr/bin/env python3
"""
Test script to verify that all major dependencies are installed correctly.
"""

def test_imports():
    """Test importing all major packages."""
    print("🧪 Testing package imports...")
    
    try:
        # Core AI frameworks
        import pydantic_ai
        from pydantic_ai.models.mistral import MistralModel
        print("✅ Pydantic-AI with Mistral support")
        
        import langchain
        from langchain_openai import ChatOpenAI
        from langchain_community.vectorstores import SupabaseVectorStore
        print("✅ LangChain ecosystem")
        
        import anthropic
        import openai
        import groq
        import mistralai
        print("✅ AI provider clients")
        
        # Web framework
        import fastapi
        import uvicorn
        print("✅ FastAPI and Uvicorn")
        
        # Database and storage
        import supabase
        import psycopg2
        import pgvector
        import redis
        print("✅ Database packages")
        
        # Knowledge graph
        import graphiti_core
        import falkordb
        import neo4j
        print("✅ Knowledge graph packages")
        
        # Vector databases
        import chromadb
        import pinecone
        import weaviate
        import qdrant_client
        print("✅ Vector database clients")
        
        # Data processing
        import pandas
        import numpy
        print("✅ Data processing packages")
        
        # PDF processing
        import fitz  # PyMuPDF
        import pymupdf4llm
        print("✅ PDF processing packages")
        
        # Google services
        import google.generativeai
        from googleapiclient.discovery import build
        print("✅ Google API packages")
        
        # Testing and development
        import pytest
        import black
        import mypy
        print("✅ Development tools")
        
        print("\n🎉 All major packages imported successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_pydantic_ai_basic():
    """Test basic Pydantic-AI functionality."""
    print("\n🧪 Testing Pydantic-AI basic functionality...")
    
    try:
        from pydantic_ai import Agent
        from pydantic import BaseModel
        
        class Response(BaseModel):
            message: str
            
        # Create a simple agent (without API key for testing)
        agent = Agent(
            'test',  # placeholder model
            output_type=Response,
            system_prompt='You are a helpful assistant.'
        )
        
        print("✅ Pydantic-AI Agent created successfully")
        return True
        
    except Exception as e:
        print(f"❌ Pydantic-AI test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting installation verification tests...\n")
    
    tests_passed = 0
    total_tests = 2
    
    if test_imports():
        tests_passed += 1
    
    if test_pydantic_ai_basic():
        tests_passed += 1
    
    print(f"\n📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Your environment is ready to go!")
    else:
        print("⚠️  Some tests failed. Check the error messages above.")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    main()
