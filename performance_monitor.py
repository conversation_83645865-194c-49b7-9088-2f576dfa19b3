#!/usr/bin/env python3
"""
Performance Monitor for Email Processing System
Tracks API response times, payload sizes, and system performance
"""

import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any
import statistics

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.columns import Columns
from rich.text import Text
from rich.prompt import Prompt
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.layout import Layout
from rich.live import Live

from dotenv import load_dotenv
from supabase import create_client

# Load environment
load_dotenv()

console = Console()

class PerformanceMonitor:
    """Monitor system performance and API response times."""
    
    def __init__(self):
        """Initialize the performance monitor."""
        self.supabase = create_client(
            os.getenv("SUPABASE_URL"),
            os.getenv("SUPABASE_SERVICE_KEY")
        )
    
    def get_recent_performance_data(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get recent email processing performance data."""
        try:
            # Calculate time threshold
            time_threshold = (datetime.utcnow() - timedelta(hours=hours)).isoformat()
            
            # Get recent email interactions
            response = self.supabase.table("customer_interactions").select("*").eq(
                "interaction_type", "email"
            ).gte("created_at", time_threshold).order("created_at", desc=True).execute()
            
            return response.data or []
            
        except Exception as e:
            console.print(f"[red]Error fetching performance data: {e}[/red]")
            return []
    
    def analyze_performance(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze performance metrics from email data."""
        if not data:
            return {}
        
        # Extract metrics
        payload_sizes = []
        processing_times = []
        attachment_counts = []
        urgency_distribution = {"low": 0, "medium": 0, "high": 0, "urgent": 0}
        intent_distribution = {}
        
        for email in data:
            metadata = email.get('metadata', {})
            analysis = metadata.get('analysis', {})
            
            # Payload size (estimate from content length)
            content_length = len(email.get('content', ''))
            if content_length > 0:
                payload_sizes.append(content_length)
            
            # Attachment count
            attachments = metadata.get('attachments', [])
            attachment_counts.append(len(attachments))
            
            # Urgency distribution
            urgency = analysis.get('urgency', 'low').lower()
            if urgency in urgency_distribution:
                urgency_distribution[urgency] += 1
            
            # Intent distribution
            intent = analysis.get('intent', 'unknown')
            intent_distribution[intent] = intent_distribution.get(intent, 0) + 1
        
        # Calculate statistics
        stats = {
            "total_emails": len(data),
            "avg_payload_size": statistics.mean(payload_sizes) if payload_sizes else 0,
            "max_payload_size": max(payload_sizes) if payload_sizes else 0,
            "min_payload_size": min(payload_sizes) if payload_sizes else 0,
            "avg_attachments": statistics.mean(attachment_counts) if attachment_counts else 0,
            "urgency_distribution": urgency_distribution,
            "intent_distribution": intent_distribution,
            "emails_with_attachments": sum(1 for count in attachment_counts if count > 0),
            "attachment_rate": (sum(1 for count in attachment_counts if count > 0) / len(attachment_counts) * 100) if attachment_counts else 0
        }
        
        return stats
    
    def show_performance_dashboard(self, hours: int = 24):
        """Display performance dashboard."""
        console.clear()
        
        with console.status(f"[bold green]Loading performance data for last {hours} hours..."):
            data = self.get_recent_performance_data(hours)
            stats = self.analyze_performance(data)
        
        if not stats:
            console.print("[yellow]No performance data available[/yellow]")
            return
        
        # Create layout
        layout = Layout()
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="body"),
            Layout(name="footer", size=3)
        )
        
        # Header
        header_text = Text(f"📊 Performance Dashboard - Last {hours} Hours", style="bold blue")
        layout["header"].update(Panel(header_text, style="blue"))
        
        # Body - split into sections
        layout["body"].split_row(
            Layout(name="left"),
            Layout(name="right")
        )
        
        # Left panel - Email metrics
        email_table = Table(title="📧 Email Processing Metrics", show_header=False)
        email_table.add_column("Metric", style="cyan")
        email_table.add_column("Value", style="green")
        
        email_table.add_row("Total Emails Processed", str(stats["total_emails"]))
        email_table.add_row("Avg Payload Size", f"{stats['avg_payload_size']:.0f} chars")
        email_table.add_row("Max Payload Size", f"{stats['max_payload_size']:,} chars")
        email_table.add_row("Emails with Attachments", f"{stats['emails_with_attachments']} ({stats['attachment_rate']:.1f}%)")
        email_table.add_row("Avg Attachments per Email", f"{stats['avg_attachments']:.1f}")
        
        layout["left"].update(Panel(email_table, border_style="green"))
        
        # Right panel - Distribution metrics
        dist_table = Table(title="📈 Distribution Analysis", show_header=True)
        dist_table.add_column("Category", style="cyan")
        dist_table.add_column("Type", style="yellow")
        dist_table.add_column("Count", style="green")
        dist_table.add_column("Percentage", style="magenta")
        
        # Urgency distribution
        total_emails = stats["total_emails"]
        for urgency, count in stats["urgency_distribution"].items():
            percentage = (count / total_emails * 100) if total_emails > 0 else 0
            dist_table.add_row("Urgency", urgency.title(), str(count), f"{percentage:.1f}%")
        
        # Top intents
        sorted_intents = sorted(stats["intent_distribution"].items(), key=lambda x: x[1], reverse=True)[:5]
        for intent, count in sorted_intents:
            percentage = (count / total_emails * 100) if total_emails > 0 else 0
            dist_table.add_row("Intent", intent, str(count), f"{percentage:.1f}%")
        
        layout["right"].update(Panel(dist_table, border_style="yellow"))
        
        # Footer
        footer_text = Text("Press Enter to continue...", style="dim")
        layout["footer"].update(Panel(footer_text, style="dim"))
        
        console.print(layout)
        input()
    
    def show_payload_analysis(self):
        """Show detailed payload size analysis."""
        console.clear()
        
        with console.status("[bold green]Analyzing payload sizes..."):
            data = self.get_recent_performance_data(24)
        
        if not data:
            console.print("[yellow]No data available for analysis[/yellow]")
            return
        
        # Analyze payload sizes
        payload_data = []
        for email in data:
            content_length = len(email.get('content', ''))
            metadata = email.get('metadata', {})
            attachments = metadata.get('attachments', [])
            
            payload_data.append({
                "email_id": metadata.get('email_id', 'unknown'),
                "from": email.get('customer_email', 'unknown'),
                "size": content_length,
                "attachments": len(attachments),
                "created_at": email.get('created_at', '')
            })
        
        # Sort by size
        payload_data.sort(key=lambda x: x['size'], reverse=True)
        
        # Create table
        table = Table(title="📊 Payload Size Analysis (Top 20)", show_header=True, header_style="bold magenta")
        table.add_column("Rank", style="dim", width=5)
        table.add_column("From", style="cyan", width=30)
        table.add_column("Size (chars)", style="green", width=12)
        table.add_column("Attachments", style="yellow", width=12)
        table.add_column("Time", style="dim", width=12)
        
        for i, email in enumerate(payload_data[:20]):
            time_str = email['created_at'][:10] if email['created_at'] else 'unknown'
            
            # Color code size
            size = email['size']
            if size > 10000:
                size_str = f"[red]{size:,}[/red]"
            elif size > 5000:
                size_str = f"[yellow]{size:,}[/yellow]"
            else:
                size_str = f"[green]{size:,}[/green]"
            
            table.add_row(
                str(i + 1),
                email['from'][:27] + "..." if len(email['from']) > 30 else email['from'],
                size_str,
                str(email['attachments']),
                time_str
            )
        
        console.print(table)
        
        # Show statistics
        sizes = [email['size'] for email in payload_data]
        if sizes:
            stats_panel = Panel(
                f"📈 Statistics:\n"
                f"Average: {statistics.mean(sizes):.0f} chars\n"
                f"Median: {statistics.median(sizes):.0f} chars\n"
                f"Max: {max(sizes):,} chars\n"
                f"Min: {min(sizes):,} chars\n"
                f"Std Dev: {statistics.stdev(sizes):.0f} chars" if len(sizes) > 1 else "Std Dev: N/A",
                title="📊 Payload Statistics",
                border_style="blue"
            )
            console.print(stats_panel)
        
        input("\nPress Enter to continue...")

def main():
    """Main performance monitoring interface."""
    monitor = PerformanceMonitor()
    
    while True:
        console.clear()
        console.print(Panel.fit("📊 Performance Monitor", style="bold blue"))
        console.print()
        
        # Show menu
        menu_table = Table(show_header=False, box=None)
        menu_table.add_column("Option", style="cyan")
        menu_table.add_column("Description", style="white")
        
        menu_table.add_row("1", "📊 Performance Dashboard (24h)")
        menu_table.add_row("2", "📈 Performance Dashboard (1h)")
        menu_table.add_row("3", "📋 Payload Size Analysis")
        menu_table.add_row("4", "🔄 Refresh")
        menu_table.add_row("q", "❌ Quit")
        
        console.print(menu_table)
        console.print()
        
        choice = Prompt.ask("Select option", choices=["1", "2", "3", "4", "q"], default="1")
        
        if choice == "1":
            monitor.show_performance_dashboard(24)
        elif choice == "2":
            monitor.show_performance_dashboard(1)
        elif choice == "3":
            monitor.show_payload_analysis()
        elif choice == "4":
            continue
        elif choice == "q":
            console.print("[yellow]Goodbye![/yellow]")
            break

if __name__ == "__main__":
    main()
